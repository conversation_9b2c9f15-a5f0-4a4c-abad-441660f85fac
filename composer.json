{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/react-starter-kit", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "dompdf/dompdf": "^3.1", "inertiajs/inertia-laravel": "^2.0", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.10.1", "nativephp/electron": "^1.0", "spatie/laravel-permission": "^6.17", "stancl/tenancy": "^3.9", "tightenco/ziggy": "^2.4"}, "require-dev": {"fakerphp/faker": "^1.23", "larastan/larastan": "^3.0", "laravel/pail": "^1.2.2", "laravel/pint": "^1.18", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "peckphp/peck": "^0.1.3", "pestphp/pest": "^3.8", "pestphp/pest-plugin-drift": "^3.0", "pestphp/pest-plugin-laravel": "^3.1", "pestphp/pest-plugin-type-coverage": "^3.5", "rector/rector": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"npm run dev\" --names='server,queue,vite'"], "dev:ssr": ["npm run build:ssr", "Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"php artisan inertia:start-ssr\" --names=server,queue,logs,ssr"], "test": ["@test:type-coverage", "@test:typos", "@test:unit", "@test:lint", "@test:types", "@test:refactor"], "native:dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -k -c \"#93c5fd,#c4b5fd\" \"php artisan native:serve --no-interaction\" \"npm run dev\" --names=app,vite"], "lint": ["pint", "npm run lint"], "refactor": "rector", "test:type-coverage": "pest --type-coverage --min=100", "test:typos": "peck", "test:lint": ["pint --test", "npm run test:lint"], "test:unit": "pest --parallel --coverage --exactly=100.0", "test:types": ["phpstan", "npm run test:types"], "test:refactor": "rector --dry-run"}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}