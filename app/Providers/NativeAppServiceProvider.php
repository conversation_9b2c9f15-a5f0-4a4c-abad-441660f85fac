<?php

declare(strict_types=1);

namespace App\Providers;

use Native\Laravel\Contracts\ProvidesPhpIni;
use Native\Laravel\Facades\Menu;
use Native\Laravel\Facades\MenuBar;
use Native\Laravel\Facades\Window;

final class NativeAppServiceProvider implements ProvidesPhpIni
{
    /**
     * Executed once the native application has been booted.
     * Use this method to open windows, register global shortcuts, etc.
     */
    public function boot(): void
    {
        Window::open()
            ->maximized()
            ->minWidth(1024)
            ->minHeight(768)
            ->showDevTools(false);

        // Try icon context menu
        MenuBar::create()
            ->withContextMenu(
                Menu::make(
                    Menu::label('Products'),
                    Menu::label('Invoices'),
                    Menu::separator(),
                    Menu::link('/login', 'Login'),
                    Menu::link('/register', 'Register'),
                    Menu::separator(),
                    Menu::link('https://astridtechnology.com/contact-us', 'Contact Astrid Team')
                        ->openInBrowser(),
                    Menu::separator(),
                    Menu::quit()
                )
            )->onlyShowContextMenu();
    }

    /**
     * Return an array of php.ini directives to be set.
     */
    public function phpIni(): array
    {
        return [
            'memory_limit'       => '512M',
            'display_errors'     => '1',
            'error_reporting'    => 'E_ALL',
            'max_execution_time' => '0',
            'max_input_time'     => '0',
        ];
    }
}
