<?php

declare(strict_types=1);

namespace App\Helpers;

class CurrencyHelper
{
    /**
     * Format amount in Indian Rupees
     */
    public static function formatINR(float|int|string $amount, bool $showDecimals = true): string
    {
        $amount = (float) $amount;
        
        if ($showDecimals) {
            return '₹' . number_format($amount, 2, '.', ',');
        }
        
        return '₹' . number_format($amount, 0, '.', ',');
    }

    /**
     * Format amount in Indian Rupees with Indian number system (lakhs, crores)
     */
    public static function formatINRIndian(float|int|string $amount, bool $showDecimals = true): string
    {
        $amount = (float) $amount;
        
        if ($amount >= 10000000) { // 1 crore
            $crores = $amount / 10000000;
            return '₹' . number_format($crores, $showDecimals ? 2 : 0) . ' Cr';
        } elseif ($amount >= 100000) { // 1 lakh
            $lakhs = $amount / 100000;
            return '₹' . number_format($lakhs, $showDecimals ? 2 : 0) . ' L';
        } elseif ($amount >= 1000) { // 1 thousand
            $thousands = $amount / 1000;
            return '₹' . number_format($thousands, $showDecimals ? 1 : 0) . 'K';
        }
        
        return self::formatINR($amount, $showDecimals);
    }

    /**
     * Get currency symbol
     */
    public static function getSymbol(): string
    {
        return '₹';
    }

    /**
     * Get currency code
     */
    public static function getCode(): string
    {
        return 'INR';
    }

    /**
     * Get currency name
     */
    public static function getName(): string
    {
        return 'Indian Rupee';
    }
}
