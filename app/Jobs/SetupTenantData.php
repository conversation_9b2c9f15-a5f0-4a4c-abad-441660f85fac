<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Models\Branch;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Stancl\Tenancy\Contracts\Tenant;

class SetupTenantData implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Tenant $tenant
    ) {}

    /**
     * Execute the job.
     * This runs in the central database context for single-database tenancy.
     */
    public function handle(): void
    {
        // Find the vendor user who owns this tenant
        $vendorUser = User::where('tenant_id', $this->tenant->id)
            ->whereHas('roles', function ($q) {
                $q->where('name', 'vendor');
            })
            ->first();

        if ($vendorUser) {
            // Create a default branch for the vendor
            $branch = Branch::create([
                'user_id' => $vendorUser->id,
                'name' => 'Main Branch',
                'address' => 'Main Location',
                'phone' => '************',
                'is_active' => true,
            ]);

            // Set the vendor's current branch
            $vendorUser->update(['current_branch_id' => $branch->id]);
        }
    }
}
