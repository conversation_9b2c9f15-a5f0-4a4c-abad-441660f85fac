<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Scopes\TenantScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

final class User extends Authenticatable
{
    use HasApiTokens,HasFactory, HasRoles, Notifiable;

    protected $fillable = [
        'name',
        'company_name',
        'company_domain',
        'tenant_id',
        'branch_id',
        'current_branch_id',
        'email',
        'password',
        'phone',
        'gender',
        'address',
        'is_active',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password'          => 'hashed',
        'is_active'         => 'boolean',
    ];

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'current_branch_id');
    }

    public function managedBranches(): HasMany
    {
        return $this->hasMany(Branch::class, 'user_id');
    }

    public function seats(): HasMany
    {
        return $this->hasMany(Seat::class, 'staff_id');
    }

    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class);
    }

    public function isStaff(): bool
    {
        return $this->hasRole('staff');
    }

    protected static function booted(): void
    {
        static::addGlobalScope(new TenantScope);
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function vendorSubscriptions(): HasMany
    {
        return $this->hasMany(VendorSubscription::class);
    }

    public function activeSubscription(): HasMany
    {
        return $this->hasMany(VendorSubscription::class)->where('status', 'active');
    }

    public function getCurrentSubscription(): ?VendorSubscription
    {
        return $this->vendorSubscriptions()
            ->where('status', 'active')
            ->where('ends_at', '>', now())
            ->first();
    }

    public function hasActiveSubscription(): bool
    {
        return $this->getCurrentSubscription() !== null;
    }

    public function isSubscriptionExpired(): bool
    {
        $subscription = $this->getCurrentSubscription();
        return $subscription ? $subscription->isExpired() : true;
    }

    public function canPerformAction(string $action): bool
    {
        if (!$this->hasRole('vendor')) {
            return true; // Non-vendors are not limited
        }

        $subscription = $this->getCurrentSubscription();
        if (!$subscription) {
            return false; // No active subscription
        }

        return match($action) {
            'add_service' => $subscription->canAddService(),
            'add_appointment' => $subscription->canAddAppointment(),
            'add_seat' => $subscription->canAddSeat(),
            'add_branch' => $subscription->canAddBranch(),
            'add_staff' => $subscription->canAddStaff(),
            default => true,
        };
    }
}
