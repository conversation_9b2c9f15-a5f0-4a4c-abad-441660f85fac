<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class WorkingHour extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'day',
        'open_time',
        'close_time',
        'is_closed',
        'day_order',
    ];

    protected $casts = [
        'is_closed' => 'boolean',
    ];

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function scopeOpen($query)
    {
        return $query->where('is_closed', false);
    }
}
