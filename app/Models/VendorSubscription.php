<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

final class VendorSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'subscription_plan_id',
        'starts_at',
        'ends_at',
        'trial_ends_at',
        'status',
        'current_services_count',
        'current_appointments_count',
        'current_seats_count',
        'current_branches_count',
        'current_staff_count',
        'billing_period_start',
        'billing_period_end',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'billing_period_start' => 'date',
        'billing_period_end' => 'date',
        'current_services_count' => 'integer',
        'current_appointments_count' => 'integer',
        'current_seats_count' => 'integer',
        'current_branches_count' => 'integer',
        'current_staff_count' => 'integer',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired')
                    ->orWhere('ends_at', '<', now());
    }

    public function isActive(): bool
    {
        return $this->status === 'active' && $this->ends_at > now();
    }

    public function isExpired(): bool
    {
        return $this->ends_at < now() || $this->status === 'expired';
    }

    public function isOnTrial(): bool
    {
        return $this->status === 'trial' && 
               $this->trial_ends_at && 
               $this->trial_ends_at > now();
    }

    public function daysUntilExpiry(): int
    {
        return max(0, now()->diffInDays($this->ends_at, false));
    }

    public function daysUntilTrialExpiry(): int
    {
        if (!$this->trial_ends_at) {
            return 0;
        }
        return max(0, now()->diffInDays($this->trial_ends_at, false));
    }

    // Limit checking methods
    public function canAddService(): bool
    {
        if ($this->subscriptionPlan->isUnlimited('max_services')) {
            return true;
        }
        return $this->current_services_count < $this->subscriptionPlan->max_services;
    }

    public function canAddAppointment(): bool
    {
        if ($this->subscriptionPlan->isUnlimited('max_appointments_per_month')) {
            return true;
        }
        return $this->current_appointments_count < $this->subscriptionPlan->max_appointments_per_month;
    }

    public function canAddSeat(): bool
    {
        if ($this->subscriptionPlan->isUnlimited('max_seats')) {
            return true;
        }
        return $this->current_seats_count < $this->subscriptionPlan->max_seats;
    }

    public function canAddBranch(): bool
    {
        return $this->current_branches_count < $this->subscriptionPlan->max_branches;
    }

    public function canAddStaff(): bool
    {
        if ($this->subscriptionPlan->isUnlimited('max_staff')) {
            return true;
        }
        return $this->current_staff_count < $this->subscriptionPlan->max_staff;
    }

    // Usage update methods
    public function updateUsageCounts(): void
    {
        $vendor = $this->user;
        
        // Count services across all vendor's branches
        $this->current_services_count = Service::whereHas('branch', function ($query) use ($vendor) {
            $query->where('user_id', $vendor->id);
        })->count();
        
        // Count appointments in current billing period
        $this->current_appointments_count = Appointment::whereHas('branch', function ($query) use ($vendor) {
            $query->where('user_id', $vendor->id);
        })->whereBetween('appointment_date', [
            $this->billing_period_start,
            $this->billing_period_end
        ])->count();
        
        // Count seats across all vendor's branches
        $this->current_seats_count = Seat::whereHas('branch', function ($query) use ($vendor) {
            $query->where('user_id', $vendor->id);
        })->count();
        
        // Count branches
        $this->current_branches_count = Branch::where('user_id', $vendor->id)->count();
        
        // Count staff across all vendor's branches
        $this->current_staff_count = User::where('branch_id', '!=', null)
            ->whereHas('branch', function ($query) use ($vendor) {
                $query->where('user_id', $vendor->id);
            })
            ->whereHas('roles', function ($query) {
                $query->where('name', 'staff');
            })->count();
        
        $this->save();
    }

    public function resetBillingPeriod(): void
    {
        $now = now();
        $this->billing_period_start = $now->startOfMonth()->toDateString();
        $this->billing_period_end = $now->endOfMonth()->toDateString();
        
        // Reset monthly counters
        $this->current_appointments_count = 0;
        
        $this->save();
    }
}
