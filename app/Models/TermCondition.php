<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Scopes\TenantScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

final class TermCondition extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'terms_conditions';

    protected $fillable = [
        'user_id',
        'branch_id',
        'title',
        'condition',
        'is_default',
        'is_active',
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'is_active'  => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
                ->orWhere('condition', 'like', "%{$search}%");
        });
    }

    public function scopeSort($query, $field, $direction)
    {
        return $query->orderBy($field, $direction);
    }

    protected static function booted(): void
    {
        static::addGlobalScope(new TenantScope);
    }
}
