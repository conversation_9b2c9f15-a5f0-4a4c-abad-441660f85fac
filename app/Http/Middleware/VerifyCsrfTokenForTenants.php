<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;

class VerifyCsrfTokenForTenants extends VerifyCsrfToken
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        //
    ];

    /**
     * Determine if the session and input CSRF tokens match.
     */
    protected function tokensMatch($request): bool
    {
        // For tenant domains, we need to be more flexible with CSRF tokens
        // since they might be coming from different subdomains

        $token = $this->getTokenFromRequest($request);

        if (!$token) {
            return false;
        }

        // Get the session token
        $sessionToken = $request->session()->token();

        if (!$sessionToken) {
            return false;
        }

        // Check if tokens match
        if (hash_equals($sessionToken, $token)) {
            return true;
        }

        // For tenant domains, also check if we're in a tenant context
        // and allow the request if it's a valid tenant
        if (app()->bound('tenant') && tenant()) {
            // Additional validation can be added here if needed
            return hash_equals($sessionToken, $token);
        }

        return false;
    }
}
