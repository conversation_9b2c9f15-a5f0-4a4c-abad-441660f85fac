<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscriptionLimits
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $action): Response
    {
        $user = auth()->user();

        // Skip check for non-vendors
        if (!$user || !$user->hasRole('vendor')) {
            return $next($request);
        }

        // Check if user can perform the action
        if (!$user->canPerformAction($action)) {
            $subscription = $user->getCurrentSubscription();

            if (!$subscription) {
                return redirect()
                    ->back()
                    ->with('error', 'You need an active subscription to perform this action.');
            }

            $limitMessage = $this->getLimitMessage($action, $subscription);

            return redirect()
                ->back()
                ->with('error', $limitMessage);
        }

        return $next($request);
    }

    private function getLimitMessage(string $action, $subscription): string
    {
        $plan = $subscription->subscriptionPlan;

        return match($action) {
            'add_service' => $plan->isUnlimited('max_services')
                ? 'Service limit reached. Please upgrade your plan to add more services.'
                : "You have reached your service limit of {$plan->max_services}. Upgrade your plan to add more services.",

            'add_appointment' => $plan->isUnlimited('max_appointments_per_month')
                ? 'Monthly appointment limit reached. Please upgrade your plan for unlimited appointments.'
                : "You have reached your monthly appointment limit of {$plan->max_appointments_per_month}. Upgrade your plan or wait for next month.",

            'add_seat' => $plan->isUnlimited('max_seats')
                ? 'Seat limit reached. Please upgrade your plan to add more seats.'
                : "You have reached your seat limit of {$plan->max_seats}. Upgrade your plan to add more seats.",

            'add_branch' => "You have reached your branch limit of {$plan->max_branches}. Upgrade your plan to add more branches.",

            'add_staff' => $plan->isUnlimited('max_staff')
                ? 'Staff limit reached. Please upgrade your plan to add more staff members.'
                : "You have reached your staff limit of {$plan->max_staff}. Upgrade your plan to add more staff members.",

            default => 'You have reached your subscription limit for this action.',
        };
    }
}
