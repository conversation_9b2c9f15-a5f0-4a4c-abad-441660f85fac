<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserBelongsToTenant
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        $currentTenant = tenant();

        // Only check tenant access if we're in a tenant context
        if ($currentTenant && $user && $user->tenant_id !== $currentTenant->id) {

            Auth::logout();
            return redirect()->route('login')->with('error', 'You do not have access to this tenant.');

        }

        return $next($request);
    }
}
