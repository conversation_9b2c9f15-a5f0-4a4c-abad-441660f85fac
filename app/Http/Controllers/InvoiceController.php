<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\InvoiceTermCondition;
use App\Models\Product;
use App\Models\TermCondition;
use App\Models\Vendor;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

final class InvoiceController extends Controller
{
    public function index(Request $request)
    {
        $companyId = auth()->user()->current_company_id;
        $query     = Invoice::where('company_id', $companyId);

        // Apply search filter
        if ($request->has('search') && $request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('invoice_number', 'like', "%{$request->search}%")
                    ->orWhere('vendor_company_name', 'like', "%{$request->search}%")
                    ->orWhere('vendor_contact_person_name', 'like', "%{$request->search}%");
            });
        }

        // Apply vendor filter
        if ($request->has('vendor_id') && $request->vendor_id) {
            $query->where('vendor_id', $request->vendor_id);
        }

        // Apply invoice type filter
        if ($request->has('invoice_type') && $request->invoice_type) {
            $query->where('invoice_type', $request->invoice_type);
        }

        // Apply payment method filter
        if ($request->has('payment_method') && $request->payment_method) {
            $query->where('payment_method', $request->payment_method);
        }

        // Apply payment status filter
        if ($request->has('payment_status') && $request->payment_status) {
            $query->where('payment_status', $request->payment_status);
        }

        // Apply date range filter
        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('invoice_date', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('invoice_date', '<=', $request->date_to);
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'invoice_date';
        $sortDirection = $request->direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $invoices = $query->paginate(10)
            ->withQueryString();

        // Get vendors for filtering
        $vendors = Vendor::where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('company_name')
            ->get(['id', 'company_name']);

        return Inertia::render('invoices/index', [
            'invoices' => $invoices,
            'vendors'  => $vendors,
            'filters'  => $request->only(['search', 'vendor_id', 'invoice_type', 'payment_method', 'payment_status', 'date_from', 'date_to', 'sort', 'direction']),
        ]);
    }

    public function create()
    {
        $companyId = auth()->user()->current_company_id;

        $vendors = Vendor::where('company_id', $companyId)
            ->where('is_active', true)
            ->with(['country', 'state', 'city'])
            ->orderBy('company_name')
            ->get();

        $products = Product::where('company_id', $companyId)
            ->where('is_active', true)
            ->with('unit:id,name')
            ->orderBy('name')
            ->get(['id', 'name', 'sku', 'price', 'unit_id']);

        // Get term conditions for both purchase and sales
        $termConditions = TermCondition::where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('title')
            ->get();

        // Generate next invoice number
        $lastInvoice = Invoice::where('company_id', $companyId)
            ->orderBy('id', 'desc')
            ->first();

        $nextInvoiceNumber = 'INV-'.date('Ymd').'-001';

        if ($lastInvoice) {
            $lastNumber = $lastInvoice->invoice_number;
            $parts      = explode('-', $lastNumber);
            if (count($parts) === 3 && $parts[1] === date('Ymd')) {
                $sequence          = (int) $parts[2] + 1;
                $nextInvoiceNumber = 'INV-'.date('Ymd').'-'.mb_str_pad((string) $sequence, 3, '0', STR_PAD_LEFT);
            }
        }

        return Inertia::render('invoices/create', [
            'vendors'           => $vendors,
            'products'          => $products,
            'termConditions'    => $termConditions,
            'nextInvoiceNumber' => $nextInvoiceNumber,
        ]);
    }

    public function store(Request $request)
    {
        $companyId = auth()->user()->current_company_id;
        $userId    = auth()->id();

        $validated = $request->validate([
            'invoice_number'        => ['required', 'string', 'max:50', 'unique:invoices,invoice_number'],
            'invoice_date'          => ['required', 'date'],
            'invoice_type'          => ['required', 'string', 'in:purchase,sales'],
            'vendor_id'             => ['required', 'exists:vendors,id'],
            'items'                 => ['required', 'array', 'min:1'],
            'items.*.product_id'    => ['required', 'exists:products,id'],
            'items.*.quantity'      => ['required', 'integer', 'min:1'],
            'items.*.price'         => ['required', 'numeric', 'min:0'],
            'subtotal'              => ['required', 'numeric', 'min:0'],
            'is_gst_applicable'     => ['required', 'boolean'],
            'gst_applicable_amount' => ['nullable', 'numeric', 'min:0'],
            'gst_amount'            => ['required', 'numeric', 'min:0'],
            'discount'              => ['required', 'numeric', 'min:0'],
            'without_gst_amount'    => ['required', 'numeric', 'min:0'],
            'with_gst_amount'       => ['required', 'numeric', 'min:0'],
            'payment_method'        => ['required', 'string', 'in:cash,cheque,bank_transfer'],
            'payment_status'        => ['required', 'string', 'in:pending,paid'],
            'grand_total'           => ['required', 'numeric', 'min:0'],
            'remarks'               => ['nullable', 'string'],
            'term_conditions'       => ['nullable', 'array'],
            'term_conditions.*'     => ['exists:terms_conditions,id'],
        ]);

        try {
            DB::beginTransaction();

            // Get vendor details
            $vendor = Vendor::with(['country', 'state', 'city'])
                ->findOrFail($validated['vendor_id']);

            // Create invoice
            $invoice = Invoice::create([
                'company_id'                   => $companyId,
                'user_id'                      => $userId,
                'vendor_id'                    => $vendor->id,
                'vendor_company_name'          => $vendor->company_name,
                'vendor_party_code'            => $vendor->party_code,
                'vendor_contact_person_name'   => $vendor->contact_person_name,
                'vendor_contact_person_number' => $vendor->contact_person_number,
                'vendor_email'                 => $vendor->email,
                'vendor_gstin'                 => $vendor->gstin,
                'vendor_country_id'            => $vendor->country_id,
                'vendor_state_id'              => $vendor->state_id,
                'vendor_city_id'               => $vendor->city_id,
                'vendor_pincode'               => $vendor->pincode,
                'vendor_address'               => $vendor->address,
                'vendor_pan_number'            => $vendor->pan_number,
                'invoice_number'               => $validated['invoice_number'],
                'invoice_date'                 => $validated['invoice_date'],
                'invoice_type'                 => $validated['invoice_type'],
                'subtotal'                     => $validated['subtotal'],
                'is_gst_applicable'            => $validated['is_gst_applicable'],
                'gst_applicable_amount'        => $validated['gst_applicable_amount'] ?? 0,
                'gst_amount'                   => $validated['gst_amount'],
                'discount'                     => $validated['discount'],
                'without_gst_amount'           => $validated['without_gst_amount'],
                'with_gst_amount'              => $validated['with_gst_amount'],
                'payment_method'               => $validated['payment_method'],
                'payment_status'               => $validated['payment_status'],
                'grand_total'                  => $validated['grand_total'],
                'remarks'                      => $validated['remarks'],
            ]);

            // Create invoice items
            foreach ($validated['items'] as $item) {
                $invoice->items()->create([
                    'product_id' => $item['product_id'],
                    'quantity'   => $item['quantity'],
                    'price'      => $item['price'],
                    'total'      => $item['quantity'] * $item['price'],
                ]);
            }

            // Create invoice term conditions
            if (! empty($validated['term_conditions'])) {
                foreach ($validated['term_conditions'] as $termConditionId) {
                    $termCondition = TermCondition::find($termConditionId);
                    if ($termCondition && $termCondition->type === $validated['invoice_type']) {
                        InvoiceTermCondition::create([
                            'invoice_id'        => $invoice->id,
                            'term_condition_id' => $termConditionId,
                            'title'             => $termCondition->title,
                            'condition'         => $termCondition->condition,
                            'type'              => $termCondition->type,
                        ]);
                    }
                }
            }

            DB::commit();

            return redirect()
                ->route('invoices.index')
                ->with('success', 'Invoice created successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to create invoice: '.$e->getMessage());
        }
    }

    public function edit(Invoice $invoice)
    {
        $companyId = auth()->user()->current_company_id;

        // Ensure the invoice belongs to the current company
        if ($invoice->company_id !== $companyId) {
            abort(403);
        }

        // Make sure to eager load the relationships
        $invoice->load(['items.product', 'termConditions']);

        // Ensure termConditions is always an array, even if empty
        if (! $invoice->termConditions) {
            $invoice->termConditions = [];
        }
        // Make sure the property name is consistent
        $invoiceData = $invoice->toArray();

        // Ensure the property name is 'termConditions' for consistency with the frontend
        if (! isset($invoiceData['termConditions']) && isset($invoiceData['term_conditions'])) {
            $invoiceData['termConditions'] = $invoiceData['term_conditions'];
        }

        $vendors = Vendor::where('company_id', $companyId)
            ->where('is_active', true)
            ->with(['country', 'state', 'city'])
            ->orderBy('company_name')
            ->get();

        $products = Product::where('company_id', $companyId)
            ->where('is_active', true)
            ->with('unit:id,name')
            ->orderBy('name')
            ->get(['id', 'name', 'sku', 'price', 'unit_id']);

        // Get term conditions for both purchase and sales
        $termConditions = TermCondition::where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('title')
            ->get();

        return Inertia::render('invoices/edit', [
            'invoice'        => $invoiceData,
            'vendors'        => $vendors,
            'products'       => $products,
            'termConditions' => $termConditions,
        ]);
    }

    public function update(Request $request, Invoice $invoice)
    {
        $companyId = auth()->user()->current_company_id;

        // Ensure the invoice belongs to the current company
        if ($invoice->company_id !== $companyId) {
            abort(403);
        }

        $validated = $request->validate([
            'invoice_number'        => ['required', 'string', 'max:50', 'unique:invoices,invoice_number,'.$invoice->id],
            'invoice_date'          => ['required', 'date'],
            'invoice_type'          => ['required', 'string', 'in:purchase,sales'],
            'vendor_id'             => ['required', 'exists:vendors,id'],
            'items'                 => ['required', 'array', 'min:1'],
            'items.*.id'            => ['nullable', 'exists:invoice_items,id'],
            'items.*.product_id'    => ['required', 'exists:products,id'],
            'items.*.quantity'      => ['required', 'integer', 'min:1'],
            'items.*.price'         => ['required', 'numeric', 'min:0'],
            'subtotal'              => ['required', 'numeric', 'min:0'],
            'is_gst_applicable'     => ['required', 'boolean'],
            'gst_applicable_amount' => ['nullable', 'numeric', 'min:0'],
            'gst_amount'            => ['required', 'numeric', 'min:0'],
            'discount'              => ['required', 'numeric', 'min:0'],
            'without_gst_amount'    => ['required', 'numeric', 'min:0'],
            'with_gst_amount'       => ['required', 'numeric', 'min:0'],
            'payment_method'        => ['required', 'string', 'in:cash,cheque,bank_transfer'],
            'payment_status'        => ['required', 'string', 'in:pending,paid'],
            'grand_total'           => ['required', 'numeric', 'min:0'],
            'remarks'               => ['nullable', 'string'],
            'term_conditions'       => ['nullable', 'array'],
            'term_conditions.*'     => ['exists:terms_conditions,id'],
        ]);

        try {
            DB::beginTransaction();

            // Get vendor details
            $vendor = Vendor::with(['country', 'state', 'city'])
                ->findOrFail($validated['vendor_id']);

            // Update invoice
            $invoice->update([
                'invoice_number'               => $validated['invoice_number'],
                'invoice_date'                 => $validated['invoice_date'],
                'invoice_type'                 => $validated['invoice_type'],
                'vendor_id'                    => $validated['vendor_id'],
                'vendor_company_name'          => $vendor->company_name,
                'vendor_party_code'            => $vendor->party_code,
                'vendor_contact_person_name'   => $vendor->contact_person_name,
                'vendor_contact_person_number' => $vendor->contact_person_number,
                'vendor_email'                 => $vendor->email,
                'vendor_gstin'                 => $vendor->gstin,
                'vendor_country_id'            => $vendor->country_id,
                'vendor_state_id'              => $vendor->state_id,
                'vendor_city_id'               => $vendor->city_id,
                'vendor_pincode'               => $vendor->pincode,
                'vendor_address'               => $vendor->address,
                'vendor_pan_number'            => $vendor->pan_number,
                'subtotal'                     => $validated['subtotal'],
                'is_gst_applicable'            => $validated['is_gst_applicable'],
                'gst_applicable_amount'        => $validated['gst_applicable_amount'] ?? 0,
                'gst_amount'                   => $validated['gst_amount'],
                'discount'                     => $validated['discount'],
                'without_gst_amount'           => $validated['without_gst_amount'],
                'with_gst_amount'              => $validated['with_gst_amount'],
                'payment_method'               => $validated['payment_method'],
                'payment_status'               => $validated['payment_status'],
                'grand_total'                  => $validated['grand_total'],
                'remarks'                      => $validated['remarks'],
            ]);

            // Update invoice items
            // First, get existing item IDs
            $existingItemIds = $invoice->items->pluck('id')->toArray();
            $updatedItemIds  = [];

            foreach ($validated['items'] as $itemData) {
                if (isset($itemData['id'])) {
                    // Update existing item
                    $item = InvoiceItem::find($itemData['id']);
                    if ($item && $item->invoice_id === $invoice->id) {
                        $item->update([
                            'product_id' => $itemData['product_id'],
                            'quantity'   => $itemData['quantity'],
                            'price'      => $itemData['price'],
                            'total'      => $itemData['quantity'] * $itemData['price'],
                        ]);
                        $updatedItemIds[] = $item->id;
                    }
                } else {
                    // Create new item
                    $item = InvoiceItem::create([
                        'invoice_id' => $invoice->id,
                        'product_id' => $itemData['product_id'],
                        'quantity'   => $itemData['quantity'],
                        'price'      => $itemData['price'],
                        'total'      => $itemData['quantity'] * $itemData['price'],
                    ]);
                    $updatedItemIds[] = $item->id;
                }
            }

            // Delete items that were not updated
            $itemsToDelete = array_diff($existingItemIds, $updatedItemIds);
            if (! empty($itemsToDelete)) {
                InvoiceItem::whereIn('id', $itemsToDelete)->forceDelete();
            }

            // Update term conditions - preserve existing ones when possible
            if (! empty($validated['term_conditions'])) {
                // Get existing term condition records for this invoice
                $existingTermConditions   = $invoice->termConditions()->get();
                $existingTermConditionIds = $existingTermConditions->pluck('term_condition_id')->toArray();

                // Track which term conditions we've processed
                $processedIds = [];

                foreach ($validated['term_conditions'] as $termConditionId) {
                    $termCondition = TermCondition::find($termConditionId);

                    // Only process if term condition exists and matches invoice type
                    if ($termCondition && $termCondition->type === $validated['invoice_type']) {
                        // Check if this term condition already exists for this invoice
                        $existingRecord = $existingTermConditions->first(function ($item) use ($termConditionId) {
                            return $item->term_condition_id === $termConditionId;
                        });

                        if ($existingRecord) {
                            // Update existing record
                            $existingRecord->update([
                                'title'     => $termCondition->title,
                                'condition' => $termCondition->condition,
                                'type'      => $termCondition->type,
                            ]);
                        } else {
                            // Create new record
                            InvoiceTermCondition::create([
                                'invoice_id'        => $invoice->id,
                                'term_condition_id' => $termConditionId,
                                'title'             => $termCondition->title,
                                'condition'         => $termCondition->condition,
                                'type'              => $termCondition->type,
                            ]);
                        }

                        $processedIds[] = $termConditionId;
                    }
                }

                // Delete term conditions that were not in the request
                $idsToDelete = array_diff($existingTermConditionIds, $processedIds);
                if (! empty($idsToDelete)) {
                    $invoice->termConditions()
                        ->whereIn('term_condition_id', $idsToDelete)
                        ->forceDelete();
                }
            } else {
                // If no term conditions were provided, delete all existing ones
                $invoice->termConditions()->forceDelete();
            }

            DB::commit();

            return redirect()
                ->route('invoices.index')
                ->with('success', 'Invoice updated successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to update invoice: '.$e->getMessage());
        }
    }

    public function destroy(Invoice $invoice)
    {
        $companyId = auth()->user()->current_company_id;

        // Ensure the invoice belongs to the current company
        if ($invoice->company_id !== $companyId) {
            abort(403);
        }

        try {
            $invoice->delete();

            return redirect()
                ->route('invoices.index')
                ->with('success', 'Invoice deleted successfully');
        } catch (Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to delete invoice: '.$e->getMessage());
        }
    }

    public function print(Invoice $invoice)
    {
        $companyId = auth()->user()->current_company_id;

        // Ensure the invoice belongs to the current company
        if ($invoice->company_id !== $companyId) {
            abort(403);
        }

        $invoice->load(['items.product.unit', 'company', 'vendorCountry', 'vendorState', 'vendorCity', 'termConditions']);

        return Inertia::render('invoices/print', [
            'invoice' => $invoice,
        ]);
    }

    public function updateStatus(Request $request, Invoice $invoice)
    {
        // Ensure the invoice belongs to the current company
        if ($invoice->company_id !== auth()->user()->current_company_id) {
            abort(403);
        }

        $validated = $request->validate([
            'payment_status' => ['required', 'string', 'in:paid,pending'],
            'payment_method' => ['required', 'string', 'in:cash,cheque,bank_transfer'],
        ]);

        $invoice->update([
            'payment_status' => $validated['payment_status'],
            'payment_method' => $validated['payment_method'],
        ]);

        return redirect()->back()->with('success', 'Invoice payment status updated successfully');
    }

    public function downloadPdf(Invoice $invoice)
    {
        $companyId = auth()->user()->current_company_id;

        // Ensure the invoice belongs to the current company
        if ($invoice->company_id !== $companyId) {
            abort(403);
        }

        $invoice->load(['items.product.unit', 'company', 'vendorCountry', 'vendorState', 'vendorCity', 'termConditions']);

        // Generate PDF using DomPDF with optimized settings
        $pdf     = new \Dompdf\Dompdf();
        $options = new \Dompdf\Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $options->set('isFontSubsettingEnabled', true);
        $options->set('defaultMediaType', 'print');
        $pdf->setOptions($options);

        // Generate HTML content for the PDF
        $html = view('pdf.colored_invoice', ['invoice' => $invoice])->render();

        $pdf->loadHtml($html);
        $pdf->setPaper('A4', 'portrait');
        $pdf->render();

        // Generate a filename
        $filename = 'Invoice-'.$invoice->invoice_number.'.pdf';

        // Stream the PDF to the browser
        return response($pdf->output())
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'inline; filename="'.$filename.'"');
    }
}
