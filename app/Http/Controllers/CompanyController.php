<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

final class CompanyController extends Controller
{
    public function index(Request $request)
    {
        $query = Company::query()
            ->with('user')
            ->orderBy($request->input('sort', 'created_at'), $request->input('direction', 'desc'));

        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('company_name', 'like', "%{$search}%")
                    ->orWhere('gstin', 'like', "%{$search}%")
                    ->orWhere('contact_person_name', 'like', "%{$search}%");
            });
        }

        if ($request->has('status')) {
            $status = $request->input('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $companies = $query->paginate(10)
            ->withQueryString();

        return Inertia::render('companies/index', [
            'companies' => $companies,
            'filters'   => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    public function create()
    {
        return Inertia::render('companies/create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'company_name'          => ['required', 'string', 'max:255', 'unique:companies,company_name'],
            'logo'                  => ['nullable', 'image', 'max:5120'],
            'gstin'                 => ['nullable', 'string', 'max:15'],
            'contact_no'            => ['required', 'string', 'max:20'],
            'contact_person_name'   => ['required', 'string', 'max:255'],
            'contact_person_number' => ['required', 'string', 'max:20'],
            'company_address'       => ['required', 'string'],
            'header_print'          => ['nullable', 'image', 'max:5120'],
            'digital_signature'     => ['nullable', 'image', 'max:5120'],
            'msme_reg_no'           => ['nullable', 'string', 'max:50'],
            'pan_number'            => ['nullable', 'string', 'max:10'],
            'bank_name'             => ['nullable', 'string', 'max:255'],
            'bank_branch'           => ['nullable', 'string', 'max:255'],
            'bank_account_number'   => ['nullable', 'string', 'max:50'],
            'bank_ifsc_code'        => ['nullable', 'string', 'max:20'],
            'bank_swift_code'       => ['nullable', 'string', 'max:20'],
            'is_active'             => ['boolean'],
        ]);

        // Handle file uploads
        if ($request->hasFile('logo')) {
            $validated['logo'] = $request->file('logo')->store('companies/logos', 'public');
        }

        if ($request->hasFile('header_print')) {
            $validated['header_print'] = $request->file('header_print')->store('companies/headers', 'public');
        }

        if ($request->hasFile('digital_signature')) {
            $validated['digital_signature'] = $request->file('digital_signature')->store('companies/signatures', 'public');
        }

        // Add user_id
        $validated['user_id'] = $request->user()->id;

        Company::create($validated);

        return redirect()
            ->route('manage-company.list')
            ->with('success', 'Company created successfully');
    }

    public function edit($id)
    {
        $company = Company::findOrFail($id);

        return Inertia::render('companies/edit', [
            'company' => [
                'id'                    => $company->id,
                'company_name'          => $company->company_name,
                'logo'                  => $company->logo ? Storage::url($company->logo) : null,
                'gstin'                 => $company->gstin,
                'contact_no'            => $company->contact_no,
                'contact_person_name'   => $company->contact_person_name,
                'contact_person_number' => $company->contact_person_number,
                'company_address'       => $company->company_address,
                'header_print'          => $company->header_print ? Storage::url($company->header_print) : null,
                'digital_signature'     => $company->digital_signature ? Storage::url($company->digital_signature) : null,
                'msme_reg_no'           => $company->msme_reg_no,
                'pan_number'            => $company->pan_number,
                'bank_name'             => $company->bank_name,
                'bank_branch'           => $company->bank_branch,
                'bank_account_number'   => $company->bank_account_number,
                'bank_ifsc_code'        => $company->bank_ifsc_code,
                'bank_swift_code'       => $company->bank_swift_code,
                'is_active'             => $company->is_active,
            ],
        ]);
    }

    public function update(Request $request, $id)
    {
        $company = Company::findOrFail($id);

        $validated = $request->validate([
            'company_name'          => ['required', 'string', 'max:255', 'unique:companies,company_name,'.$id],
            'logo'                  => ['nullable', 'image', 'max:5120'],
            'gstin'                 => ['nullable', 'string', 'max:15'],
            'contact_no'            => ['required', 'string', 'max:20'],
            'contact_person_name'   => ['required', 'string', 'max:255'],
            'contact_person_number' => ['required', 'string', 'max:20'],
            'company_address'       => ['required', 'string'],
            'header_print'          => ['nullable', 'image', 'max:5120'],
            'digital_signature'     => ['nullable', 'image', 'max:5120'],
            'msme_reg_no'           => ['nullable', 'string', 'max:50'],
            'pan_number'            => ['nullable', 'string', 'max:10'],
            'bank_name'             => ['nullable', 'string', 'max:255'],
            'bank_branch'           => ['nullable', 'string', 'max:255'],
            'bank_account_number'   => ['nullable', 'string', 'max:50'],
            'bank_ifsc_code'        => ['nullable', 'string', 'max:20'],
            'bank_swift_code'       => ['nullable', 'string', 'max:20'],
            'is_active'             => ['boolean'],
        ]);

        // Handle file uploads
        if ($request->hasFile('logo')) {
            // Delete old file if exists
            if ($company->logo) {
                Storage::disk('public')->delete($company->logo);
            }
            $validated['logo'] = $request->file('logo')->store('companies/logos', 'public');
        } else {
            unset($validated['logo']);
        }

        if ($request->hasFile('header_print')) {
            if ($company->header_print) {
                Storage::disk('public')->delete($company->header_print);
            }
            $validated['header_print'] = $request->file('header_print')->store('companies/headers', 'public');
        } else {
            unset($validated['header_print']);
        }

        if ($request->hasFile('digital_signature')) {
            if ($company->digital_signature) {
                Storage::disk('public')->delete($company->digital_signature);
            }
            $validated['digital_signature'] = $request->file('digital_signature')->store('companies/signatures', 'public');
        } else {
            unset($validated['digital_signature']);
        }

        $company->update($validated);

        return redirect()
            ->route('manage-company.list')
            ->with('success', 'Company updated successfully');
    }

    public function destroy($id)
    {

        // at list one company is requried that should not be delete
        $companyCount = Company::count();
        if ($companyCount <= 1) {
            return back()->with('error', 'At least one company is required');
        }

        $company = Company::findOrFail($id);

        // Delete associated files
        if ($company->logo) {
            Storage::disk('public')->delete($company->logo);
        }
        if ($company->header_print) {
            Storage::disk('public')->delete($company->header_print);
        }
        if ($company->digital_signature) {
            Storage::disk('public')->delete($company->digital_signature);
        }

        $company->delete();

        if ($id === Auth::user()->current_company_id) {
            $firstCompany                    = Company::first();
            Auth::user()->current_company_id = $firstCompany->id;
            Auth::user()->save();
        }

        return redirect()
            ->route('manage-company.list')
            ->with('success', 'Company deleted successfully');
    }

    public function trashed()
    {
        $trashedCompanies = Company::onlyTrashed()
            ->with('user')
            ->orderBy('deleted_at', 'desc')
            ->paginate(10);

        return Inertia::render('companies/trashed', [
            'companies' => $trashedCompanies,
        ]);
    }

    public function restore($id)
    {
        $company = Company::onlyTrashed()->findOrFail($id);
        $company->restore();

        return redirect()
            ->route('manage-company.trashed')
            ->with('success', 'Company restored successfully');
    }
}
