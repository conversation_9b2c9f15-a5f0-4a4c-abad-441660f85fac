<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

final class UserController extends Controller
{
    public function index(Request $request): Response
    {
        $branchId = auth()->user()->current_branch_id;

        $query = User::where('branch_id', $branchId);

        // Apply search filter
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                    ->orWhere('email', 'like', "%{$request->search}%")
                    ->orWhere('phone', 'like', "%{$request->search}%");
            });
        }

        // Apply role filter
        if ($request->role) {
            $query->where('role', $request->role);
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'name';
        $sortDirection = $request->direction ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        $users = $query->paginate(10)
            ->withQueryString();

        return Inertia::render('users/index', [
            'users'   => $users,
            'filters' => $request->only(['search', 'role', 'sort', 'direction']),
        ]);
    }

    public function updateCurrentBranch(Request $request)
    {
        $userId   = auth()->id();
        $branchId = $request->input('branch_id');

        $user                    = User::find($userId);
        $user->current_branch_id = $branchId;
        $user->save();

        return redirect()->back()->with('success', 'Branch updated successfully');
    }
}
