<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\AppointmentService;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Seat;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

final class DashboardController extends Controller
{
    public function index(Request $request): Response
    {
        $user = auth()->user();

        // Get current user's branch ID
        $branchId = $user->current_branch_id;

        // If user doesn't have a branch (admin/customer), show basic dashboard
        if (!$branchId) {
            return Inertia::render('dashboard', [
                'stats' => [],
                'appointmentStats' => [],
                'seatStats' => [],
                'upcomingAppointments' => [],
                'recentCompletedServices' => [],
                'recentProducts' => [],
                'userRole' => $user->roles->pluck('name')->first() ?? 'user',
                'message' => 'Welcome to the dashboard!',
            ]);
        }

        $stats = [];

        // Salon appointment stats
        $today = Carbon::today();

        // Today's appointments
        $todayAppointments = Appointment::where('branch_id', $branchId)
            ->whereDate('appointment_date', $today)
            ->get();

        $appointmentStats = [
            'today_total'       => $todayAppointments->count(),
            'today_pending'     => $todayAppointments->where('status', 'pending')->count(),
            'today_in_progress' => $todayAppointments->where('status', 'in_progress')->count(),
            'today_completed'   => $todayAppointments->where('status', 'completed')->count(),
            'today_cancelled'   => $todayAppointments->where('status', 'cancelled')->count(),
        ];

        // Seat availability
        $seatStats = [
            'total'       => Seat::where('branch_id', $branchId)->count(),
            'available'   => Seat::where('branch_id', $branchId)->where('status', 'available')->count(),
            'occupied'    => Seat::where('branch_id', $branchId)->where('status', 'occupied')->count(),
            'maintenance' => Seat::where('branch_id', $branchId)->whereIn('status', ['cleaning', 'maintenance'])->count(),
        ];

        // Upcoming appointments (next 7 days)
        $upcomingAppointments = Appointment::with(['services', 'user'])
            ->where('branch_id', $branchId)
            ->where('status', 'pending')
            ->whereDate('appointment_date', '>=', $today)
            ->whereDate('appointment_date', '<=', $today->copy()->addDays(7))
            ->orderBy('appointment_date')
            ->orderBy('appointment_time')
            ->take(5)
            ->get()
            ->map(fn ($appointment) => [
                'id'            => $appointment->id,
                'customer_name' => $appointment->user->name,
                'date'          => $appointment->appointment_date->format('M d, Y'),
                'time'          => is_string($appointment->appointment_time)
                    ? Carbon::parse($appointment->appointment_time)->format('h:i A')
                    : $appointment->appointment_time->format('h:i A'),
                'ticket_number'  => $appointment->ticket_number,
                'services_count' => $appointment->services->count(),
                'status'         => $appointment->status,
            ]);

        // Recent completed services
        $recentCompletedServices = AppointmentService::with(['appointment.user', 'service', 'seat'])
            ->whereHas('appointment', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })
            ->where('status', 'completed')
            ->orderBy('end_time', 'desc')
            ->take(5)
            ->get()
            ->map(fn ($service) => [
                'id'            => $service->id,
                'customer_name' => $service->appointment->user->name,
                'service_name'  => $service->service->name ?? 'Unknown Service',
                'seat'          => $service->seat->name    ?? 'No Seat',
                'completed_at'  => $service->end_time->diffForHumans(),
                'duration'      => $service->start_time && $service->end_time
                    ? $service->start_time->diffInMinutes($service->end_time).' min'
                    : 'Unknown',
            ]);

        return Inertia::render('dashboard', [
            'stats'                   => $stats,
            'appointmentStats'        => $appointmentStats,
            'seatStats'               => $seatStats,
            'upcomingAppointments'    => $upcomingAppointments,
            'recentCompletedServices' => $recentCompletedServices,
            'recentProducts'          => isset($stats['products']) ? Product::with(['categories', 'unit', 'hsn'])
                ->where('products.company_id', $branchId)
                ->latest()
                ->take(5)
                ->get()
                ->map(fn ($product) => [
                    'id'         => $product->id,
                    'name'       => $product->name,
                    'category'   => $product->categories->first()?->name,
                    'unit'       => $product->unit?->name,
                    'hsn'        => $product->hsn?->code,
                    'created_at' => $product->created_at->diffForHumans(),
                    'is_active'  => $product->is_active,
                ]) : [],
            'categoryDistribution' => isset($stats['categories']) && isset($stats['products'])
                ? ProductCategory::withCount(['products' => function ($query) use ($branchId) {
                    $query->where('products.company_id', $branchId);
                }])
                    ->where('product_categories.company_id', $branchId)
                    ->get()
                    ->filter(fn ($category) => $category->products_count > 0)
                    ->map(fn ($category) => [
                        'name'  => $category->name,
                        'count' => $category->products_count,
                    ]) : [],
        ]);
    }
}
