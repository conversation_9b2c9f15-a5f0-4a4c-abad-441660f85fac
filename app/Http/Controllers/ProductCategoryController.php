<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

final class ProductCategoryController extends Controller
{
    public function index(Request $request)
    {
        // Get current user's company ID
        $companyId = auth()->user()->current_company_id;

        $query = ProductCategory::query()
            ->with(['parent'])
            ->withCount(['children', 'products'])
            ->where('company_id', $companyId);

        // Apply filters
        if ($request->search) {
            $query->search($request->search);
        }

        if ($request->status && $request->status !== 'all') {
            $query->where('is_active', $request->status === 'active');
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'id';
        $sortDirection = $request->direction ?? 'desc';
        $query->sort($sortField, $sortDirection);

        $categories = $query->paginate(10)
            ->withQueryString();

        // Get all categories for parent selection (without pagination)
        $allCategories = ProductCategory::select(['id', 'name', 'parent_id'])
            ->where('company_id', $companyId)
            ->orderBy('name')
            ->get();

        return Inertia::render('products/categories/index', [
            'categories'    => $categories,
            'allCategories' => $allCategories,
            'filters'       => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    public function create()
    {
        $companyId  = auth()->user()->current_company_id;
        $categories = ProductCategory::select(['id', 'name', 'parent_id'])
            ->where('company_id', $companyId)
            ->orderBy('name')
            ->get();

        return Inertia::render('products/categories/create', [
            'categories' => $categories,
        ]);
    }

    public function store(Request $request)
    {
        $companyId = auth()->user()->current_company_id;

        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('product_categories')->where(function ($query) use ($request) {
                    return $query->where('parent_id', $request->parent_id);
                }),
            ],
            'parent_id' => [
                'nullable',
                'exists:product_categories,id',
            ],
            'is_active'   => 'boolean',
            'description' => 'nullable|string',
        ]);

        $validated['slug']       = $this->generateUniqueSlug($validated['name']);
        $validated['company_id'] = $companyId;

        ProductCategory::create($validated);

        return redirect()
            ->route('products.categories.index')
            ->with('success', 'Category created successfully');
    }

    public function edit(ProductCategory $category)
    {
        $companyId = auth()->user()->current_company_id;
        $category->where('company_id', $companyId)->firstOrFail();
        $categories = ProductCategory::select(['id', 'name', 'parent_id'])
            ->where('company_id', $companyId)
            ->where('id', '!=', $category->id)
            ->whereNotIn('id', $category->children->pluck('id'))
            ->orderBy('name')
            ->get();

        return Inertia::render('products/categories/edit', [
            'category'   => $category,
            'categories' => $categories,
        ]);
    }

    public function update(Request $request, ProductCategory $category)
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('product_categories')->where(function ($query) use ($request) {
                    return $query->where('parent_id', $request->parent_id);
                })->ignore($category->id),
            ],
            'parent_id' => [
                'nullable',
                'exists:product_categories,id',
                function ($attribute, $value, $fail) use ($category) {
                    if ($value === $category->id) {
                        $fail('A category cannot be its own parent.');
                    }
                },
            ],
            'is_active'   => 'boolean',
            'description' => 'nullable|string',
        ]);

        $validated['slug'] = $this->generateUniqueSlug($validated['name'], $category->id);

        $category->update($validated);

        return redirect()
            ->route('products.categories.index')
            ->with('success', 'Category updated successfully');
    }

    public function destroy(ProductCategory $category)
    {
        // Check if category has children
        if ($category->children()->exists()) {
            return back()->with('error', 'Cannot delete category with subcategories');
        }

        // Check if category is used by any products
        if ($category->products()->exists()) {
            return back()->with('error', 'Cannot delete category that is being used by products');
        }

        $category->delete();

        return redirect()
            ->route('products.categories.index')
            ->with('success', 'Category deleted successfully');
    }

    private function generateUniqueSlug(string $name, ?int $excludeId = null): string
    {
        $originalSlug = Str::slug($name);
        $slug         = $originalSlug;
        $counter      = 1;
        $suffix       = 1;

        while (true) {
            $query = ProductCategory::where('slug', $slug);

            // Exclude current category when updating
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (! $query->exists()) {
                break;
            }

            // If base slug with counter exists, increment the suffix
            if (preg_match('/-(\d+)$/', $slug, $matches)) {
                $baseSlug = preg_replace('/-\d+$/', '', $slug);
                $suffix   = (int) $matches[1] + 1;
                $slug     = "{$baseSlug}-{$suffix}";
            } else {
                // First duplicate, add counter
                $slug = "{$originalSlug}-{$counter}";
                $counter++;
            }
        }

        return $slug;
    }
}
