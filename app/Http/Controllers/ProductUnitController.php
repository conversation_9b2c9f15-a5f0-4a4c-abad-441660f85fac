<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\ProductUnit;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

final class ProductUnitController extends Controller
{
    public function index(Request $request)
    {
        $companyId = auth()->user()->current_company_id;

        $query = ProductUnit::query()->where('company_id', $companyId)
            ->withCount(['products']);

        // Apply search filter
        if ($request->search) {
            $query->search($request->search);
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'id';
        $sortDirection = $request->direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $units = $query->paginate(10)
            ->withQueryString();

        return Inertia::render('products/units/index', [
            'units'   => $units,
            'filters' => $request->only(['search', 'sort', 'direction']),
        ]);
    }

    public function create()
    {
        return Inertia::render('products/units/create');
    }

    public function store(Request $request)
    {
        $companyId = auth()->user()->current_company_id;

        // Check if a soft-deleted record with the same name or short_name exists
        $existingDeleted = ProductUnit::onlyTrashed()
            ->where('company_id', $companyId)
            ->where(function ($query) use ($request) {
                $query->where('name', $request->name)
                    ->orWhere('short_name', $request->short_name);
            })
            ->first();

        if ($existingDeleted) {
            // Restore the soft-deleted record and update it
            $existingDeleted->restore();

            $existingDeleted->update([
                'name'        => $request->name,
                'short_name'  => $request->short_name,
                'description' => $request->description,
                'company_id'  => $companyId,
            ]);

            return redirect()
                ->route('products.units.index')
                ->with('success', 'Unit restored and updated successfully');
        }

        $validated = $request->validate([
            'name'        => ['required', 'string', 'max:255', 'unique:product_units'],
            'short_name'  => ['required', 'string', 'max:50', 'unique:product_units'],
            'description' => ['nullable', 'string'],
        ]);

        $validated['company_id'] = $companyId;

        ProductUnit::create($validated);

        return redirect()
            ->route('products.units.index')
            ->with('success', 'Unit created successfully');
    }

    public function edit(ProductUnit $unit)
    {
        $companyId = auth()->user()->current_company_id;
        $unit->where('company_id', $companyId)->firstOrFail();

        return Inertia::render('products/units/edit', [
            'unit' => $unit,
        ]);
    }

    public function update(Request $request, ProductUnit $unit)
    {
        $companyId = auth()->user()->current_company_id;

        // Check if we're trying to update to a name or short_name that exists in a soft-deleted record
        $existingDeleted = ProductUnit::onlyTrashed()
            ->where('company_id', $companyId)
            ->where('id', '!=', $unit->id)
            ->where(function ($query) use ($request) {
                $query->where('name', $request->name)
                    ->orWhere('short_name', $request->short_name);
            })
            ->first();

        if ($existingDeleted) {
            // Restore the soft-deleted record and update it with the current data
            $existingDeleted->restore();

            $existingDeleted->update([
                'name'        => $request->name,
                'short_name'  => $request->short_name,
                'description' => $request->description,
            ]);

            // Delete the current unit as we're using the restored one instead
            $unit->delete();

            return redirect()
                ->route('products.units.index')
                ->with('success', 'Unit restored and updated successfully');
        }

        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('product_units')->ignore($unit->id),
            ],
            'short_name' => [
                'required',
                'string',
                'max:50',
                Rule::unique('product_units')->ignore($unit->id),
            ],
            'description' => ['nullable', 'string'],
        ]);

        $unit->update($validated);

        return redirect()
            ->route('products.units.index')
            ->with('success', 'Unit updated successfully');
    }

    public function destroy(ProductUnit $unit)
    {
        // Check if unit is used by any products
        if ($unit->products()->exists()) {
            return back()->with('error', 'Cannot delete unit that is being used by products');
        }

        $unit->delete();

        return redirect()
            ->route('products.units.index')
            ->with('success', 'Unit deleted successfully');
    }
}
