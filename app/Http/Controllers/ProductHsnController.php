<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\ProductHsn;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

final class ProductHsnController extends Controller
{
    public function index(Request $request)
    {
        $companyId = auth()->user()->current_company_id;
        $query     = ProductHsn::query()->where('company_id', $companyId)
            ->withCount(['products']);

        // Apply search filter
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('code', 'like', "%{$request->search}%")
                    ->orWhere('description', 'like', "%{$request->search}%")
                    ->orWhere('gst_rate', 'like', "%{$request->search}%");
            });
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'id';
        $sortDirection = $request->direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $hsns = $query->paginate(10)
            ->withQueryString();

        return Inertia::render('products/hsns/index', [
            'hsns'    => $hsns,
            'filters' => $request->only(['search', 'sort', 'direction']),
        ]);
    }

    public function create()
    {
        return Inertia::render('products/hsns/create');
    }

    public function store(Request $request)
    {
        $companyId = auth()->user()->current_company_id;

        // Check if a soft-deleted record with the same code exists
        $existingDeleted = ProductHsn::onlyTrashed()
            ->where('company_id', $companyId)
            ->where('code', $request->code)
            ->first();

        if ($existingDeleted) {
            // Restore the soft-deleted record and update it
            $existingDeleted->restore();

            $existingDeleted->update([
                'code'        => $request->code,
                'description' => $request->description,
                'gst_rate'    => $request->gst_rate,
                'company_id'  => $companyId,
            ]);

            return redirect()
                ->route('products.hsns.index')
                ->with('success', 'HSN code restored and updated successfully');
        }

        $validated = $request->validate([
            'code'        => ['required', 'string', 'max:8', 'unique:product_hsns'],
            'description' => ['required', 'string'],
            'gst_rate'    => ['required', 'numeric', 'min:0', 'max:100'],
        ]);
        $validated['company_id'] = $companyId;

        ProductHsn::create($validated);

        return redirect()
            ->route('products.hsns.index')
            ->with('success', 'HSN code created successfully');
    }

    public function edit(ProductHsn $hsn)
    {
        $companyId = auth()->user()->current_company_id;
        $hsn->where('company_id', $companyId)->firstOrFail();

        return Inertia::render('products/hsns/edit', [
            'hsn' => $hsn,
        ]);
    }

    public function update(Request $request, ProductHsn $hsn)
    {
        $companyId = auth()->user()->current_company_id;

        // Check if we're trying to update to a code that exists in a soft-deleted record
        $existingDeleted = ProductHsn::onlyTrashed()
            ->where('company_id', $companyId)
            ->where('id', '!=', $hsn->id)
            ->where('code', $request->code)
            ->first();

        if ($existingDeleted) {
            // Restore the soft-deleted record and update it with the current data
            $existingDeleted->restore();

            $existingDeleted->update([
                'code'        => $request->code,
                'description' => $request->description,
                'gst_rate'    => $request->gst_rate,
            ]);

            // Delete the current HSN code as we're using the restored one instead
            $hsn->delete();

            return redirect()
                ->route('products.hsns.index')
                ->with('success', 'HSN code restored and updated successfully');
        }

        $validated = $request->validate([
            'code' => [
                'required',
                'string',
                'max:8',
                Rule::unique('product_hsns')->ignore($hsn->id),
            ],
            'description' => ['required', 'string'],
            'gst_rate'    => ['required', 'numeric', 'min:0', 'max:100'],
        ]);

        $hsn->update($validated);

        return redirect()
            ->route('products.hsns.index')
            ->with('success', 'HSN code updated successfully');
    }

    public function destroy(ProductHsn $hsn)
    {
        if ($hsn->products()->exists()) {
            return back()->with('error', 'Cannot delete HSN code that is being used by products');
        }

        $hsn->delete();

        return redirect()
            ->route('products.hsns.index')
            ->with('success', 'HSN code deleted successfully');
    }
}
