<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\TermCondition;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

final class TermConditionController extends Controller
{
    public function index(Request $request): Response
    {
        $branchId = auth()->user()->current_branch_id;
        $query    = TermCondition::with('user')
            ->where('branch_id', $branchId);

        // Apply search filter
        if ($search = $request->search) {
            $query->search($search);
        }

        // Apply status filter
        if ($status = $request->status) {
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Apply type filter
        if ($type = $request->type) {
            $query->where('type', $type);
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'id';
        $sortDirection = $request->direction ?? 'desc';
        $query->sort($sortField, $sortDirection);

        $termConditions = $query->paginate(10)
            ->withQueryString()
            ->through(function ($termCondition) {
                // Ensure type is never null
                return [
                    'id'         => $termCondition->id,
                    'title'      => $termCondition->title,
                    'condition'  => $termCondition->condition,
                    'type'       => $termCondition->type ?? 'default',
                    'is_default' => $termCondition->is_default,
                    'is_active'  => $termCondition->is_active,
                    'user'       => [
                        'name' => $termCondition->user->name,
                    ],
                ];
            });

        return Inertia::render('terms-conditions/index', [
            'termConditions' => $termConditions,
            'filters'        => $request->only(['search', 'status', 'type', 'sort', 'direction']),
        ]);
    }

    public function create(): Response
    {
        return Inertia::render('terms-conditions/create');
    }

    public function store(Request $request)
    {
        $branchId = auth()->user()->current_branch_id;
        $userId   = auth()->id();

        $validated = $request->validate([
            'title'      => 'required|string|max:255',
            'condition'  => 'required|string',
            'is_default' => 'boolean',
            'is_active'  => 'boolean',
        ]);

        $validated['branch_id'] = $branchId;
        $validated['user_id']   = $userId;

        // If this is set as default, unset all other defaults
        if ($validated['is_default']) {
            TermCondition::where('branch_id', $branchId)
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        TermCondition::create($validated);

        return redirect()
            ->route('terms-conditions.index')
            ->with('success', 'Term & Condition created successfully');
    }

    public function edit(TermCondition $termCondition): Response
    {
        $branchId = auth()->user()->current_branch_id;
        $termCondition->where('branch_id', $branchId)->firstOrFail();

        return Inertia::render('terms-conditions/edit', [
            'termCondition' => $termCondition,
        ]);
    }

    public function update(Request $request, TermCondition $termCondition)
    {
        $branchId = auth()->user()->current_branch_id;

        // Ensure the term condition belongs to the current branch
        if ($termCondition->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'title'      => 'required|string|max:255',
            'condition'  => 'required|string',
            'is_default' => 'boolean',
            'is_active'  => 'boolean',
        ]);

        // If this is set as default, unset all other defaults
        if ($validated['is_default'] && ! $termCondition->is_default) {
            TermCondition::where('branch_id', $branchId)
                ->where('id', '!=', $termCondition->id)
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $termCondition->update($validated);

        return redirect()
            ->route('terms-conditions.index')
            ->with('success', 'Term & Condition updated successfully');
    }

    public function destroy(TermCondition $termCondition)
    {
        $branchId = auth()->user()->current_branch_id;

        // Ensure the term condition belongs to the current branch
        if ($termCondition->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        $termCondition->delete();

        return redirect()
            ->route('terms-conditions.index')
            ->with('success', 'Term & Condition deleted successfully');
    }

    public function trashed(Request $request): Response
    {
        $branchId = auth()->user()->current_branch_id;

        $query = TermCondition::onlyTrashed()
            ->where('branch_id', $branchId);

        // Apply search filter
        if ($request->search) {
            $query->where('title', 'like', "%{$request->search}%")
                ->orWhere('condition', 'like', "%{$request->search}%");
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'deleted_at';
        $sortDirection = $request->direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $trashedTermConditions = $query->paginate(10)
            ->withQueryString();

        return Inertia::render('terms-conditions/trashed', [
            'trashedTermConditions' => $trashedTermConditions,
            'filters'               => $request->only(['search', 'sort', 'direction']),
        ]);
    }

    public function restore($id)
    {
        $branchId = auth()->user()->current_branch_id;

        $termCondition = TermCondition::onlyTrashed()
            ->where('id', $id)
            ->where('branch_id', $branchId)
            ->firstOrFail();

        $termCondition->restore();

        return redirect()
            ->route('terms-conditions.index')
            ->with('success', 'Term & Condition restored successfully');
    }
}
