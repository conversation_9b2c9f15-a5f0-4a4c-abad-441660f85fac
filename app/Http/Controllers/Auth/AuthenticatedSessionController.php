<?php

declare(strict_types=1);

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\Branch;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;

final class AuthenticatedSessionController extends Controller
{
    /**
     * Show the login page.
     */
    public function create(Request $request): Response
    {
        return Inertia::render('auth/login', [
            'canResetPassword' => Route::has('password.request'),
            'status'           => $request->session()->get('status'),
        ]);
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        // Check user role and redirect accordingly
        $user = $request->user();
        $protocol = $request->isSecure() ? 'https' : 'http';

        // If user is a vendor, redirect to their tenant domain
        if ($user->hasRole('vendor') && $user->company_domain) {
            Auth::guard('web')->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();


            $vendorUrl = $protocol . '://' . $user->company_domain . '.salon.test/dashboard';
            Auth::login($user);
            session()->flash('vendor_redirect_url', $vendorUrl);
            return redirect()->route('vendor.redirect');
        }

        // Only allow admin and customer access to central domain
        if ($user->hasRole('admin') || $user->hasRole('customer')) {
            Auth::guard('web')->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            $vendorUrl = $protocol . '://salon.test/admin/dashboard';
            Auth::login($user);
            session()->flash('vendor_redirect_url', $vendorUrl);
            return redirect()->route('vendor.redirect');
        }

        $request->session()->regenerate();

        // Check if user has a current_branch_id set
        if (!$user->current_branch_id) {
            // Get the first branch associated with this user
            $branch = Branch::first();
            if ($branch) {
                $user->current_branch_id = $branch->id;
                $user->save();
            }
        }

        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login');
    }
}
