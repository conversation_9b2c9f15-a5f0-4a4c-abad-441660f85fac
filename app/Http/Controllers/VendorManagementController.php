<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Stancl\Tenancy\Database\Models\Domain;
use App\Models\Tenant;

final class VendorManagementController extends Controller
{
    public function index(Request $request): Response
    {
        $query = User::whereHas('roles', function ($q) {
            $q->where('name', 'vendor');
        })->with(['roles']);

        // Apply search filter
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                    ->orWhere('email', 'like', "%{$request->search}%")
                    ->orWhere('company_name', 'like', "%{$request->search}%")
                    ->orWhere('company_domain', 'like', "%{$request->search}%");
            });
        }

        // Apply sorting
        $sortField = $request->sort ?? 'created_at';
        $sortDirection = $request->direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $vendors = $query->paginate(10)->withQueryString();

        return Inertia::render('admin/vendors/index', [
            'vendors' => $vendors,
            'filters' => [
                'search' => $request->search,
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
        ]);
    }

    public function show(User $vendor): Response
    {
        // Ensure the user is a vendor
        if (!$vendor->hasRole('vendor')) {
            abort(404);
        }

        $vendor->load(['roles']);

        // Get tenant information
        $tenant = null;
        $domain = null;
        if ($vendor->tenant_id) {
            $tenant = Tenant::find($vendor->tenant_id);
            $domain = Domain::where('tenant_id', $vendor->tenant_id)->first();
        }

        return Inertia::render('admin/vendors/show', [
            'vendor' => $vendor,
            'tenant' => $tenant,
            'domain' => $domain,
        ]);
    }

    public function destroy(User $vendor)
    {
        // Ensure the user is a vendor
        if (!$vendor->hasRole('vendor')) {
            abort(404);
        }

        // Delete associated tenant and domain
        if ($vendor->tenant_id) {
            $tenant = Tenant::find($vendor->tenant_id);
            if ($tenant) {
                $tenant->delete(); // This will also delete associated domains
            }
        }

        $vendor->delete();

        return redirect()
            ->route('vendors.index')
            ->with('success', 'Vendor deleted successfully');
    }
}
