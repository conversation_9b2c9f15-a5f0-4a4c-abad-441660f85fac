<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\VendorSubscription;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

final class AdminDashboardController extends Controller
{
    public function index(Request $request): Response
    {
        // Get dashboard statistics
        $stats = [
            'total_vendors' => User::whereHas('roles', function ($q) {
                $q->where('name', 'vendor');
            })->count(),

            'active_subscriptions' => VendorSubscription::where('status', 'active')
                ->where('ends_at', '>', now())
                ->count(),

            'expired_subscriptions' => VendorSubscription::where('status', 'expired')
                ->orWhere('ends_at', '<', now())
                ->count(),

            'trial_subscriptions' => VendorSubscription::where('status', 'trial')
                ->where('trial_ends_at', '>', now())
                ->count(),

            'total_revenue' => VendorSubscription::where('status', 'active')
                ->with('subscriptionPlan')
                ->get()
                ->sum(function ($subscription) {
                    return (float) $subscription->subscriptionPlan->price;
                }),
        ];

        // Get recent vendors
        $recentVendors = User::whereHas('roles', function ($q) {
            $q->where('name', 'vendor');
        })
        ->with(['roles', 'vendorSubscriptions.subscriptionPlan'])
        ->latest()
        ->take(5)
        ->get()
        ->map(function ($vendor) {
            $subscription = $vendor->getCurrentSubscription();
            return [
                'id' => $vendor->id,
                'name' => $vendor->name,
                'email' => $vendor->email,
                'company_name' => $vendor->company_name,
                'company_domain' => $vendor->company_domain,
                'created_at' => $vendor->created_at->diffForHumans(),
                'subscription' => $subscription ? [
                    'plan_name' => $subscription->subscriptionPlan->name,
                    'status' => $subscription->status,
                    'ends_at' => $subscription->ends_at->format('M d, Y'),
                ] : null,
            ];
        });

        // Get subscription plan statistics
        $planStats = SubscriptionPlan::withCount(['activeVendorSubscriptions'])
            ->get()
            ->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'price' => $plan->formatted_price,
                    'active_subscriptions' => $plan->active_vendor_subscriptions_count,
                    'revenue' => (float) $plan->price * $plan->active_vendor_subscriptions_count,
                ];
            });

        // Get expiring subscriptions (next 30 days)
        $expiringSubscriptions = VendorSubscription::where('status', 'active')
            ->where('ends_at', '>', now())
            ->where('ends_at', '<=', now()->addDays(30))
            ->with(['user', 'subscriptionPlan'])
            ->get()
            ->map(function ($subscription) {
                return [
                    'id' => $subscription->id,
                    'vendor_name' => $subscription->user->name,
                    'company_name' => $subscription->user->company_name,
                    'plan_name' => $subscription->subscriptionPlan->name,
                    'ends_at' => $subscription->ends_at->format('M d, Y'),
                    'days_until_expiry' => $subscription->daysUntilExpiry(),
                ];
            });

        return Inertia::render('admin/dashboard', [
            'stats' => $stats,
            'recentVendors' => $recentVendors,
            'planStats' => $planStats,
            'expiringSubscriptions' => $expiringSubscriptions,
        ]);
    }
}
