<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Service;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

final class ServiceController extends Controller
{
    public function index(Request $request): Response
    {
        $branchId = auth()->user()->current_branch_id;

        $query = Service::where('branch_id', $branchId);

        // Apply search filter
        if ($request->search) {
            $query->where('name', 'like', "%{$request->search}%")
                ->orWhere('description', 'like', "%{$request->search}%");
        }

        // Apply status filter
        if ($request->status !== null) {
            $query->where('is_active', $request->status === 'active');
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'name';
        $sortDirection = $request->direction ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        $services = $query->paginate(10)
            ->withQueryString();

        return Inertia::render('services/index', [
            'services' => $services,
            'filters'  => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    public function create(): Response
    {
        return Inertia::render('services/create');
    }

    public function store(Request $request)
    {
        $user = auth()->user();
        $branchId = $user->current_branch_id;

        // Check if user has a current branch
        if (!$branchId) {
            return redirect()
                ->back()
                ->withErrors(['branch_id' => 'You must have a current branch set to create services. Please contact support.'])
                ->withInput();
        }

        $validated = $request->validate([
            'name'             => 'required|string|max:255',
            'description'      => 'required|string|max:1000',
            'duration_minutes' => 'required|integer|min:1',
            'price'            => 'required|numeric|min:0',
            'is_active'        => 'boolean',
        ]);

        $validated['branch_id'] = $branchId;
        $validated['is_active'] = $validated['is_active'] ?? true;

        try {
            Service::create($validated);

            return redirect()
                ->route('services.index')
                ->with('success', 'Service created successfully');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Failed to create service: ' . $e->getMessage()])
                ->withInput();
        }
    }

    public function edit(Service $service): Response
    {
        $branchId = auth()->user()->current_branch_id;

        // Ensure the service belongs to the current branch
        if ($service->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        return Inertia::render('services/edit', [
            'service' => $service,
        ]);
    }

    public function update(Request $request, Service $service)
    {
        $branchId = auth()->user()->current_branch_id;

        // Ensure the service belongs to the current branch
        if ($service->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name'             => 'required|string|max:255',
            'description'      => 'nullable|string|max:1000',
            'duration_minutes' => 'required|integer|min:5|max:480',
            'price'            => 'required|numeric|min:0',
            'is_active'        => 'boolean',
        ]);

        $validated['is_active'] = $validated['is_active'] ?? true;

        $service->update($validated);

        return redirect()
            ->route('services.index')
            ->with('success', 'Service updated successfully');
    }

    public function destroy(Service $service)
    {
        $branchId = auth()->user()->current_branch_id;

        // Ensure the service belongs to the current branch
        if ($service->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        // Check if service is used in any appointments
        if ($service->appointments()->count() > 0) {
            return redirect()
                ->route('services.index')
                ->with('error', 'Cannot delete a service that is used in appointments');
        }

        $service->delete();

        return redirect()
            ->route('services.index')
            ->with('success', 'Service deleted successfully');
    }
}
