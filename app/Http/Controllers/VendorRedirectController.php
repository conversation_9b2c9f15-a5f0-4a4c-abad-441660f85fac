<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class VendorRedirectController extends Controller
{
    /**
     * Show the vendor redirect page that will handle external redirect via JavaScript.
     */
    public function show(Request $request): Response|RedirectResponse
    {
        $redirectUrl = session('vendor_redirect_url');

        if (!$redirectUrl) {
            return redirect()->route('dashboard');
        }

        return Inertia::render('vendor-redirect', [
            'redirectUrl' => $redirectUrl,
        ]);
    }
}
