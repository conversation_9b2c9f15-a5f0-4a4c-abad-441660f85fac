<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\VendorSubscription;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;
use Carbon\Carbon;

final class VendorSubscriptionController extends Controller
{
    public function index(Request $request): Response
    {
        $query = VendorSubscription::with(['user', 'subscriptionPlan']);

        // Apply search filter
        if ($request->search) {
            $query->whereHas('user', function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                    ->orWhere('email', 'like', "%{$request->search}%")
                    ->orWhere('company_name', 'like', "%{$request->search}%");
            });
        }

        // Apply status filter
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Apply plan filter
        if ($request->plan_id) {
            $query->where('subscription_plan_id', $request->plan_id);
        }

        $subscriptions = $query->latest()
            ->paginate(15)
            ->withQueryString()
            ->through(function ($subscription) {
                return [
                    'id' => $subscription->id,
                    'user' => [
                        'id' => $subscription->user->id,
                        'name' => $subscription->user->name,
                        'email' => $subscription->user->email,
                        'company_name' => $subscription->user->company_name,
                        'company_domain' => $subscription->user->company_domain,
                    ],
                    'subscription_plan' => [
                        'id' => $subscription->subscriptionPlan->id,
                        'name' => $subscription->subscriptionPlan->name,
                        'price' => $subscription->subscriptionPlan->price,
                        'billing_cycle' => $subscription->subscriptionPlan->billing_cycle,
                    ],
                    'starts_at' => $subscription->starts_at->toISOString(),
                    'ends_at' => $subscription->ends_at->toISOString(),
                    'trial_ends_at' => $subscription->trial_ends_at?->toISOString(),
                    'status' => $subscription->status,
                    'current_services_count' => $subscription->current_services_count,
                    'current_appointments_count' => $subscription->current_appointments_count,
                    'current_seats_count' => $subscription->current_seats_count,
                    'current_branches_count' => $subscription->current_branches_count,
                    'current_staff_count' => $subscription->current_staff_count,
                ];
            });

        $plans = SubscriptionPlan::active()->ordered()->get();

        return Inertia::render('admin/vendor-subscriptions/index', [
            'subscriptions' => $subscriptions,
            'plans' => $plans,
            'filters' => $request->only(['search', 'status', 'plan_id']),
        ]);
    }

    public function create(Request $request): Response
    {
        $vendors = User::whereHas('roles', function ($q) {
            $q->where('name', 'vendor');
        })->get();

        $plans = SubscriptionPlan::active()->ordered()->get();

        $selectedVendor = null;
        if ($request->vendor_id) {
            $selectedVendor = User::find($request->vendor_id);
        }

        return Inertia::render('admin/vendor-subscriptions/create', [
            'vendors' => $vendors,
            'plans' => $plans,
            'selectedVendor' => $selectedVendor,
        ]);
    }

    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'starts_at' => 'required|date',
            'billing_cycle_count' => 'required|integer|min:1|max:12',
            'status' => 'required|in:active,trial',
            'trial_days' => 'nullable|integer|min:0|max:365',
        ]);

        $vendor = User::findOrFail($validated['user_id']);
        $plan = SubscriptionPlan::findOrFail($validated['subscription_plan_id']);

        // Check if vendor already has an active subscription
        $existingSubscription = $vendor->getCurrentSubscription();
        if ($existingSubscription) {
            return redirect()
                ->back()
                ->withErrors(['user_id' => 'Vendor already has an active subscription']);
        }

        $startsAt = Carbon::parse($validated['starts_at']);
        $endsAt = $plan->billing_cycle === 'monthly'
            ? $startsAt->copy()->addMonths($validated['billing_cycle_count'])
            : $startsAt->copy()->addYears($validated['billing_cycle_count']);

        $subscriptionData = [
            'user_id' => $validated['user_id'],
            'subscription_plan_id' => $validated['subscription_plan_id'],
            'starts_at' => $startsAt,
            'ends_at' => $endsAt,
            'status' => $validated['status'],
            'billing_period_start' => $startsAt->startOfMonth()->toDateString(),
            'billing_period_end' => $startsAt->endOfMonth()->toDateString(),
        ];

        if ($validated['status'] === 'trial' && isset($validated['trial_days'])) {
            $subscriptionData['trial_ends_at'] = $startsAt->copy()->addDays($validated['trial_days']);
        }

        $subscription = VendorSubscription::create($subscriptionData);

        // Update usage counts
        $subscription->updateUsageCounts();

        return redirect()
            ->route('admin.vendor-subscriptions.index')
            ->with('success', 'Vendor subscription created successfully');
    }

    public function show(VendorSubscription $vendorSubscription): Response
    {
        $vendorSubscription->load(['user', 'subscriptionPlan']);

        return Inertia::render('admin/vendor-subscriptions/show', [
            'subscription' => $vendorSubscription,
        ]);
    }

    public function edit(VendorSubscription $vendorSubscription): Response
    {
        $vendorSubscription->load(['user', 'subscriptionPlan']);
        $plans = SubscriptionPlan::active()->ordered()->get();

        return Inertia::render('admin/vendor-subscriptions/edit', [
            'subscription' => $vendorSubscription,
            'plans' => $plans,
        ]);
    }

    public function update(Request $request, VendorSubscription $vendorSubscription): RedirectResponse
    {
        $validated = $request->validate([
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'ends_at' => 'required|date|after:today',
            'status' => 'required|in:active,inactive,cancelled,expired,trial',
            'trial_ends_at' => 'nullable|date',
        ]);

        $vendorSubscription->update($validated);
        $vendorSubscription->updateUsageCounts();

        return redirect()
            ->route('admin.vendor-subscriptions.index')
            ->with('success', 'Vendor subscription updated successfully');
    }

    public function destroy(VendorSubscription $vendorSubscription): RedirectResponse
    {
        $vendorSubscription->delete();

        return redirect()
            ->route('admin.vendor-subscriptions.index')
            ->with('success', 'Vendor subscription deleted successfully');
    }

    public function extend(Request $request, VendorSubscription $vendorSubscription): RedirectResponse
    {
        $validated = $request->validate([
            'extend_by' => 'required|integer|min:1|max:12',
            'extend_type' => 'required|in:days,months,years',
        ]);

        $currentEndDate = Carbon::parse($vendorSubscription->ends_at);

        $newEndDate = match($validated['extend_type']) {
            'days' => $currentEndDate->addDays($validated['extend_by']),
            'months' => $currentEndDate->addMonths($validated['extend_by']),
            'years' => $currentEndDate->addYears($validated['extend_by']),
        };

        $vendorSubscription->update(['ends_at' => $newEndDate]);

        return redirect()
            ->back()
            ->with('success', "Subscription extended by {$validated['extend_by']} {$validated['extend_type']}");
    }
}
