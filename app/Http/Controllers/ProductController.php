<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\ArchiveProduct;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductHsn;
use App\Models\ProductUnit;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Log;

final class ProductController extends Controller
{
    public function index(Request $request)
    {
        $companyId = auth()->user()->current_company_id;

        $query = Product::where('company_id', $companyId)->with(['categories', 'hsn', 'unit']);

        // Search
        if ($request->search) {
            $query->where('name', 'like', "%{$request->search}%")
                ->orWhere('sku', 'like', "%{$request->search}%");
        }

        // Category Filter
        if ($request->category && $request->category !== 'all') {
            $query->whereHas('categories', function ($q) use ($request) {
                $q->where('product_categories.id', $request->category);
            });
        }

        // Status Filter
        if ($request->status && $request->status !== 'all') {
            $query->where('is_active', $request->status === 'active');
        }

        // Sorting
        $sortField     = $request->sort      ?? 'id';
        $sortDirection = $request->direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $products = $query->paginate(20)->withQueryString();

        return Inertia::render('products/index', [
            'products'   => $products,
            'categories' => ProductCategory::where('company_id', $companyId)->get(['id', 'name', 'parent_id']),
            'filters'    => [
                'search'    => $request->search,
                'category'  => $request->category,
                'status'    => $request->status,
                'sort'      => $sortField,
                'direction' => $sortDirection,
            ],
        ]);
    }

    public function create()
    {
        $companyId = auth()->user()->current_company_id;

        return Inertia::render('products/create', [
            'categories' => ProductCategory::where('company_id', $companyId)->get(['id', 'name', 'parent_id']),
            'units'      => ProductUnit::where('company_id', $companyId)->get(['id', 'name']),
            'hsns'       => ProductHsn::where('company_id', $companyId)->get(['id', 'code', 'description']),
        ]);
    }

    public function store(Request $request)
    {
        $companyId = auth()->user()->current_company_id;

        $validated = $request->validate([
            'name'         => 'required|string|max:255',
            'sku'          => 'required|string|unique:products,sku',
            'price'        => 'required|numeric|min:0',
            'stock'        => 'required|integer|min:0',
            'hsn_id'       => 'required|exists:product_hsns,id',
            'unit_id'      => 'required|exists:product_units,id',
            'categories'   => 'required|array|min:1',
            'categories.*' => 'exists:product_categories,id',
            'is_active'    => 'boolean',
            'is_taxable'   => 'boolean',
            'description'  => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            // Extract categories from validated data
            $categories = $validated['categories'];
            unset($validated['categories']);
            $validated['company_id'] = $companyId;

            // Create product
            $product = Product::create($validated);

            // Attach categories
            $product->categories()->attach(array_fill_keys($categories, ['company_id' => $companyId]));

            DB::commit();

            return back()->with('message', 'Product created successfully');
        } catch (Exception $e) {
            Log::error($e->getMessage());
            DB::rollBack();

            return back()->with('error', 'Failed to create product: '.$e->getMessage());
        }
    }

    public function edit(Product $product)
    {
        $companyId = auth()->user()->current_company_id;
        $product->load('categories')->where('company_id', $companyId)->firstOrFail();

        return Inertia::render('products/edit', [
            'product'    => $product,
            'categories' => ProductCategory::where('company_id', $companyId)->get(['id', 'name', 'parent_id']),
            'units'      => ProductUnit::where('company_id', $companyId)->get(['id', 'name']),
            'hsns'       => ProductHsn::where('company_id', $companyId)->get(['id', 'code', 'description']),
        ]);
    }

    public function update(Request $request, Product $product)
    {
        $validated = $request->validate([
            'name'         => 'required|string|max:255',
            'sku'          => 'required|string|unique:products,sku,'.$product->id,
            'price'        => 'required|numeric|min:0',
            'stock'        => 'required|integer|min:0',
            'hsn_id'       => 'required|exists:product_hsns,id',
            'unit_id'      => 'required|exists:product_units,id',
            'categories'   => 'required|array|min:1',
            'categories.*' => 'exists:product_categories,id',
            'is_active'    => 'boolean',
            'is_taxable'   => 'boolean',
            'description'  => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $product->update($validated);
            $product->categories()->sync(array_fill_keys($validated['categories'], ['company_id' => $product->company_id]));
            DB::commit();

            return back()->with('message', 'Product updated successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return back()->with('error', 'Failed to update product');
        }
    }

    public function destroy(Request $request, Product $product)
    {
        DB::beginTransaction();
        try {
            // Create archive record
            $archiveProduct = ArchiveProduct::create([
                'company_id'     => $product->company_id,
                'original_id'    => $product->id,
                'name'           => $product->name,
                'sku'            => $product->sku,
                'price'          => $product->price,
                'stock'          => $product->stock,
                'hsn_id'         => $product->hsn_id,
                'unit_id'        => $product->unit_id,
                'is_active'      => $product->is_active,
                'is_taxable'     => $product->is_taxable,
                'description'    => $product->description,
                'archived_by'    => $request->user()->id,
                'archive_reason' => $request->reason ?? 'Product archived',
            ]);

            // Archive product categories
            $categoryIds = $product->categories->pluck('id')->toArray();
            $archiveProduct->categories()->attach($categoryIds);

            // Hard delete the original product and its relationships
            $product->categories()->detach();
            $product->forceDelete();

            DB::commit();

            return redirect()->route('products.index')
                ->with('success', 'Product archived successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return back()->with('error', 'Failed to archive product: '.$e->getMessage());
        }
    }
}
