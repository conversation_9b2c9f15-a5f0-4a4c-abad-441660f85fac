<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Country;
use App\Models\State;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

final class StateController extends Controller
{
    public function index(Request $request)
    {
        // Get current user's company ID
        $companyId = auth()->user()->current_company_id;

        $query = State::query()
            ->with(['country'])
            ->withCount(['cities'])
            ->where('company_id', $companyId);

        // Apply filters
        if ($request->search) {
            $query->search($request->search);
        }

        if ($request->status && $request->status !== 'all') {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->country_id) {
            $query->where('country_id', $request->country_id);
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'id';
        $sortDirection = $request->direction ?? 'desc';
        $query->sort($sortField, $sortDirection);

        $states = $query->paginate(10)
            ->withQueryString();

        // Get all countries for filtering
        $countries = Country::where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name']);

        return Inertia::render('locations/states/index', [
            'states'    => $states,
            'countries' => $countries,
            'filters'   => $request->only(['search', 'status', 'country_id', 'sort', 'direction']),
        ]);
    }

    public function create()
    {
        $companyId = auth()->user()->current_company_id;
        $countries = Country::where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name']);

        return Inertia::render('locations/states/create', [
            'countries' => $countries,
        ]);
    }

    public function store(Request $request)
    {
        $companyId = auth()->user()->current_company_id;

        // Check if a soft-deleted record with the same name or code exists
        $existingDeleted = State::onlyTrashed()
            ->where('company_id', $companyId)
            ->where('country_id', $request->country_id)
            ->where(function ($query) use ($request) {
                $query->where('name', $request->name)
                    ->orWhere('code', $request->code);
            })
            ->first();

        if ($existingDeleted) {
            // Restore the soft-deleted record and update it
            $existingDeleted->restore();

            $existingDeleted->update([
                'name'       => $request->name,
                'code'       => $request->code,
                'country_id' => $request->country_id,
                'is_active'  => $request->boolean('is_active', true),
                'company_id' => $companyId,
            ]);

            return redirect()
                ->route('locations.states.index')
                ->with('success', 'State restored and updated successfully');
        }

        // If no soft-deleted record exists, proceed with normal validation and creation
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255',
                Rule::unique('states')->where(function ($query) use ($request, $companyId) {
                    return $query->where('company_id', $companyId)
                        ->where('country_id', $request->country_id);
                }),
            ],
            'code' => ['required', 'string', 'max:5',
                Rule::unique('states')->where(function ($query) use ($request, $companyId) {
                    return $query->where('company_id', $companyId)
                        ->where('country_id', $request->country_id);
                }),
            ],
            'country_id' => ['required', 'exists:countries,id'],
            'is_active'  => ['boolean'],
        ]);

        $validated['company_id'] = $companyId;

        State::create($validated);

        return redirect()
            ->route('locations.states.index')
            ->with('success', 'State created successfully');
    }

    public function edit(State $state)
    {
        $companyId = auth()->user()->current_company_id;
        $state->where('company_id', $companyId)->firstOrFail();

        $countries = Country::where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name']);

        return Inertia::render('locations/states/edit', [
            'state'     => $state,
            'countries' => $countries,
        ]);
    }

    public function update(Request $request, State $state)
    {
        $companyId = auth()->user()->current_company_id;

        // Check if we're trying to update to a name or code that exists in a soft-deleted record
        $existingDeleted = State::onlyTrashed()
            ->where('company_id', $companyId)
            ->where('country_id', $request->country_id)
            ->where('id', '!=', $state->id)
            ->where(function ($query) use ($request) {
                $query->where('name', $request->name)
                    ->orWhere('code', $request->code);
            })
            ->first();

        if ($existingDeleted) {
            // Restore the soft-deleted record and update it with the current data
            $existingDeleted->restore();

            $existingDeleted->update([
                'name'       => $request->name,
                'code'       => $request->code,
                'country_id' => $request->country_id,
                'is_active'  => $request->boolean('is_active', true),
            ]);

            // Delete the current state as we're using the restored one instead
            $state->delete();

            return redirect()
                ->route('locations.states.index')
                ->with('success', 'State restored and updated successfully');
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255',
                Rule::unique('states')->where(function ($query) use ($request, $companyId) {
                    return $query->where('company_id', $companyId)
                        ->where('country_id', $request->country_id);
                })->ignore($state->id),
            ],
            'code' => ['required', 'string', 'max:5',
                Rule::unique('states')->where(function ($query) use ($request, $companyId) {
                    return $query->where('company_id', $companyId)
                        ->where('country_id', $request->country_id);
                })->ignore($state->id),
            ],
            'country_id' => ['required', 'exists:countries,id'],
            'is_active'  => ['boolean'],
        ]);

        $state->update($validated);

        return redirect()
            ->route('locations.states.index')
            ->with('success', 'State updated successfully');
    }

    public function destroy(State $state)
    {
        $companyId = auth()->user()->current_company_id;
        $state->where('company_id', $companyId)->firstOrFail();

        // Check if state has cities
        if ($state->cities()->count() > 0) {
            return back()->with('error', 'Cannot delete state with cities');
        }

        $state->delete();

        return back()->with('success', 'State deleted successfully');
    }
}
