<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

final class ServiceController extends Controller
{
    /**
     * List all active services for the current branch
     */
    public function index(Request $request)
    {
        $branchId = Auth::user()->current_branch_id;

        $query = Service::where('branch_id', $branchId)
            ->where('is_active', true);

        // Apply search filter if provided
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply duration filter if provided
        if ($request->has('min_duration')) {
            $query->where('duration_minutes', '>=', $request->input('min_duration'));
        }

        if ($request->has('max_duration')) {
            $query->where('duration_minutes', '<=', $request->input('max_duration'));
        }

        // Apply price filter if provided
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->input('min_price'));
        }

        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->input('max_price'));
        }

        // Apply sorting
        $sortField     = $request->input('sort', 'name');
        $sortDirection = $request->input('direction', 'asc');

        // Validate sort field to prevent SQL injection
        $allowedSortFields = ['name', 'duration_minutes', 'price'];
        if (! in_array($sortField, $allowedSortFields)) {
            $sortField = 'name';
        }

        $query->orderBy($sortField, $sortDirection === 'desc' ? 'desc' : 'asc');

        // Get services
        $services = $query->get()->map(function ($service) {
            return [
                'id'               => $service->id,
                'name'             => $service->name,
                'description'      => $service->description,
                'duration_minutes' => $service->duration_minutes,
                'price'            => $service->price,
            ];
        });

        return response()->json([
            'services' => $services,
        ]);
    }
}
