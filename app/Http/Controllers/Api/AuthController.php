<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

final class AuthController extends Controller
{
    public function login(Request $request)
    {
        $request->validate([
            'email'    => 'required|email',
            'password' => 'required',
        ]);

        $user = User::where('email', $request->email)->first();

        if (! $user || ! Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        // Check if user has a role
        if (! $user->hasAnyRole(['admin', 'staff', 'customer'])) {
            return response()->json([
                'message' => 'User does not have required permissions',
            ], 403);
        }

        // Get user's roles
        $roles = $user->getRoleNames();

        return response()->json([
            'user'  => $user,
            'roles' => $roles,
            'token' => $user->createToken('api-token')->plainTextToken,
        ]);
    }
}
