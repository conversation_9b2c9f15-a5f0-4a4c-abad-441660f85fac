<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Appointment;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

final class AppointmentController extends Controller
{
    /**
     * Get today's appointments
     */
    public function todayAppointments(Request $request)
    {
        $branchId = Auth::user()->current_branch_id;
        $today    = Carbon::today();

        $appointments = Appointment::with(['user', 'services', 'services'])
            ->where('branch_id', $branchId)
            ->whereDate('appointment_date', $today)
            ->orderBy('appointment_time')
            ->get()
            ->map(function ($appointment) {
                return $this->formatAppointment($appointment);
            });

        return response()->json([
            'appointments' => $appointments,
        ]);
    }

    /**
     * Get upcoming appointments (next 7 days)
     */
    public function upcomingAppointments(Request $request)
    {
        $branchId = Auth::user()->current_branch_id;
        $today    = Carbon::today();
        $nextWeek = Carbon::today()->addDays(7);

        $appointments = Appointment::with(['user', 'services', 'services'])
            ->where('branch_id', $branchId)
            ->whereDate('appointment_date', '>=', $today)
            ->whereDate('appointment_date', '<=', $nextWeek)
            ->orderBy('appointment_date')
            ->orderBy('appointment_time')
            ->get()
            ->map(function ($appointment) {
                return $this->formatAppointment($appointment);
            });

        return response()->json([
            'appointments' => $appointments,
        ]);
    }

    /**
     * Get current user's appointments
     */
     public function myAppointments(Request $request)
    {
        $userId = Auth::id();

        $appointments = Appointment::with(['services', 'branch'])
            ->where('user_id', $userId)
            ->orderBy('appointment_date', 'desc')
            ->orderBy('appointment_time')
            ->get()
            ->map(function ($appointment) {
                return $this->formatAppointment($appointment, true);
            });

        return response()->json([
            'appointments' => $appointments,
        ]);
    }

    /**
     * Update appointment status
     */
    public function updateStatus(Request $request, Appointment $appointment)
    {
        $branchId = Auth::user()->current_branch_id;

        // Verify appointment belongs to user's branch
        if ($appointment->branch_id !== $branchId) {
            return response()->json([
                'message' => 'Unauthorized access to this appointment',
            ], 403);
        }

        $validated = $request->validate([
            'status' => 'required|in:pending,in_progress,completed,cancelled',
        ]);

        $appointment->update([
            'status' => $validated['status'],
        ]);

        return response()->json([
            'message'     => 'Appointment status updated successfully',
            'appointment' => $this->formatAppointment($appointment->fresh(['user', 'services'])),
        ]);
    }

    /**
     * Create a new appointment
     */
    public function store(Request $request)
    {
        $userId   = Auth::id();
        $branchId = Auth::user()->current_branch_id;

        $validated = $request->validate([
            'appointment_date' => 'required|date|after_or_equal:today',
            'appointment_time' => 'required',
            'services'         => 'required|array|min:1',
            'services.*.id'    => 'required|exists:services,id',
            'notes'            => 'nullable|string|max:500',
        ]);

        // Generate ticket number
        $ticketNumber = mt_rand(1000, 9999);

        // Create appointment
        $appointment = Appointment::create([
            'user_id'          => $userId,
            'branch_id'        => $branchId,
            'appointment_date' => $validated['appointment_date'],
            'appointment_time' => $validated['appointment_time'],
            'ticket_number'    => $ticketNumber,
            'status'           => 'pending',
            'notes'            => $validated['notes'] ?? null,
        ]);

        // Attach services
        foreach ($validated['services'] as $service) {
            $appointment->services()->attach($service['id'], [
                'status'     => 'pending',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Load relationships for response
        $appointment->load(['services', 'services', 'branch']);

        return response()->json([
            'message'     => 'Appointment created successfully',
            'appointment' => $this->formatAppointment($appointment, true),
        ], 201);
    }

    /**
     * Format appointment data for API response
     */
    private function formatAppointment($appointment, $includesBranch = false)
    {
        $formattedAppointment = [
            'id'               => $appointment->id,
            'appointment_date' => $this->formatDate($appointment->appointment_date),
            'appointment_time' => $appointment->appointment_time,
            'ticket_number'    => $appointment->ticket_number,
            'status'           => $appointment->status,
            'notes'            => $appointment->notes,
            'services'         => $appointment->services->map(function ($service) {
                return [
                    'id'               => $service->id,
                    'name'             => $service->name,
                    'duration_minutes' => $service->duration_minutes,
                    'price'            => $service->price,
                    'status'           => $service->pivot->status ?? 'pending',
                    'start_time'       => $service->pivot->start_time ? $this->formatDateTime($service->pivot->start_time) : null,
                    'end_time'         => $service->pivot->end_time ? $this->formatDateTime($service->pivot->end_time) : null,
                ];
            }),
        ];

        // Include user data if available
        if ($appointment->relationLoaded('user')) {
            $formattedAppointment['user'] = [
                'id'    => $appointment->user->id,
                'name'  => $appointment->user->name,
                'email' => $appointment->user->email,
                'phone' => $appointment->user->phone,
            ];
        }

        // Include branch data if requested
        if ($includesBranch && $appointment->relationLoaded('branch')) {
            $formattedAppointment['branch'] = [
                'id'   => $appointment->branch->id,
                'name' => $appointment->branch->name,
            ];
        }

        return $formattedAppointment;
    }

    /**
     * Format date to Y-m-d format
     */
    private function formatDate($date)
    {
        if ($date instanceof \Carbon\Carbon) {
            return $date->format('Y-m-d');
        }

        if (is_string($date)) {
            return $date;
        }

        return null;
    }

    /**
     * Format datetime to Y-m-d H:i:s format
     */
    private function formatDateTime($datetime)
    {
        if ($datetime instanceof \Carbon\Carbon) {
            return $datetime->format('Y-m-d H:i:s');
        }

        if (is_string($datetime)) {
            return $datetime;
        }

        return null;
    }
}
