<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\Seat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

final class SeatController extends Controller
{
    /**
     * List all seats for admin/staff
     */
    public function index(Request $request)
    {
        $branchId = Auth::user()->current_branch_id;

        $query = Seat::with('staff')
            ->where('branch_id', $branchId);

        // Apply status filter if provided
        if ($request->has('status') && in_array($request->status, ['available', 'occupied', 'cleaning', 'maintenance'])) {
            $query->where('status', $request->status);
        }

        // Apply search filter if provided
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('notes', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortField     = $request->input('sort', 'name');
        $sortDirection = $request->input('direction', 'asc');

        // Validate sort field to prevent SQL injection
        $allowedSortFields = ['name', 'status'];
        if (! in_array($sortField, $allowedSortFields)) {
            $sortField = 'name';
        }

        $query->orderBy($sortField, $sortDirection === 'desc' ? 'desc' : 'asc');

        // Get seats
        $seats = $query->get()->map(function ($seat) {
            return [
                'id'     => $seat->id,
                'name'   => $seat->name,
                'status' => $seat->status,
                'notes'  => $seat->notes,
                'staff'  => $seat->staff ? [
                    'id'   => $seat->staff->id,
                    'name' => $seat->staff->name,
                ] : null,
            ];
        });

        return response()->json([
            'seats' => $seats,
        ]);
    }

    /**
     * Public seat map - accessible without authentication
     */
    public function map(Request $request)
    {
        // If branch_id is provided, use it; otherwise use the first active branch
        $branchId = $request->input('branch_id');

        if (! $branchId) {
            $branch = Branch::where('is_active', true)->first();
            if (! $branch) {
                return response()->json([
                    'message' => 'No active branches found',
                ], 404);
            }
            $branchId = $branch->id;
        }

        $seats = Seat::where('branch_id', $branchId)
            ->orderBy('name')
            ->get(['id', 'name', 'status', 'notes']);

        $branch = Branch::find($branchId, ['id', 'name', 'address']);

        return response()->json([
            'branch' => $branch,
            'seats'  => $seats,
        ]);
    }

    /**
     * Update seat status - admin/staff only
     */
    public function updateStatus(Request $request, Seat $seat)
    {
        $branchId = Auth::user()->current_branch_id;

        // Verify seat belongs to user's branch
        if ($seat->branch_id !== $branchId) {
            return response()->json([
                'message' => 'Unauthorized access to this seat',
            ], 403);
        }

        $validated = $request->validate([
            'status' => 'required|in:available,occupied,cleaning,maintenance',
            'notes'  => 'nullable|string|max:500',
        ]);

        $seat->update([
            'status' => $validated['status'],
            'notes'  => $validated['notes'] ?? $seat->notes,
        ]);

        return response()->json([
            'message' => 'Seat status updated successfully',
            'seat'    => [
                'id'     => $seat->id,
                'name'   => $seat->name,
                'status' => $seat->status,
                'notes'  => $seat->notes,
                'staff'  => $seat->staff ? [
                    'id'   => $seat->staff->id,
                    'name' => $seat->staff->name,
                ] : null,
            ],
        ]);
    }
}
