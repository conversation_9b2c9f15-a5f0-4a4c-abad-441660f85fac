<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

final class CountryController extends Controller
{
    public function index(Request $request)
    {
        // Get current user's company ID
        $companyId = auth()->user()->current_company_id;

        $query = Country::query()
            ->withCount(['states'])
            ->where('company_id', $companyId);

        // Apply filters
        if ($request->search) {
            $query->search($request->search);
        }

        if ($request->status && $request->status !== 'all') {
            $query->where('is_active', $request->status === 'active');
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'id';
        $sortDirection = $request->direction ?? 'desc';
        $query->sort($sortField, $sortDirection);

        $countries = $query->paginate(10)
            ->withQueryString();

        return Inertia::render('locations/countries/index', [
            'countries' => $countries,
            'filters'   => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    public function create()
    {
        return Inertia::render('locations/countries/create');
    }

    public function store(Request $request)
    {
        $companyId = auth()->user()->current_company_id;

        // Check if a soft-deleted record with the same name or code exists
        $existingDeleted = Country::onlyTrashed()
            ->where('company_id', $companyId)
            ->where(function ($query) use ($request) {
                $query->where('name', $request->name)
                    ->orWhere('code', $request->code);
            })
            ->first();

        if ($existingDeleted) {
            // Restore the soft-deleted record and update it
            $existingDeleted->restore();

            $existingDeleted->update([
                'name'       => $request->name,
                'code'       => $request->code,
                'is_active'  => $request->boolean('is_active', true),
                'company_id' => $companyId,
            ]);

            return redirect()
                ->route('locations.countries.index')
                ->with('success', 'Country restored and updated successfully');
        }

        // If no soft-deleted record exists, proceed with normal validation and creation
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255',
                Rule::unique('countries')->where(function ($query) use ($companyId) {
                    return $query->where('company_id', $companyId);
                }),
            ],
            'code' => ['required', 'string', 'max:10',
                Rule::unique('countries')->where(function ($query) use ($companyId) {
                    return $query->where('company_id', $companyId);
                }),
            ],
            'is_active' => ['boolean'],
        ]);

        $validated['company_id'] = $companyId;

        Country::create($validated);

        return redirect()
            ->route('locations.countries.index')
            ->with('success', 'Country created successfully');
    }

    public function edit(Country $country)
    {
        $companyId = auth()->user()->current_company_id;
        $country->where('company_id', $companyId)->firstOrFail();

        return Inertia::render('locations/countries/edit', [
            'country' => $country,
        ]);
    }

    public function update(Request $request, Country $country)
    {
        $companyId = auth()->user()->current_company_id;

        // Check if we're trying to update to a name or code that exists in a soft-deleted record
        $existingDeleted = Country::onlyTrashed()
            ->where('company_id', $companyId)
            ->where('id', '!=', $country->id)
            ->where(function ($query) use ($request) {
                $query->where('name', $request->name)
                    ->orWhere('code', $request->code);
            })
            ->first();

        if ($existingDeleted) {
            // Restore the soft-deleted record and update it with the current data
            $existingDeleted->restore();

            $existingDeleted->update([
                'name'      => $request->name,
                'code'      => $request->code,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Delete the current country as we're using the restored one instead
            $country->delete();

            return redirect()
                ->route('locations.countries.index')
                ->with('success', 'Country restored and updated successfully');
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255',
                Rule::unique('countries')->where(function ($query) use ($companyId) {
                    return $query->where('company_id', $companyId);
                })->ignore($country->id),
            ],
            'code' => ['required', 'string', 'max:10',
                Rule::unique('countries')->where(function ($query) use ($companyId) {
                    return $query->where('company_id', $companyId);
                })->ignore($country->id),
            ],
            'is_active' => ['boolean'],
        ]);

        $country->update($validated);

        return redirect()
            ->route('locations.countries.index')
            ->with('success', 'Country updated successfully');
    }

    public function destroy(Country $country)
    {
        $companyId = auth()->user()->current_company_id;
        $country->where('company_id', $companyId)->firstOrFail();

        // Check if country has states
        if ($country->states()->count() > 0) {
            return back()->with('error', 'Cannot delete country with states');
        }

        $country->delete();

        return back()->with('success', 'Country deleted successfully');
    }
}
