<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

final class StaffController extends Controller
{
    public function index(Request $request): Response
    {
        $branchId = auth()->user()->current_branch_id;

        $query = User::role('staff')
            ->where('branch_id', $branchId);

        // Apply search filter
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                    ->orWhere('email', 'like', "%{$request->search}%")
                    ->orWhere('phone', 'like', "%{$request->search}%");
            });
        }

        // Apply status filter
        if ($request->status) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'name';
        $sortDirection = $request->direction ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        $staff = $query->paginate(10)
            ->withQueryString()
            ->through(fn ($user) => [
                'id'        => $user->id,
                'name'      => $user->name,
                'email'     => $user->email,
                'phone'     => $user->phone,
                'gender'    => $user->gender,
                'is_active' => $user->is_active,
            ]);

        return Inertia::render('staff/index', [
            'staff'   => $staff,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    public function create(): Response
    {
        return Inertia::render('staff/create');
    }

    public function store(Request $request)
    {
        $branchId = auth()->user()->current_branch_id;

        $validated = $request->validate([
            'name'      => 'required|string|max:255',
            'email'     => 'required|email|unique:users,email|max:255',
            'phone'     => 'required|string|max:20',
            'gender'    => 'required|in:male,female,other',
            'address'   => 'nullable|string|max:500',
            'password'  => 'required|min:8|confirmed',
            'is_active' => 'boolean',
        ]);

        $validated['branch_id']         = $branchId;
        $validated['current_branch_id'] = $branchId;
        $validated['is_active']         = $validated['is_active'] ?? true;
        $validated['password']          = Hash::make($validated['password']);

        // Create the user
        $user = User::create($validated);

        // Assign the staff role
        $user->assignRole('staff');

        return redirect()
            ->route('staff.index')
            ->with('success', 'Staff member created successfully');
    }

    public function edit(User $user): Response
    {
        $branchId = auth()->user()->current_branch_id;

        // Ensure the staff belongs to the current branch
        if ($user->branch_id !== $branchId || ! $user->hasRole('staff')) {
            abort(403, 'Unauthorized action.');
        }

        return Inertia::render('staff/edit', [
            'staff' => $user,
        ]);
    }

    public function update(Request $request, User $user)
    {
        $branchId = auth()->user()->current_branch_id;

        // Ensure the staff belongs to the current branch
        if ($user->branch_id !== $branchId || ! $user->hasRole('staff')) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name'      => 'required|string|max:255',
            'email'     => 'required|string|email|max:255|unique:users,email,'.$user->id,
            'phone'     => 'required|string|max:20',
            'gender'    => 'required|in:male,female,other',
            'address'   => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        // Password is optional on update
        if ($request->filled('password')) {
            $request->validate([
                'password' => ['required', 'confirmed', Rules\Password::defaults()],
            ]);
            $validated['password'] = Hash::make($request->password);
        }

        $validated['is_active'] = $validated['is_active'] ?? true;

        $user->update($validated);

        return redirect()
            ->route('staff.index')
            ->with('success', 'Staff member updated successfully');
    }

    public function destroy(User $user)
    {
        $branchId = auth()->user()->current_branch_id;

        // Ensure the staff belongs to the current branch
        if ($user->branch_id !== $branchId || ! $user->hasRole('staff')) {
            abort(403, 'Unauthorized action.');
        }

        $user->delete();

        return redirect()
            ->route('staff.index')
            ->with('success', 'Staff member deleted successfully');
    }

    public function show(User $user): Response
    {
        $branchId = auth()->user()->current_branch_id;

        // Ensure the staff belongs to the current branch
        if ($user->branch_id !== $branchId || ! $user->hasRole('staff')) {
            abort(403, 'Unauthorized action.');
        }

        return Inertia::render('staff/show', [
            'staff' => $user,
        ]);
    }
}
