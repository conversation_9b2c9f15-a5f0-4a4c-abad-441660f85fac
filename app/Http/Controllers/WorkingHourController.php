<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\WorkingHour;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

final class WorkingHourController extends Controller
{
    public function index(Request $request): Response
    {
        $branchId = auth()->user()->current_branch_id;

        $workingHours = WorkingHour::where('branch_id', $branchId)
            ->orderBy('day_order')
            ->get();

        return Inertia::render('working-hours/index', [
            'workingHours' => $workingHours,
        ]);
    }

    public function edit(): Response
    {
        $branchId = auth()->user()->current_branch_id;

        $workingHours = WorkingHour::where('branch_id', $branchId)
            ->orderBy('day_order')
            ->get();

        return Inertia::render('working-hours/edit', [
            'workingHours' => $workingHours,
        ]);
    }

    public function update(Request $request)
    {
        $branchId = auth()->user()->current_branch_id;

        $validated = $request->validate([
            'hours'              => 'required|array',
            'hours.*.id'         => 'required|exists:working_hours,id',
            'hours.*.open_time'  => 'required_if:hours.*.is_closed,false',
            'hours.*.close_time' => 'required_if:hours.*.is_closed,false',
            'hours.*.is_closed'  => 'boolean',
        ]);

        foreach ($validated['hours'] as $hour) {
            $workingHour = WorkingHour::where('id', $hour['id'])
                ->where('branch_id', $branchId)
                ->firstOrFail();

            if ($hour['is_closed']) {
                // For closed days, keep the existing times but mark as closed
                $workingHour->update([
                    'is_closed' => true,
                ]);
            } else {
                // For open days, update all fields
                $workingHour->update([
                    'open_time'  => $hour['open_time'],
                    'close_time' => $hour['close_time'],
                    'is_closed'  => false,
                ]);
            }
        }

        return redirect()->route('working-hours.index')
            ->with('success', 'Working hours updated successfully');
    }
}
