<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

final class SubscriptionPlanController extends Controller
{
    public function index(Request $request): Response
    {
        $query = SubscriptionPlan::query();

        // Apply search filter
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                    ->orWhere('description', 'like', "%{$request->search}%");
            });
        }

        // Apply status filter
        if ($request->status !== null) {
            $query->where('is_active', $request->status === 'active');
        }

        $plans = $query->withCount(['vendorSubscriptions', 'activeVendorSubscriptions'])
            ->ordered()
            ->paginate(10)
            ->withQueryString()
            ->through(function ($plan) {
                return [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'description' => $plan->description,
                    'price' => $plan->price,
                    'billing_cycle' => $plan->billing_cycle,
                    'max_services' => $plan->max_services,
                    'max_appointments_per_month' => $plan->max_appointments_per_month,
                    'max_seats' => $plan->max_seats,
                    'max_branches' => $plan->max_branches,
                    'max_staff' => $plan->max_staff,
                    'has_analytics' => $plan->has_analytics,
                    'has_api_access' => $plan->has_api_access,
                    'has_custom_branding' => $plan->has_custom_branding,
                    'has_priority_support' => $plan->has_priority_support,
                    'is_active' => $plan->is_active,
                    'vendor_subscriptions_count' => $plan->vendor_subscriptions_count,
                    'active_vendor_subscriptions_count' => $plan->active_vendor_subscriptions_count,
                ];
            });

        return Inertia::render('admin/subscription-plans/index', [
            'plans' => $plans,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    public function create(): Response
    {
        return Inertia::render('admin/subscription-plans/create');
    }

    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,yearly',
            'max_services' => 'required|integer|min:0',
            'max_appointments_per_month' => 'required|integer|min:0',
            'max_seats' => 'required|integer|min:0',
            'max_branches' => 'required|integer|min:1',
            'max_staff' => 'required|integer|min:0',
            'has_analytics' => 'boolean',
            'has_api_access' => 'boolean',
            'has_custom_branding' => 'boolean',
            'has_priority_support' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $validated['has_analytics'] = $validated['has_analytics'] ?? false;
        $validated['has_api_access'] = $validated['has_api_access'] ?? false;
        $validated['has_custom_branding'] = $validated['has_custom_branding'] ?? false;
        $validated['has_priority_support'] = $validated['has_priority_support'] ?? false;
        $validated['is_active'] = $validated['is_active'] ?? true;
        $validated['sort_order'] = $validated['sort_order'] ?? 0;

        SubscriptionPlan::create($validated);

        return redirect()
            ->route('admin.subscription-plans.index')
            ->with('success', 'Subscription plan created successfully');
    }

    public function show(SubscriptionPlan $subscriptionPlan): Response
    {
        $subscriptionPlan->load(['vendorSubscriptions.user']);

        return Inertia::render('admin/subscription-plans/show', [
            'plan' => $subscriptionPlan,
            'subscriptions' => $subscriptionPlan->vendorSubscriptions()
                ->with(['user'])
                ->latest()
                ->paginate(10),
        ]);
    }

    public function edit(SubscriptionPlan $subscriptionPlan): Response
    {
        return Inertia::render('admin/subscription-plans/edit', [
            'plan' => $subscriptionPlan,
        ]);
    }

    public function update(Request $request, SubscriptionPlan $subscriptionPlan): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,yearly',
            'max_services' => 'required|integer|min:0',
            'max_appointments_per_month' => 'required|integer|min:0',
            'max_seats' => 'required|integer|min:0',
            'max_branches' => 'required|integer|min:1',
            'max_staff' => 'required|integer|min:0',
            'has_analytics' => 'boolean',
            'has_api_access' => 'boolean',
            'has_custom_branding' => 'boolean',
            'has_priority_support' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $validated['has_analytics'] = $validated['has_analytics'] ?? false;
        $validated['has_api_access'] = $validated['has_api_access'] ?? false;
        $validated['has_custom_branding'] = $validated['has_custom_branding'] ?? false;
        $validated['has_priority_support'] = $validated['has_priority_support'] ?? false;
        $validated['is_active'] = $validated['is_active'] ?? true;
        $validated['sort_order'] = $validated['sort_order'] ?? 0;

        $subscriptionPlan->update($validated);

        return redirect()
            ->route('admin.subscription-plans.index')
            ->with('success', 'Subscription plan updated successfully');
    }

    public function destroy(SubscriptionPlan $subscriptionPlan): RedirectResponse
    {
        // Check if plan has active subscriptions
        if ($subscriptionPlan->activeVendorSubscriptions()->exists()) {
            return redirect()
                ->route('admin.subscription-plans.index')
                ->with('error', 'Cannot delete plan with active subscriptions');
        }

        $subscriptionPlan->delete();

        return redirect()
            ->route('admin.subscription-plans.index')
            ->with('success', 'Subscription plan deleted successfully');
    }
}
