<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

final class AppointmentController extends Controller
{
    public function index(Request $request): Response
    {
        $branchId = auth()->user()->current_branch_id;

        $query = Appointment::with(['user', 'services'])
            ->where('branch_id', $branchId);

        // Apply search filter
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->whereHas('user', function ($subq) use ($request) {
                    $subq->where('name', 'like', "%{$request->search}%")
                        ->orWhere('email', 'like', "%{$request->search}%")
                        ->orWhere('phone', 'like', "%{$request->search}%");
                })
                    ->orWhere('ticket_number', 'like', "%{$request->search}%");
            });
        }

        // Apply status filter
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Apply date filter
        if ($request->date) {
            $query->whereDate('appointment_date', $request->date);
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'appointment_date';
        $sortDirection = $request->direction ?? 'desc';

        // Handle relationship sorting
        if ($sortField === 'user.name') {
            $query->join('users', 'appointments.user_id', '=', 'users.id')
                ->orderBy('users.name', $sortDirection)
                ->select('appointments.*');
        } else {
            $query->orderBy($sortField, $sortDirection);
        }

        $appointments = $query->paginate(10)
            ->withQueryString()
            ->through(function ($appointment) {
                // Get services from appointment_service pivot table
                $services = $appointment->services()->get()->map(function ($service) {
                    return [
                        'id'               => $service->id,
                        'name'             => $service->name,
                        'duration_minutes' => $service->duration_minutes,
                        'price'            => $service->price,
                    ];
                });

                return [
                    'id'   => $appointment->id,
                    'user' => [
                        'id'    => $appointment->user->id,
                        'name'  => $appointment->user->name,
                        'email' => $appointment->user->email,
                        'phone' => $appointment->user->phone,
                    ],
                    'appointment_date' => $appointment->appointment_date->format('Y-m-d'),
                    'appointment_time' => $appointment->appointment_time,
                    'ticket_number'    => $appointment->ticket_number,
                    'status'           => $appointment->status,
                    'services'         => $services,
                    'notes'            => $appointment->notes,
                ];
            });

        return Inertia::render('appointments/index', [
            'appointments' => $appointments,
            'filters'      => $request->only(['search', 'status', 'date', 'sort', 'direction']),
        ]);
    }

    public function create(): Response
    {
        $branchId = auth()->user()->current_branch_id;

        // Get all active services for the current branch
        $services = Service::where('branch_id', $branchId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get()
            ->map(function ($service) {
                return [
                    'id'               => $service->id,
                    'name'             => $service->name,
                    'duration_minutes' => $service->duration_minutes,
                    'price'            => $service->price,
                ];
            });

        // Get all customers (users with customer role)
        $customers = User::role('customer')
            ->orderBy('name')
            ->get()
            ->map(function ($customer) {
                return [
                    'id'    => $customer->id,
                    'name'  => $customer->name,
                    'email' => $customer->email,
                    'phone' => $customer->phone,
                ];
            });

        return Inertia::render('appointments/create', [
            'services'  => $services,
            'customers' => $customers,
        ]);
    }

    public function store(Request $request)
    {
        $branchId = auth()->user()->current_branch_id;

        $validated = $request->validate([
            'user_id'          => 'required|exists:users,id',
            'appointment_date' => 'required|date|after_or_equal:today',
            'appointment_time' => 'required',
            'services'         => 'required|array|min:1',
            'services.*.id'    => 'required|exists:services,id',
            'notes'            => 'nullable|string|max:500',
        ]);

        // Generate ticket number
        $ticketNumber = 'APT-'.date('Ymd').'-'.mb_strtoupper(mb_substr(uniqid(), -5));

        // Create appointment
        $appointment = Appointment::create([
            'user_id'          => $validated['user_id'],
            'branch_id'        => $branchId,
            'appointment_date' => $validated['appointment_date'],
            'appointment_time' => $validated['appointment_time'],
            'ticket_number'    => $ticketNumber,
            'status'           => 'pending',
            'notes'            => $validated['notes'] ?? null,
        ]);

        // Attach services
        foreach ($validated['services'] as $service) {
            $appointment->services()->attach($service['id'], [
                'status'     => 'pending',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        return redirect()
            ->route('appointments.index')
            ->with('success', 'Appointment created successfully');
    }

    public function edit(Appointment $appointment): Response
    {
        $branchId = auth()->user()->current_branch_id;

        // Ensure appointment belongs to current branch
        if ($appointment->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        // Get all active services for the current branch
        $services = Service::where('branch_id', $branchId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get()
            ->map(function ($service) {
                return [
                    'id'               => $service->id,
                    'name'             => $service->name,
                    'duration_minutes' => $service->duration_minutes,
                    'price'            => $service->price,
                ];
            });

        // Get all customers (users with customer role)
        $customers = User::role('customer')
            ->orderBy('name')
            ->get()
            ->map(function ($customer) {
                return [
                    'id'    => $customer->id,
                    'name'  => $customer->name,
                    'email' => $customer->email,
                    'phone' => $customer->phone,
                ];
            });

        // Format appointment data
        $appointmentData = [
            'id'               => $appointment->id,
            'user_id'          => $appointment->user_id,
            'appointment_date' => $appointment->appointment_date->format('Y-m-d'),
            'appointment_time' => $appointment->appointment_time,
            'ticket_number'    => $appointment->ticket_number,
            'status'           => $appointment->status,
            'notes'            => $appointment->notes,
            'services'         => $appointment->services->map(function ($service) {
                return [
                    'id'               => $service->id,
                    'name'             => $service->name,
                    'duration_minutes' => $service->duration_minutes,
                    'price'            => $service->price,
                ];
            }),
        ];

        return Inertia::render('appointments/edit', [
            'appointment' => $appointmentData,
            'services'    => $services,
            'customers'   => $customers,
        ]);
    }

    public function update(Request $request, Appointment $appointment)
    {
        $branchId = auth()->user()->current_branch_id;

        // Ensure appointment belongs to current branch
        if ($appointment->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'user_id'          => 'required|exists:users,id',
            'appointment_date' => 'required|date',
            'appointment_time' => 'required',
            'services'         => 'required|array|min:1',
            'services.*.id'    => 'required|exists:services,id',
            'notes'            => 'nullable|string|max:500',
            'status'           => 'required|in:pending,in_progress,completed,cancelled',
        ]);

        // Update appointment
        $appointment->update([
            'user_id'          => $validated['user_id'],
            'appointment_date' => $validated['appointment_date'],
            'appointment_time' => $validated['appointment_time'],
            'notes'            => $validated['notes'] ?? null,
            'status'           => $validated['status'],
        ]);

        // Sync services
        $serviceIds = collect($validated['services'])->pluck('id')->toArray();
        $syncData   = [];

        foreach ($serviceIds as $serviceId) {
            $syncData[$serviceId] = [
                'status'     => 'pending',
                'updated_at' => now(),
            ];
        }

        $appointment->services()->sync($syncData);

        return redirect()
            ->route('appointments.index')
            ->with('success', 'Appointment updated successfully');
    }

    public function destroy(Appointment $appointment)
    {
        $branchId = auth()->user()->current_branch_id;
        $appointment->where('branch_id', $branchId)->firstOrFail();

        // Check if appointment can be deleted
        if ($appointment->status !== 'pending' && $appointment->status !== 'cancelled') {
            return redirect()
                ->route('appointments.index')
                ->with('error', 'Only pending or cancelled appointments can be deleted');
        }

        $appointment->services()->detach();
        $appointment->delete();

        return redirect()
            ->route('appointments.index')
            ->with('success', 'Appointment deleted successfully');
    }

    public function updateStatus(Request $request, Appointment $appointment)
    {
        $branchId = auth()->user()->current_branch_id;
        $appointment->where('branch_id', $branchId)->firstOrFail();

        $validated = $request->validate([
            'status' => 'required|in:pending,in_progress,completed,cancelled',
        ]);

        $appointment->update([
            'status' => $validated['status'],
        ]);

        return redirect()
            ->route('appointments.index')
            ->with('success', 'Appointment status updated successfully');
    }
}
