<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Branch;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

final class BranchController extends Controller
{
    public function index(Request $request): Response
    {
        $userId = auth()->id();

        // Load only current user's branches
        $query = Branch::where('user_id', $userId);

        // Apply search filter
        if ($request->search) {
            $query->where('name', 'like', "%{$request->search}%")
                ->orWhere('address', 'like', "%{$request->search}%")
                ->orWhere('phone', 'like', "%{$request->search}%");
        }

        // Apply status filter
        if ($request->status !== null) {
            $query->where('is_active', $request->status === 'active');
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'name';
        $sortDirection = $request->direction ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        $branches = $query->paginate(10)
            ->withQueryString();

        return Inertia::render('branches/index', [
            'branches' => $branches,
            'filters'  => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    public function create(): Response
    {
        return Inertia::render('branches/create');
    }

    public function store(Request $request)
    {
        $userId = auth()->id();

        $validated = $request->validate([
            'name'      => 'required|string|max:255',
            'address'   => 'required|string|max:500',
            'phone'     => 'required|string|max:20',
            'email'     => 'nullable|email|max:255',
            'is_active' => 'boolean',
        ]);

        $validated['user_id']   = $userId;
        $validated['is_active'] = $validated['is_active'] ?? true;

        Branch::create($validated);

        return redirect()
            ->route('branches.index')
            ->with('success', 'Branch created successfully');
    }

    public function edit(Branch $branch): Response
    {
        return Inertia::render('branches/edit', [
            'branch' => $branch,
        ]);
    }

    public function update(Request $request, Branch $branch)
    {
        $validated = $request->validate([
            'name'      => 'required|string|max:255',
            'address'   => 'required|string|max:500',
            'phone'     => 'required|string|max:20',
            'email'     => 'nullable|email|max:255',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $validated['is_active'] ?? true;

        $branch->update($validated);

        return redirect()
            ->route('branches.index')
            ->with('success', 'Branch updated successfully');
    }

    public function destroy(Branch $branch)
    {
        $userId = auth()->id();

        // At least one branch is required - check if this is the last branch for this vendor
        $branchCount = Branch::where('user_id', $userId)->count();
        if ($branchCount <= 1) {
            return redirect()
                ->route('branches.index')
                ->with('error', 'At least one branch is required. Cannot delete the last remaining branch.');
        }

        // Check if branch has any users
        if ($branch->users()->count() > 0) {
            return redirect()
                ->route('branches.index')
                ->with('error', 'Cannot delete a branch that has users');
        }

        // Check if branch has any appointments
        if ($branch->appointments()->count() > 0) {
            return redirect()
                ->route('branches.index')
                ->with('error', 'Cannot delete a branch that has appointments');
        }

        // Check if branch has any seats
        if ($branch->seats()->count() > 0) {
            return redirect()
                ->route('branches.index')
                ->with('error', 'Cannot delete a branch that has seats');
        }

        // Check if branch has any services
        if ($branch->services()->count() > 0) {
            return redirect()
                ->route('branches.index')
                ->with('error', 'Cannot delete a branch that has services');
        }

        // If this is the user's current branch, switch to another branch
        if (auth()->user()->current_branch_id === $branch->id) {
            $anotherBranch = Branch::where('user_id', $userId)
                ->where('id', '!=', $branch->id)
                ->first();

            if ($anotherBranch) {
                auth()->user()->update(['current_branch_id' => $anotherBranch->id]);
            }
        }

        $branch->delete();

        return redirect()
            ->route('branches.index')
            ->with('success', 'Branch deleted successfully');
    }

    public function trashed(Request $request): Response
    {
        $userId = auth()->id();

        // Load only current user's trashed branches
        $query = Branch::onlyTrashed()->where('user_id', $userId);

        // Apply search filter
        if ($request->search) {
            $query->where('name', 'like', "%{$request->search}%")
                ->orWhere('address', 'like', "%{$request->search}%")
                ->orWhere('phone', 'like', "%{$request->search}%");
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'deleted_at';
        $sortDirection = $request->direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $branches = $query->paginate(10)
            ->withQueryString();

        return Inertia::render('branches/trashed', [
            'branches' => $branches,
            'filters'  => $request->only(['search', 'sort', 'direction']),
        ]);
    }

    public function restore($id)
    {
        $branch = Branch::onlyTrashed()->findOrFail($id);
        $branch->restore();

        return redirect()
            ->route('branches.trashed')
            ->with('success', 'Branch restored successfully');
    }

    public function forceDelete($id)
    {
        $branch = Branch::onlyTrashed()->findOrFail($id);

        // Check if branch has any users
        if ($branch->users()->count() > 0) {
            return redirect()
                ->route('branches.trashed')
                ->with('error', 'Cannot permanently delete a branch that has users');
        }

        // Check if branch has any appointments
        if ($branch->appointments()->count() > 0) {
            return redirect()
                ->route('branches.trashed')
                ->with('error', 'Cannot permanently delete a branch that has appointments');
        }

        // Check if branch has any seats
        if ($branch->seats()->count() > 0) {
            return redirect()
                ->route('branches.trashed')
                ->with('error', 'Cannot permanently delete a branch that has seats');
        }

        // Check if branch has any services
        if ($branch->services()->count() > 0) {
            return redirect()
                ->route('branches.trashed')
                ->with('error', 'Cannot permanently delete a branch that has services');
        }

        $branch->forceDelete();

        return redirect()
            ->route('branches.trashed')
            ->with('success', 'Branch permanently deleted');
    }
}
