<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\City;
use App\Models\Country;
use App\Models\State;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

final class VendorController extends Controller
{
    public function index(Request $request)
    {
        // Get current user's company ID
        $companyId = auth()->user()->current_company_id;

        $query = Vendor::query()
            ->with(['country', 'state', 'city'])
            ->where('company_id', $companyId);

        // Apply filters
        if ($request->search) {
            $query->search($request->search);
        }

        if ($request->status && $request->status !== 'all') {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->country_id) {
            $query->where('country_id', $request->country_id);
        }

        if ($request->state_id) {
            $query->where('state_id', $request->state_id);
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'id';
        $sortDirection = $request->direction ?? 'desc';
        $query->sort($sortField, $sortDirection);

        $vendors = $query->paginate(10)
            ->withQueryString();

        // Get all countries and states for filtering
        $countries = Country::where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name']);

        $states = State::with('country')
            ->where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'country_id']);

        return Inertia::render('vendors/index', [
            'vendors'   => $vendors,
            'countries' => $countries,
            'states'    => $states,
            'filters'   => $request->only(['search', 'status', 'country_id', 'state_id', 'sort', 'direction']),
        ]);
    }

    public function create()
    {
        $companyId = auth()->user()->current_company_id;

        $countries = Country::where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name']);

        $states = State::with('country')
            ->where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'country_id']);

        $cities = City::with('state')
            ->where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'state_id']);

        return Inertia::render('vendors/create', [
            'countries' => $countries,
            'states'    => $states,
            'cities'    => $cities,
        ]);
    }

    public function store(Request $request)
    {
        $companyId = auth()->user()->current_company_id;

        // Check if a soft-deleted record with the same party_code and company_name exists
        $existingDeleted = Vendor::onlyTrashed()
            ->where('company_id', $companyId)
            ->where('party_code', $request->party_code)
            ->where('company_name', $request->company_name)
            ->first();

        if ($existingDeleted) {
            // Restore the soft-deleted record and update it
            $existingDeleted->restore();

            // Combine address_line_1 and address_line_2 into a single address field
            $address = $request->address_line_1;
            if (! empty($request->address_line_2)) {
                $address .= "\n".$request->address_line_2;
            }

            $existingDeleted->update([
                'company_name'          => $request->company_name,
                'party_code'            => $request->party_code,
                'gstin'                 => $request->gstin,
                'pan_number'            => $request->pan_number,
                'contact_person_name'   => $request->contact_person_name,
                'contact_person_number' => $request->contact_person_number,
                'email'                 => $request->email,
                'country_id'            => $request->country_id,
                'state_id'              => $request->state_id,
                'city_id'               => $request->city_id,
                'pincode'               => $request->pincode,
                'address'               => $address,
                'is_active'             => $request->boolean('is_active', true),
                'company_id'            => $companyId,
            ]);

            return redirect()
                ->route('vendors.index')
                ->with('success', 'Vendor restored and updated successfully');
        }

        $validated = $request->validate([
            'company_name' => [
                'required',
                'string',
                'max:80',
                Rule::unique('vendors')->where(function ($query) use ($companyId) {
                    return $query->where('company_id', $companyId);
                }),
            ],
            'party_code' => [
                'required',
                'string',
                'max:80',
                Rule::unique('vendors')->where(function ($query) use ($companyId) {
                    return $query->where('company_id', $companyId);
                }),
            ],
            'contact_person_name'   => ['required', 'string', 'max:255'],
            'contact_person_number' => ['required', 'string', 'max:20'],
            'email'                 => ['nullable', 'email', 'max:255'],
            'gstin'                 => ['nullable', 'string', 'max:20'],
            'pan_number'            => ['nullable', 'string', 'max:20'],
            'country_id'            => ['required', 'exists:countries,id'],
            'state_id'              => ['required', 'exists:states,id'],
            'city_id'               => ['nullable', 'exists:cities,id'],
            'pincode'               => ['required', 'string', 'max:20'],
            'address_line_1'        => ['required', 'string', 'max:255'],
            'address_line_2'        => ['nullable', 'string', 'max:255'],
            'is_active'             => ['boolean'],
        ]);

        // Combine address_line_1 and address_line_2 into a single address field
        $address = $validated['address_line_1'];
        if (! empty($validated['address_line_2'])) {
            $address .= "\n".$validated['address_line_2'];
        }

        // Remove address_line_1 and address_line_2 from validated data
        unset($validated['address_line_1'], $validated['address_line_2']);

        // Add company_id and combined address
        $validated['company_id'] = $companyId;
        $validated['address']    = $address;

        Vendor::create($validated);

        return redirect()
            ->route('vendors.index')
            ->with('success', 'Vendor created successfully');
    }

    public function edit(Vendor $vendor)
    {
        $companyId = auth()->user()->current_company_id;
        $vendor->where('company_id', $companyId)->firstOrFail();

        $countries = Country::where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name']);

        $states = State::with('country')
            ->where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'country_id']);

        $cities = City::with('state')
            ->where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'state_id']);

        return Inertia::render('vendors/edit', [
            'vendor'    => $vendor,
            'countries' => $countries,
            'states'    => $states,
            'cities'    => $cities,
        ]);
    }

    public function update(Request $request, Vendor $vendor)
    {
        $companyId = auth()->user()->current_company_id;

        // Check if we're trying to update to a party_code and company_name that exists in a soft-deleted record
        $existingDeleted = Vendor::onlyTrashed()
            ->where('company_id', $companyId)
            ->where('id', '!=', $vendor->id)
            ->where('party_code', $request->party_code)
            ->where('company_name', $request->company_name)
            ->first();

        if ($existingDeleted) {
            // Restore the soft-deleted record and update it with the current data
            $existingDeleted->restore();

            // Combine address_line_1 and address_line_2 into a single address field
            $address = $request->address_line_1;
            if (! empty($request->address_line_2)) {
                $address .= "\n".$request->address_line_2;
            }

            $existingDeleted->update([
                'company_name'          => $request->company_name,
                'party_code'            => $request->party_code,
                'gstin'                 => $request->gstin,
                'pan_number'            => $request->pan_number,
                'contact_person_name'   => $request->contact_person_name,
                'contact_person_number' => $request->contact_person_number,
                'email'                 => $request->email,
                'country_id'            => $request->country_id,
                'state_id'              => $request->state_id,
                'city_id'               => $request->city_id,
                'pincode'               => $request->pincode,
                'address'               => $address,
                'is_active'             => $request->boolean('is_active', true),
            ]);

            // Delete the current vendor as we're using the restored one instead
            $vendor->delete();

            return redirect()
                ->route('vendors.index')
                ->with('success', 'Vendor restored and updated successfully');
        }

        $validated = $request->validate([
            'company_name' => ['required', 'string', 'max:255'],
            'party_code'   => [
                'required',
                'string',
                'max:50',
                Rule::unique('vendors')->where(function ($query) use ($companyId) {
                    return $query->where('company_id', $companyId);
                })->ignore($vendor->id),
            ],
            'contact_person_name'   => ['required', 'string', 'max:255'],
            'contact_person_number' => ['required', 'string', 'max:20'],
            'email'                 => ['nullable', 'email', 'max:255'],
            'gstin'                 => ['nullable', 'string', 'max:20'],
            'pan_number'            => ['nullable', 'string', 'max:20'],
            'country_id'            => ['required', 'exists:countries,id'],
            'state_id'              => ['required', 'exists:states,id'],
            'city_id'               => ['nullable', 'exists:cities,id'],
            'pincode'               => ['required', 'string', 'max:20'],
            'address_line_1'        => ['required', 'string', 'max:255'],
            'address_line_2'        => ['nullable', 'string', 'max:255'],
            'is_active'             => ['boolean'],
        ]);

        // Combine address_line_1 and address_line_2 into a single address field
        $address = $validated['address_line_1'];
        if (! empty($validated['address_line_2'])) {
            $address .= "\n".$validated['address_line_2'];
        }

        // Remove address_line_1 and address_line_2 from validated data
        unset($validated['address_line_1'], $validated['address_line_2']);

        // Add combined address
        $validated['address'] = $address;

        $vendor->update($validated);

        return redirect()
            ->route('vendors.index')
            ->with('success', 'Vendor updated successfully');
    }

    public function destroy(Vendor $vendor)
    {
        $vendor->delete();

        return redirect()
            ->route('vendors.index')
            ->with('success', 'Vendor deleted successfully');
    }
}
