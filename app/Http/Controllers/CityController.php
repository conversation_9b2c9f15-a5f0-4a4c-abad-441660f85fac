<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\City;
use App\Models\State;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

final class CityController extends Controller
{
    public function index(Request $request)
    {
        // Get current user's company ID
        $companyId = auth()->user()->current_branch_id;

        $query = City::query()
            ->with(['state.country'])
            ->where('company_id', $companyId);

        // Apply filters
        if ($request->search) {
            $query->search($request->search);
        }

        if ($request->status && $request->status !== 'all') {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->state_id) {
            $query->where('state_id', $request->state_id);
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'id';
        $sortDirection = $request->direction ?? 'desc';
        $query->sort($sortField, $sortDirection);

        $cities = $query->paginate(10)
            ->withQueryString();

        // Get all states for filtering
        $states = State::with('country')
            ->where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'country_id']);

        return Inertia::render('locations/cities/index', [
            'cities'  => $cities,
            'states'  => $states,
            'filters' => $request->only(['search', 'status', 'state_id', 'sort', 'direction']),
        ]);
    }

    public function create()
    {
        $companyId = auth()->user()->current_company_id;
        $states    = State::with('country')
            ->where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'country_id']);

        return Inertia::render('locations/cities/create', [
            'states' => $states,
        ]);
    }

    public function store(Request $request)
    {
        $companyId = auth()->user()->current_company_id;

        // Check if a soft-deleted record with the same name or code exists
        $existingDeleted = City::onlyTrashed()
            ->where('company_id', $companyId)
            ->where('state_id', $request->state_id)
            ->where(function ($query) use ($request) {
                $query->where('name', $request->name);
                if ($request->code) {
                    $query->orWhere('code', $request->code);
                }
            })
            ->first();

        if ($existingDeleted) {
            // Restore the soft-deleted record and update it
            $existingDeleted->restore();

            $existingDeleted->update([
                'name'       => $request->name,
                'code'       => $request->code,
                'state_id'   => $request->state_id,
                'is_active'  => $request->boolean('is_active', true),
                'company_id' => $companyId,
            ]);

            return redirect()
                ->route('locations.cities.index')
                ->with('success', 'City restored and updated successfully');
        }

        // If no soft-deleted record exists, proceed with normal validation and creation
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255',
                Rule::unique('cities')->where(function ($query) use ($request, $companyId) {
                    return $query->where('company_id', $companyId)
                        ->where('state_id', $request->state_id);
                }),
            ],
            'code' => ['nullable', 'string', 'max:10',
                Rule::unique('cities')->where(function ($query) use ($request, $companyId) {
                    return $query->where('company_id', $companyId)
                        ->where('state_id', $request->state_id);
                }),
            ],
            'state_id'  => ['required', 'exists:states,id'],
            'is_active' => ['boolean'],
        ]);

        $validated['company_id'] = $companyId;

        City::create($validated);

        return redirect()
            ->route('locations.cities.index')
            ->with('success', 'City created successfully');
    }

    public function edit(City $city)
    {
        $companyId = auth()->user()->current_company_id;
        $city->where('company_id', $companyId)->firstOrFail();

        $states = State::with('country')
            ->where('company_id', $companyId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'country_id']);

        return Inertia::render('locations/cities/edit', [
            'city'   => $city,
            'states' => $states,
        ]);
    }

    public function update(Request $request, City $city)
    {
        $companyId = auth()->user()->current_company_id;

        // Check if we're trying to update to a name or code that exists in a soft-deleted record
        $existingDeleted = City::onlyTrashed()
            ->where('company_id', $companyId)
            ->where('state_id', $request->state_id)
            ->where('id', '!=', $city->id)
            ->where(function ($query) use ($request) {
                $query->where('name', $request->name);
                if ($request->code) {
                    $query->orWhere('code', $request->code);
                }
            })
            ->first();

        if ($existingDeleted) {
            // Restore the soft-deleted record and update it with the current data
            $existingDeleted->restore();

            $existingDeleted->update([
                'name'      => $request->name,
                'code'      => $request->code,
                'state_id'  => $request->state_id,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Delete the current city as we're using the restored one instead
            $city->delete();

            return redirect()
                ->route('locations.cities.index')
                ->with('success', 'City restored and updated successfully');
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255',
                Rule::unique('cities')->where(function ($query) use ($request, $companyId) {
                    return $query->where('company_id', $companyId)
                        ->where('state_id', $request->state_id);
                })->ignore($city->id),
            ],
            'code' => ['nullable', 'string', 'max:10',
                Rule::unique('cities')->where(function ($query) use ($request, $companyId) {
                    return $query->where('company_id', $companyId)
                        ->where('state_id', $request->state_id);
                })->ignore($city->id),
            ],
            'state_id'  => ['required', 'exists:states,id'],
            'is_active' => ['boolean'],
        ]);

        $city->update($validated);

        return redirect()
            ->route('locations.cities.index')
            ->with('success', 'City updated successfully');
    }

    public function destroy(City $city)
    {
        $companyId = auth()->user()->current_company_id;
        $city->where('company_id', $companyId)->firstOrFail();

        $city->delete();

        return back()->with('success', 'City deleted successfully');
    }
}
