<?php

declare(strict_types=1);

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\Branch;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;

final class AuthController extends Controller
{
    /**
     * Show the login page for tenant.
     */
    public function create(Request $request): Response
    {
        return Inertia::render('auth/login', [
            'canResetPassword' => false, // Disable password reset on tenant domains
            'status'           => $request->session()->get('status'),
            'isTenant'         => true,
            'tenantId'         => tenant('id'),
        ]);
    }

    /**
     * Handle tenant login - only allow vendors for this tenant.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        \Log::info('Tenant login attempt', [
            'email' => $request->email,
            'tenant_id' => tenant('id'),
            'url' => $request->url(),
        ]);

        $request->authenticate();

        $user = $request->user();

        \Log::info('Authentication successful', [
            'user_id' => $user->id,
            'email' => $user->email,
            'tenant_id' => $user->tenant_id,
            'current_tenant' => tenant('id'),
        ]);

        // Only allow vendors on tenant domains
        if (!$user->hasRole('vendor')) {
            Auth::guard('web')->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('login')->withErrors([
                'email' => 'Only salon owners can access this system.',
            ]);
        }

        // Check if this vendor belongs to this tenant (single database)
        $currentTenantId = tenant('id');
        if ($user->tenant_id !== $currentTenantId) {
            Auth::guard('web')->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('login')->withErrors([
                'email' => 'You do not have access to this salon.',
            ]);
        }

        $request->session()->regenerate();

        // Check if user has a current_branch_id set
        if (!$user->current_branch_id) {
            // Get the first branch owned by this vendor
            $branch = Branch::where('user_id', $user->id)->first();
            if ($branch) {
                $user->current_branch_id = $branch->id;
                $user->save();
            }
        }

        \Log::info('Tenant login successful, redirecting to dashboard', [
            'user_id' => $user->id,
            'dashboard_route' => route('dashboard'),
        ]);

        return redirect()->intended(route('dashboard'));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login');
    }
}
