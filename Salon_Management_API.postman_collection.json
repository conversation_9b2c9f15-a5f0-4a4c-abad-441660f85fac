{"info": {"name": "Salon Management API", "description": "API collection for the Salon Management System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password\"\n}"}, "url": {"raw": "{{base_url}}/api/login", "host": ["{{base_url}}"], "path": ["api", "login"]}, "description": "Login with admin credentials"}, "response": []}, {"name": "<PERSON><PERSON> (Staff)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password\"\n}"}, "url": {"raw": "{{base_url}}/api/login", "host": ["{{base_url}}"], "path": ["api", "login"]}, "description": "<PERSON>gin with staff credentials"}, "response": []}, {"name": "<PERSON><PERSON> (Customer)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password\"\n}"}, "url": {"raw": "{{base_url}}/api/login", "host": ["{{base_url}}"], "path": ["api", "login"]}, "description": "Login with customer credentials"}, "response": []}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user", "host": ["{{base_url}}"], "path": ["api", "user"]}, "description": "Get the currently authenticated user"}, "response": []}], "description": "Authentication endpoints"}, {"name": "Services", "item": [{"name": "List Services", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/services", "host": ["{{base_url}}"], "path": ["api", "services"], "query": [{"key": "search", "value": "hair", "disabled": true}, {"key": "min_duration", "value": "30", "disabled": true}, {"key": "max_duration", "value": "60", "disabled": true}, {"key": "min_price", "value": "20", "disabled": true}, {"key": "max_price", "value": "50", "disabled": true}, {"key": "sort", "value": "price", "disabled": true}, {"key": "direction", "value": "desc", "disabled": true}]}, "description": "List all active services for the current branch"}, "response": []}], "description": "Service management endpoints"}, {"name": "Seats", "item": [{"name": "Public Seat Map", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/seats/map", "host": ["{{base_url}}"], "path": ["api", "seats", "map"], "query": [{"key": "branch_id", "value": "1", "disabled": true}]}, "description": "Get public seat map (no authentication required)"}, "response": []}, {"name": "List Seats (Admin/Staff)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/seats", "host": ["{{base_url}}"], "path": ["api", "seats"], "query": [{"key": "status", "value": "available", "disabled": true}, {"key": "search", "value": "chair", "disabled": true}, {"key": "sort", "value": "name", "disabled": true}, {"key": "direction", "value": "asc", "disabled": true}]}, "description": "List all seats (admin/staff only)"}, "response": []}, {"name": "Update Seat Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"available\",\n    \"notes\": \"Seat is clean and ready for use\"\n}"}, "url": {"raw": "{{base_url}}/api/seats/1/status", "host": ["{{base_url}}"], "path": ["api", "seats", "1", "status"]}, "description": "Update seat status (admin/staff only)"}, "response": []}], "description": "Seat management endpoints"}, {"name": "Appointments", "item": [{"name": "My Appointments", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/appointments/my", "host": ["{{base_url}}"], "path": ["api", "appointments", "my"]}, "description": "Get current user's appointments"}, "response": []}, {"name": "Create Appointment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"appointment_date\": \"2023-06-15\",\n    \"appointment_time\": \"14:30:00\",\n    \"services\": [\n        {\"id\": 1},\n        {\"id\": 3}\n    ],\n    \"notes\": \"First time visit, prefer gentle styling\"\n}"}, "url": {"raw": "{{base_url}}/api/appointments", "host": ["{{base_url}}"], "path": ["api", "appointments"]}, "description": "Create a new appointment"}, "response": []}, {"name": "Today's Appointments (Admin/Staff)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/appointments/today", "host": ["{{base_url}}"], "path": ["api", "appointments", "today"]}, "description": "Get today's appointments (admin/staff only)"}, "response": []}, {"name": "Upcoming Appointments (Admin/Staff)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/appointments/upcoming", "host": ["{{base_url}}"], "path": ["api", "appointments", "upcoming"]}, "description": "Get upcoming appointments for next 7 days (admin/staff only)"}, "response": []}, {"name": "Update Appointment Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"in_progress\"\n}"}, "url": {"raw": "{{base_url}}/api/appointments/1/status", "host": ["{{base_url}}"], "path": ["api", "appointments", "1", "status"]}, "description": "Update appointment status (admin/staff only)"}, "response": []}], "description": "Appointment management endpoints"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Auto-extract token from login response", "if (pm.response.code === 200 && pm.info.requestName.includes('Login')) {", "    const responseJson = pm.response.json();", "    if (responseJson.token) {", "        pm.environment.set('auth_token', responseJson.token);", "        console.log('Token saved to environment variable: auth_token');", "    }", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}]}