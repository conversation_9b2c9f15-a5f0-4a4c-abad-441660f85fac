import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useInitials } from '@/hooks/use-initials';
import { type SharedData } from '@/types';
import { router, usePage } from '@inertiajs/react';
import { ChevronsUpDown } from 'lucide-react';
import { useEffect, useState } from 'react';
import AppLogoIcon from './app-logo-icon';

export default function AppLogo() {
    const { site_branches, auth } = usePage<SharedData>().props;
    const [selectedCompany, setSelectedCompany] = useState<any>(null);
    const getInitials = useInitials();

    // Don't render if user is not authenticated
    if (!auth?.user) {
        return (
            <div className="flex justify-center items-center bg-sidebar-primary rounded-md text-sidebar-primary-foreground aspect-square size-8">
                <AppLogoIcon className="text-white dark:text-black size-5 fill-current" />
            </div>
        );
    }

    useEffect(() => {
        // Find the branch that matches the user's current_branch_id
        const currentCompany = site_branches?.find((branch) => branch.id === auth.user?.current_branch_id) || site_branches?.[0] || null;

        setSelectedCompany(currentCompany);
    }, [site_branches, auth.user?.current_branch_id]);

    const handleCompanyChange = (company: any) => {
        // Only update if selecting a different branch
        if (!auth.user || company.id === auth.user.current_branch_id) return;

        // Send request to update the user's current_branch_id
        router.post(route('user.update-branch'), {
            branch_id: company.id,
        });
        //window.location.reload();
        // No need for window.location.assign here. The redirect from the backend will handle it.
    };

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <div className="flex justify-between items-center gap-2 w-full cursor-pointer">
                        <div className="flex justify-center items-center bg-sidebar-primary rounded-md text-sidebar-primary-foreground aspect-square size-8">
                            {selectedCompany?.logo ? (
                                <Avatar className="size-8">
                                    <AvatarImage src={selectedCompany.logo} alt={selectedCompany.name} />
                                    <AvatarFallback>
                                        <AppLogoIcon className="text-white dark:text-black size-5 fill-current" />
                                    </AvatarFallback>
                                </Avatar>
                            ) : (
                                <Avatar className="size-6">
                                    <AvatarFallback className="bg-neutral-200 dark:bg-neutral-700 rounded-lg text-black dark:text-white">
                                        {getInitials(selectedCompany?.name || auth.user?.name || 'Salon')}
                                    </AvatarFallback>
                                </Avatar>
                            )}
                        </div>

                        <div className="flex-1 grid ml-1 text-left text-sm">
                            <span className="mb-0.5 font-semibold truncate leading-none">
                                {selectedCompany?.name?.length > 15 ? `${selectedCompany.name}` : selectedCompany?.name || 'Salon ERP'}
                            </span>
                        </div>
                        <ChevronsUpDown className="ms-2 size-4" />
                    </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-56">
                    {site_branches?.length > 0 ? (
                        site_branches.map((branch) => (
                            <DropdownMenuItem key={branch.id} onClick={() => handleCompanyChange(branch)} className="flex items-center gap-2 py-2">
                                <Avatar className="size-6">
                                    <AvatarImage src={branch.logo} alt={branch.name} />
                                    <AvatarFallback className="text-xs">{branch.name?.substring(0, 2) || 'SL'}</AvatarFallback>
                                </Avatar>
                                <span className="truncate">{branch.name || 'Unnamed Branch'} sd</span>
                            </DropdownMenuItem>
                        ))
                    ) : (
                        <DropdownMenuItem disabled className="text-muted-foreground">
                            No branches available
                        </DropdownMenuItem>
                    )}
                </DropdownMenuContent>
            </DropdownMenu>
        </>
    );
}
