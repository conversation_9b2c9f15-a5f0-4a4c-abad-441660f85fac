import { Breadcrumbs } from '@/components/breadcrumbs';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { type BreadcrumbItem as BreadcrumbItemType } from '@/types';
import { Button } from '@/components/ui/button';
import { Search, Bell, User, Settings, LogOut, ChevronsUpDown, Sparkles, Heart, Crown } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Link, usePage } from '@inertiajs/react';
import { type SharedData } from '@/types';

export function AppSidebarHeader({ breadcrumbs = [] }: { breadcrumbs?: BreadcrumbItemType[] }) {
    return (
        <header className="flex items-center gap-3 bg-gradient-to-r from-slate-50/95 via-blue-50/95 to-indigo-50/95 dark:from-slate-950/95 dark:via-blue-950/95 dark:to-indigo-950/95 shadow-lg backdrop-blur-md px-6 md:px-4 border-slate-200/30 dark:border-slate-700/30 border-b h-16 group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 transition-all duration-300 ease-in-out shrink-0">
            <div className="flex items-center gap-3">
                <SidebarTrigger className="hover:bg-slate-100/50 dark:hover:bg-slate-900/50 -ml-1 transition-all duration-200 rounded-lg p-2 text-slate-700 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-200" />
                <div className="hidden md:block h-6 w-px bg-slate-200 dark:bg-slate-700"></div>
                <Breadcrumbs breadcrumbs={breadcrumbs} />
            </div>
            <div className="flex items-center gap-2 ml-auto">
                <Button variant="ghost" size="icon" className="rounded-full hover:bg-slate-100/50 dark:hover:bg-slate-900/50 text-slate-600 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300 transition-all duration-200 hover:scale-105">
                    <Search className="w-5 h-5" />
                </Button>
                <Button variant="ghost" size="icon" className="rounded-full hover:bg-slate-100/50 dark:hover:bg-slate-900/50 text-slate-600 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300 transition-all duration-200 hover:scale-105 relative">
                    <Bell className="w-5 h-5" />
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                        <Sparkles className="w-2 h-2 text-white" />
                    </div>
                </Button>

                {/* Profile Dropdown */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="gap-2 px-3 py-2 rounded-full hover:bg-slate-100/50 dark:hover:bg-slate-900/50 transition-all duration-200 hover:scale-105 border border-slate-200/50 dark:border-slate-700/50 bg-white/50 dark:bg-slate-950/50">
                            <Avatar className="w-8 h-8 ring-2 ring-slate-200 dark:ring-slate-700">
                                <AvatarImage src={usePage<SharedData>().props.auth.user.avatar} alt="Profile" />
                                <AvatarFallback className="bg-gradient-to-br from-slate-200 to-blue-200 dark:from-slate-700 dark:to-blue-700 text-slate-800 dark:text-slate-200 font-semibold">
                                    {usePage<SharedData>().props.auth.user.name.charAt(0)}
                                </AvatarFallback>
                            </Avatar>
                            <span className="md:inline-block hidden font-medium text-slate-800 dark:text-slate-200">
                                {usePage<SharedData>().props.auth.user.name}
                            </span>
                            <ChevronsUpDown className="opacity-70 ml-1 w-4 h-4 text-slate-600 dark:text-slate-400" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-64 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950 border-slate-200 dark:border-slate-700 shadow-xl">
                        <DropdownMenuLabel className="font-normal p-4">
                            <div className="flex items-center space-x-3">
                                <Avatar className="w-12 h-12 ring-2 ring-slate-300 dark:ring-slate-600">
                                    <AvatarImage src={usePage<SharedData>().props.auth.user.avatar} alt="Profile" />
                                    <AvatarFallback className="bg-gradient-to-br from-slate-200 to-blue-200 dark:from-slate-700 dark:to-blue-700 text-slate-800 dark:text-slate-200 font-semibold text-lg">
                                        {usePage<SharedData>().props.auth.user.name.charAt(0)}
                                    </AvatarFallback>
                                </Avatar>
                                <div className="flex flex-col space-y-1">
                                    <p className="font-semibold text-sm text-slate-800 dark:text-slate-200">{usePage<SharedData>().props.auth.user.name}</p>
                                    <p className="text-slate-600 dark:text-slate-400 text-xs">{usePage<SharedData>().props.auth.user.email}</p>
                                    <div className="flex items-center gap-1 mt-1">
                                        <Crown className="w-3 h-3 text-amber-500" />
                                        <span className="text-xs text-amber-600 dark:text-amber-400 font-medium">Salon Owner</span>
                                    </div>
                                </div>
                            </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator className="bg-slate-200 dark:bg-slate-700" />
                        <DropdownMenuItem asChild className="hover:bg-slate-100 dark:hover:bg-slate-900/50 cursor-pointer">
                            <Link href={route('profile.edit')} className="flex items-center p-3">
                                <User className="mr-3 w-4 h-4 text-slate-600 dark:text-slate-400" />
                                <span className="text-slate-800 dark:text-slate-200">👤 Profile</span>
                            </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild className="hover:bg-slate-100 dark:hover:bg-slate-900/50 cursor-pointer">
                            <Link href="/settings/profile" className="flex items-center p-3">
                                <Settings className="mr-3 w-4 h-4 text-slate-600 dark:text-slate-400" />
                                <span className="text-slate-800 dark:text-slate-200">⚙️ Settings</span>
                            </Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="bg-slate-200 dark:bg-slate-700" />
                        <DropdownMenuItem asChild className="hover:bg-red-100 dark:hover:bg-red-900/50 cursor-pointer">
                            <Link href={route('logout')} method="post" className="flex items-center p-3">
                                <LogOut className="mr-3 w-4 h-4 text-red-600 dark:text-red-400" />
                                <span className="text-red-600 dark:text-red-400">🚪 Logout</span>
                            </Link>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        </header>
    );
}







