import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useInitials } from '@/hooks/use-initials';
import { type User } from '@/types';

export function UserInfo({ user, showEmail = false }: { user: User; showEmail?: boolean }) {
    const getInitials = useInitials();
    return (
        <>
            <Avatar className="h-10 w-10 overflow-hidden rounded-full ring-2 ring-slate-200 dark:ring-slate-700 shadow-sm">
                <AvatarImage src={user.avatar} alt={user.name} />
                {user.branch?.name && (
                    <AvatarFallback className="rounded-full bg-gradient-to-br from-slate-200 to-blue-200 dark:from-slate-700 dark:to-blue-700 text-slate-800 dark:text-slate-200 font-semibold">
                        {getInitials(user.branch.name)}
                    </AvatarFallback>
                )}
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight">
                {user.branch?.name && (
                    <span className="truncate font-semibold text-slate-800 dark:text-slate-200">
                        {user.branch.name}
                    </span>
                )}
                <span className="text-slate-600 dark:text-slate-400 truncate text-xs font-medium">
                    {user.name}
                </span>
                {showEmail && (
                    <span className="text-slate-500 dark:text-slate-500 truncate text-xs">
                        {user.email}
                    </span>
                )}
            </div>
        </>
    );
}
