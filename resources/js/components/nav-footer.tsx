import { Icon } from '@/components/icon';
import { SidebarGroup, SidebarGroupContent, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { type ComponentPropsWithoutRef } from 'react';

export function NavFooter({
    items,
    className,
    ...props
}: ComponentPropsWithoutRef<typeof SidebarGroup> & {
    items: NavItem[];
}) {
    return (
        <SidebarGroup {...props} className={`group-data-[collapsible=icon]:p-0 ${className || ''}`}>
            <SidebarGroupContent>
                <SidebarMenu className="space-y-1">
                    {items.map((item) => (
                        <SidebarMenuItem key={item.title} className="group">
                            <SidebarMenuButton
                                asChild
                                className="rounded-lg transition-all duration-200 hover:bg-violet-100/50 dark:hover:bg-violet-900/50 text-violet-700 dark:text-violet-300 hover:text-violet-800 dark:hover:text-violet-200 group-hover:scale-[1.02] hover:shadow-sm"
                            >
                                <a href={item.href} target="_blank" rel="noopener noreferrer" className="flex items-center gap-3 p-2">
                                    {item.icon && <Icon iconNode={item.icon} className="h-5 w-5 text-violet-600 dark:text-violet-400" />}
                                    <span className="font-medium">{item.title}</span>
                                    <div className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                        <div className="w-2 h-2 bg-gradient-to-r from-violet-500 to-purple-500 rounded-full"></div>
                                    </div>
                                </a>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    ))}
                </SidebarMenu>
            </SidebarGroupContent>
        </SidebarGroup>
    );
}
