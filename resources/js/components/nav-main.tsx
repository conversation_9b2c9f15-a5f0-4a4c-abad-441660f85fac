import {
    SidebarGroup,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { ChevronDown } from 'lucide-react';
import { useEffect, useState } from 'react';

export function NavMain({ items = [] }: { items: NavItem[] }) {
    const page = usePage();

    return (
        <SidebarGroup className="px-3 py-2">
            <SidebarGroupLabel className="text-slate-600 dark:text-slate-400 font-semibold text-xs uppercase tracking-wider mb-3 flex items-center gap-2">
                <div className="w-2 h-2 bg-gradient-to-r from-slate-500 to-blue-500 rounded-full"></div>
                ✂️ Salon Management
            </SidebarGroupLabel>
            <SidebarMenu className="space-y-1">
                {items.map((item) => (
                    <NavMenuItem key={item.title} item={item} />
                ))}
            </SidebarMenu>
        </SidebarGroup>
    );
}

function NavMenuItem({ item }: { item: NavItem }) {
    const page = usePage();
    const [isOpen, setIsOpen] = useState(false);
    const hasChildren = item.children && item.children.length > 0;

    // Check if current route matches the matchPath pattern
    const isRouteMatch = (pattern?: string) => {
        if (!pattern) return false;
        const currentRoute = route().current() || '';
        return pattern.endsWith('.*') ? currentRoute.startsWith(pattern.slice(0, -2)) : currentRoute === pattern;
    };

    // Check if the item or any of its children are active
    const isActive =
        item.href === page.url ||
        isRouteMatch(item.matchPath) ||
        (hasChildren && item.children?.some((child) => child.href === page.url || isRouteMatch(child.matchPath)));

    // Auto-expand parent menu if child is active
    useEffect(() => {
        if (isActive && hasChildren) {
            setIsOpen(true);
        }
    }, [isActive, hasChildren]);

    return (
        <SidebarMenuItem key={item.title} className="group">
            {hasChildren ? (
                <>
                    <SidebarMenuButton
                        isActive={isActive}
                        tooltip={{ children: item.title }}
                        onClick={() => setIsOpen(!isOpen)}
                        className="rounded-lg transition-all duration-200 hover:bg-slate-100/50 dark:hover:bg-slate-900/50 data-[active=true]:bg-gradient-to-r data-[active=true]:from-slate-200 data-[active=true]:to-blue-200 dark:data-[active=true]:from-slate-800 dark:data-[active=true]:to-blue-800 data-[active=true]:text-slate-800 dark:data-[active=true]:text-slate-200 data-[active=true]:shadow-md group-hover:scale-[1.02]"
                    >
                        {item.icon && <item.icon className="text-slate-600 dark:text-slate-400" />}
                        <span className="font-medium">{item.title}</span>
                        <ChevronDown className={`ml-auto h-4 w-4 transition-all duration-200 text-slate-500 dark:text-slate-400 ${isOpen ? 'rotate-180' : ''}`} />
                    </SidebarMenuButton>

                    {isOpen && (
                        <SidebarMenuSub className="ml-4 mt-1 space-y-1">
                            {item.children?.map((child) => (
                                <SidebarMenuSubItem key={child.title}>
                                    <SidebarMenuButton
                                        asChild
                                        isActive={child.href === page.url || isRouteMatch(child.matchPath)}
                                        tooltip={{ children: child.title }}
                                        className="rounded-lg transition-all duration-200 hover:bg-slate-100/50 dark:hover:bg-slate-900/50 data-[active=true]:bg-gradient-to-r data-[active=true]:from-slate-200 data-[active=true]:to-blue-200 dark:data-[active=true]:from-slate-800 dark:data-[active=true]:to-blue-800 data-[active=true]:text-slate-800 dark:data-[active=true]:text-slate-200 data-[active=true]:shadow-md hover:scale-[1.02]"
                                    >
                                        <Link href={child.href} prefetch>
                                            {child.icon && <child.icon className="h-4 w-4 text-slate-600 dark:text-slate-400" />}
                                            <span className="font-medium">{child.title}</span>
                                        </Link>
                                    </SidebarMenuButton>
                                </SidebarMenuSubItem>
                            ))}
                        </SidebarMenuSub>
                    )}
                </>
            ) : (
                <SidebarMenuButton
                    asChild
                    isActive={item.href === page.url || isRouteMatch(item.matchPath)}
                    tooltip={{ children: item.title }}
                    className="rounded-lg transition-all duration-200 hover:bg-slate-100/50 dark:hover:bg-slate-900/50 data-[active=true]:bg-gradient-to-r data-[active=true]:from-slate-200 data-[active=true]:to-blue-200 dark:data-[active=true]:from-slate-800 dark:data-[active=true]:to-blue-800 data-[active=true]:text-slate-800 dark:data-[active=true]:text-slate-200 data-[active=true]:shadow-md group-hover:scale-[1.02]"
                >
                    <Link href={item.href}>
                        {item.icon && <item.icon className="text-slate-600 dark:text-slate-400" />}
                        <span className="font-medium">{item.title}</span>
                    </Link>
                </SidebarMenuButton>
            )}
        </SidebarMenuItem>
    );
}
