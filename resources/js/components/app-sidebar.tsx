import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import {
    Armchair,
    BookOpen,
    Calendar,
    ClipboardList,
    Clock,
    LayoutGrid,
    Scissors,
    Store,
    Users,
    Sparkles,
    Heart,
    Brush,
    Crown,
    Gem,
    Star
} from 'lucide-react';
import AppLogo from './app-logo';

// Helper function to get the correct dashboard route
const getDashboardRoute = () => {
    try {
        // Try tenant dashboard first (for tenant domains)
        return route('tenant.dashboard');
    } catch {
        try {
            // Try admin dashboard (for admin users on central domain)
            return route('admin.dashboard');
        } catch {
            // Fallback to root
            return '/';
        }
    }
};

const mainNavItems: NavItem[] = [
    {
        title: '✨ Dashboard',
        href: getDashboardRoute(),
    },
    {
        title: '💅 Appointments',
        href: route('appointments.index'),
        // icon: Calendar,
        matchPath: 'appointments.*',
    },
    {
        title: '💄 Beauty Services',
        href: route('services.index'),
        // icon: Brush,
        matchPath: 'services.*',
    },
    {
        title: '🪑 Seats',
        href: route('seats.index'),
        // icon: Crown,
        matchPath: 'seats.*',
    },
    {
        title: '⏰ Working Hours',
        href: route('working-hours.index'),
        // icon: Clock,
        matchPath: 'working-hours.*',
    },
    {
        title: '📋 Terms & Conditions',
        href: route('terms-conditions.index'),
        // icon: ClipboardList,
        matchPath: 'terms-conditions.*',
    },
    {
        title: '💎 Salon Team',
        href: route('staff.index'),
        // icon: Gem,
        matchPath: 'staff.*',
    },
    // Vendors menu item is only for central domain (admin)
    // Remove from tenant domains
];

const footerNavItems: NavItem[] = [
    {
        title: '💖 Salon Support',
        href: 'https://astridtechnology.com/contact-us',
        // icon: Heart,
    },
];

export function AppSidebar() {
    const { auth } = usePage().props as any;

    // Filter navigation items based on user role and domain
    const getNavItems = () => {
        let items = [...mainNavItems];

        // If user is admin, show vendor management
        if (auth.user?.roles?.some((role: any) => role.name === 'admin')) {
            items.push({
                title: '🏪 Salon Partners',
                href: route('vendors.index'),
                icon: Store,
                matchPath: 'vendors.*',
            });
        }

        return items;
    };

    return (
        <Sidebar collapsible="icon" variant="inset" className="bg-gradient-to-b from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-blue-950 dark:to-indigo-950 shadow-2xl border-r border-slate-200/50 dark:border-slate-800/50 backdrop-blur-sm">
            <SidebarHeader className="pb-6 bg-gradient-to-r from-slate-100/80 to-blue-100/80 dark:from-slate-900/80 dark:to-blue-900/80 border-b border-slate-200/50 dark:border-slate-800/50 backdrop-blur-md">
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild className="transition-all duration-300 hover:scale-105 hover:bg-slate-100/50 dark:hover:bg-slate-900/50 rounded-xl shadow-sm hover:shadow-md group">
                            <Link href={getDashboardRoute()} prefetch className="flex items-center gap-3 p-3">
                                <div className="relative">
                                    <AppLogo />
                                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                        <div className="w-1 h-1 bg-white rounded-full animate-pulse"></div>
                                    </div>
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent className="bg-gradient-to-b from-transparent via-slate-50/20 to-slate-50/40 dark:via-slate-950/20 dark:to-slate-950/40 backdrop-blur-sm">
                <NavMain items={getNavItems()} />
            </SidebarContent>

            <SidebarFooter className="bg-gradient-to-r from-slate-100/80 to-blue-100/80 dark:from-slate-900/80 dark:to-blue-900/80 border-t border-slate-200/50 dark:border-slate-800/50 backdrop-blur-md">
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}

