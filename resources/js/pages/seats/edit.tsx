import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { Arm<PERSON>, Crown, Sparkles, Heart } from 'lucide-react';
import { toast } from 'sonner';

interface Staff {
    id: number;
    name: string;
}

interface Seat {
    id: number;
    name: string;
    staff_id: number | null;
    status: string;
    notes: string;
}

interface Props {
    seat: Seat;
    staff: Staff[];
}

type FormData = {
    name: string;
    staff_id: string | null;
    status: string;
    notes: string;
};

const breadcrumbs = [
    { title: '👑 Beauty Stations', href: route('seats.index') },
    { title: '✨ Edit Station', href: '#' },
];

export default function Edit({ seat, staff }: Props) {
    const { data, setData, put, processing, errors } = useForm<FormData>({
        name: seat.name,
        staff_id: seat.staff_id ? seat.staff_id.toString() : null,
        status: seat.status,
        notes: seat.notes || '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('seats.update', seat.id), {
            onSuccess: () => {
                toast.success('Seat updated successfully');
            },
            onError: () => {
                toast.error('Failed to update seat');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✨ Edit Beauty Station" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center gap-3">
                    <div className="rounded-full bg-gradient-to-br from-emerald-500 to-teal-500 p-3 shadow-lg">
                        <Crown className="h-6 w-6 text-white" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                            Edit Beauty Station
                        </h1>
                        <p className="text-emerald-600 dark:text-emerald-400">Update your salon workstation details</p>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-950 dark:to-teal-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-emerald-500/10 p-2">
                                <Armchair className="h-5 w-5 text-emerald-600" />
                            </div>
                            <CardTitle className="text-lg text-emerald-800 dark:text-emerald-200">
                                👑 Station Details
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                {/* Station Name */}
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-emerald-700 dark:text-emerald-300">
                                        👑 Station Name <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="Enter beauty station name"
                                        className="bg-white/70 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-700 focus:border-emerald-400 focus:ring-emerald-400"
                                    />
                                    {errors.name && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.name}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Assigned Staff */}
                                <div className="space-y-2">
                                    <Label htmlFor="staff_id" className="text-emerald-700 dark:text-emerald-300">💎 Assigned Stylist</Label>
                                    <Select
                                        value={data.staff_id || 'none'}
                                        onValueChange={(value) => setData('staff_id', value === 'none' ? null : value)}
                                    >
                                        <SelectTrigger id="staff_id" className="bg-white/70 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-700 focus:border-emerald-400 focus:ring-emerald-400">
                                            <SelectValue placeholder="Select beauty stylist" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none">None</SelectItem>
                                            {staff && staff.length > 0 ? (
                                                staff.map((staffMember) => (
                                                    <SelectItem key={staffMember.id} value={staffMember.id.toString()}>
                                                        {staffMember.name}
                                                    </SelectItem>
                                                ))
                                            ) : (
                                                <SelectItem value="no-staff" disabled>
                                                    No beauty stylists available
                                                </SelectItem>
                                            )}
                                        </SelectContent>
                                    </Select>
                                    {errors.staff_id && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.staff_id}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Status */}
                                <div className="space-y-2">
                                    <Label htmlFor="status" className="text-emerald-700 dark:text-emerald-300">
                                        📊 Status <span className="text-red-500">*</span>
                                    </Label>
                                    <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                        <SelectTrigger id="status" className="bg-white/70 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-700 focus:border-emerald-400 focus:ring-emerald-400">
                                            <SelectValue placeholder="Select station status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="available">✨ Available</SelectItem>
                                            <SelectItem value="occupied">💅 In Use</SelectItem>
                                            <SelectItem value="cleaning">🧽 Cleaning</SelectItem>
                                            <SelectItem value="maintenance">🔧 Maintenance</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.status && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.status}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Notes */}
                                <div className="space-y-2 md:col-span-2">
                                    <Label htmlFor="notes" className="text-emerald-700 dark:text-emerald-300">📝 Notes</Label>
                                    <Textarea
                                        id="notes"
                                        value={data.notes}
                                        onChange={(e) => setData('notes', e.target.value)}
                                        rows={3}
                                        placeholder="Add any special notes about this beauty station..."
                                        className="bg-white/70 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-700 focus:border-emerald-400 focus:ring-emerald-400"
                                    />
                                    {errors.notes && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.notes}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>
                            </div>

                            {/* Form Actions */}
                            <div className="flex justify-end gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => window.history.back()}
                                    className="border-emerald-200 text-emerald-600 hover:bg-emerald-50 dark:border-emerald-700 dark:text-emerald-400 dark:hover:bg-emerald-950/30"
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white shadow-lg">
                                    ✨ Update Station
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
