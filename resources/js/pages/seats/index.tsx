import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Page } from '@inertiajs/core';
import { Head, Link, router } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { Armchair, ArrowDown, ArrowUp, Edit, Plus, Search, Trash, X, Crown, Sparkles, Heart } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

interface Seat {
    id: number;
    name: string;
    status: 'available' | 'occupied' | 'cleaning' | 'maintenance';
    staff: {
        id: number;
        name: string;
    } | null;
}

interface Props {
    seats: {
        data: Seat[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search?: string | null;
        status?: string | null;
        sort?: string | null;
        direction?: 'asc' | 'desc' | null;
        page?: number | null;
    };
}

// Define the page props interface that includes flash messages
interface PageProps {
    flash?: {
        message?: string;
        success?: string;
        error?: string;
    };
    [key: string]: unknown;
}

const breadcrumbs = [
    {
        title: '👑 Beauty Stations',
        href: route('seats.index'),
    },
];

export default function Index({ seats, filters }: Props) {
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [selectedSeat, setSelectedSeat] = useState<Seat | null>(null);

    const handleSearch = useCallback(
        debounce((value: string) => {
            router.get(
                route('seats.index'),
                { search: value || null, page: 1 },
                {
                    preserveState: true,
                    replace: true,
                },
            );
        }, 300),
        [],
    );

    const handleStatusChange = (value: string) => {
        router.get(
            route('seats.index'),
            {
                status: value === 'all' ? null : value,
                page: 1,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleSort = (field: string) => {
        const direction = field === filters.sort && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            route('seats.index'),
            { sort: field, direction, page: 1 },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const confirmDelete = (seat: Seat) => {
        setSelectedSeat(seat);
        setIsDeleteDialogOpen(true);
    };

    const handleDelete = () => {
        if (!selectedSeat) return;

        router.delete(route('seats.destroy', selectedSeat.id), {
            onSuccess: (page: Page<PageProps>) => {
                toast.success(page.props.flash?.success || 'Seat deleted successfully');
                setIsDeleteDialogOpen(false);
                setSelectedSeat(null);
            },
            onError: () => {
                toast.error('Failed to delete seat');
                setIsDeleteDialogOpen(false);
                setSelectedSeat(null);
            },
        });
    };

    const getSortIcon = (field: string) => {
        if (field !== filters.sort) return null;
        return filters.direction === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />;
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'available':
                return (
                    <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700">
                        ✨ Available
                    </Badge>
                );
            case 'occupied':
                return (
                    <Badge variant="outline" className="bg-rose-50 text-rose-700 border-rose-200 dark:bg-rose-900/30 dark:text-rose-300 dark:border-rose-700">
                        💅 In Use
                    </Badge>
                );
            case 'cleaning':
                return (
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700">
                        🧽 Cleaning
                    </Badge>
                );
            case 'maintenance':
                return (
                    <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-700">
                        🔧 Maintenance
                    </Badge>
                );
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="👑 Beauty Stations" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-emerald-500 to-teal-500 p-3 shadow-lg">
                            <Crown className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                                Beauty Stations
                            </h1>
                            <p className="text-emerald-600 dark:text-emerald-400">Manage your salon's beauty workstations</p>
                        </div>
                    </div>
                    <Link href={route('seats.create')}>
                        <Button className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white shadow-lg">
                            <Plus className="mr-2 h-4 w-4" />
                            ✨ Add Station
                        </Button>
                    </Link>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-950 dark:to-teal-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-emerald-500/10 p-2">
                                <Armchair className="h-5 w-5 text-emerald-600" />
                            </div>
                            <CardTitle className="text-lg text-emerald-800 dark:text-emerald-200">
                                👑 Beauty Station Management
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                <div className="flex w-full max-w-sm items-center space-x-2">
                                    <Search className="text-muted-foreground h-4 w-4" />
                                    <Input
                                        placeholder="Search beauty stations..."
                                        defaultValue={filters.search || ''}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="h-9 bg-white/70 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-700 focus:border-emerald-400 focus:ring-emerald-400"
                                    />
                                    {filters.search && (
                                        <Button variant="ghost" size="icon" onClick={() => handleSearch('')} className="h-8 w-8">
                                            <X className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                                <div className="flex items-center gap-2">
                                    <Select value={filters.status || 'all'} onValueChange={handleStatusChange}>
                                        <SelectTrigger className="h-9 w-[180px] bg-white/70 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-700">
                                            <SelectValue placeholder="Filter by status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Statuses</SelectItem>
                                            <SelectItem value="available">✨ Available</SelectItem>
                                            <SelectItem value="occupied">💅 In Use</SelectItem>
                                            <SelectItem value="cleaning">🧽 Cleaning</SelectItem>
                                            <SelectItem value="maintenance">🔧 Maintenance</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            <div className="rounded-lg border border-emerald-200/50 dark:border-emerald-700/50 bg-white/70 dark:bg-emerald-950/20 shadow-sm">
                                <Table>
                                    <TableHeader>
                                        <TableRow className="border-emerald-200/50 dark:border-emerald-700/50 hover:bg-emerald-50/50 dark:hover:bg-emerald-950/30">
                                            <TableHead className="w-[50px]">#</TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('name')} className="flex items-center">
                                                    👑 Station Name
                                                    {getSortIcon('name')}
                                                </button>
                                            </TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('status')} className="flex items-center">
                                                    📊 Status
                                                    {getSortIcon('status')}
                                                </button>
                                            </TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('staff_id')} className="flex items-center">
                                                    💎 Assigned Stylist
                                                    {getSortIcon('staff_id')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-right">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {seats.data.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={5} className="h-24 text-center text-emerald-600 dark:text-emerald-400">
                                                    👑 No beauty stations found.
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            seats.data.map((seat, index) => (
                                                <TableRow key={seat.id} className="border-emerald-200/50 dark:border-emerald-700/50 hover:bg-emerald-50/50 dark:hover:bg-emerald-950/30">
                                                    <TableCell className="font-medium text-emerald-900 dark:text-emerald-100">
                                                        {(seats.current_page - 1) * seats.per_page + index + 1}
                                                    </TableCell>
                                                    <TableCell className="font-medium text-emerald-800 dark:text-emerald-200">{seat.name}</TableCell>
                                                    <TableCell>{getStatusBadge(seat.status)}</TableCell>
                                                    <TableCell className="text-emerald-700 dark:text-emerald-300">{seat.staff?.name || 'Not assigned'}</TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex justify-end gap-2">
                                                            <Link href={route('seats.edit', seat.id)}>
                                                                <Button variant="outline" size="icon" className="h-8 w-8 border-emerald-200 text-emerald-600 hover:bg-emerald-50 dark:border-emerald-700 dark:text-emerald-400 dark:hover:bg-emerald-950/30">
                                                                    <Edit className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Button
                                                                variant="outline"
                                                                size="icon"
                                                                className="h-8 w-8 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-950/30"
                                                                onClick={() => confirmDelete(seat)}
                                                            >
                                                                <Trash className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {seats.links.length > 3 && (
                                <Pagination>
                                    <PaginationContent>
                                        {seats.links.map((link, i) => {
                                            if (link.url === null) {
                                                return (
                                                    <PaginationItem key={i} disabled>
                                                        {i === 0 ? (
                                                            <PaginationPrevious />
                                                        ) : i === seats.links.length - 1 ? (
                                                            <PaginationNext />
                                                        ) : (
                                                            <PaginationLink disabled>
                                                                {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                            </PaginationLink>
                                                        )}
                                                    </PaginationItem>
                                                );
                                            }

                                            return (
                                                <PaginationItem key={i}>
                                                    {i === 0 ? (
                                                        <PaginationPrevious
                                                            href={link.url}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        />
                                                    ) : i === seats.links.length - 1 ? (
                                                        <PaginationNext
                                                            href={link.url}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        />
                                                    ) : (
                                                        <PaginationLink
                                                            href={link.url}
                                                            isActive={link.active}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        >
                                                            {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                        </PaginationLink>
                                                    )}
                                                </PaginationItem>
                                            );
                                        })}
                                    </PaginationContent>
                                </Pagination>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Delete Dialog */}
                <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>👑 Delete Beauty Station</AlertDialogTitle>
                            <AlertDialogDescription>
                                Are you sure you want to delete the beauty station "{selectedSeat?.name}"? This action cannot be undone and will remove this workstation from your salon.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDelete} className="bg-red-600 text-white hover:bg-red-700">
                                Delete Station
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </div>
        </AppLayout>
    );
}
