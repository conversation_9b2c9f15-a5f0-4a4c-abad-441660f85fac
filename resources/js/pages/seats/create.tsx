import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { Armchair, Crown, Sparkles, Heart } from 'lucide-react';
import { toast } from 'sonner';

interface Staff {
    id: number;
    name: string;
}

interface Props {
    staff: Staff[];
}

type FormData = {
    name: string;
    staff_id: string | null;
    status: string;
    notes: string;
};

const breadcrumbs = [
    { title: '🪑 Professional Stations', href: route('seats.index') },
    { title: '✨ Create Station', href: '#' },
];

export default function Create({ staff }: Props) {
    const { data, setData, post, processing, errors } = useForm<FormData>({
        name: '',
        staff_id: null,
        status: 'available',
        notes: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('seats.store'), {
            onSuccess: () => {
                toast.success('Seat created successfully');
            },
            onError: () => {
                toast.error('Failed to create seat');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✨ Create Professional Station" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center gap-3">
                    <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                        <Armchair className="h-6 w-6 text-white" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                            Create Professional Station
                        </h1>
                        <p className="text-slate-600 dark:text-slate-400">Add a new professional workstation to your salon</p>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Armchair className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                🪑 Station Details
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                {/* Station Name */}
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-slate-700 dark:text-slate-300">
                                        🪑 Station Name <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="Enter professional station name"
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.name && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.name}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Assigned Staff */}
                                <div className="space-y-2">
                                    <Label htmlFor="staff_id" className="text-slate-700 dark:text-slate-300">👨‍💼 Assigned Professional</Label>
                                    <Select
                                        value={data.staff_id || 'none'}
                                        onValueChange={(value) => setData('staff_id', value === 'none' ? null : value)}
                                    >
                                        <SelectTrigger id="staff_id" className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400">
                                            <SelectValue placeholder="Select professional staff" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none">None</SelectItem>
                                            {staff && staff.length > 0 ? (
                                                staff.map((staffMember) => (
                                                    <SelectItem key={staffMember.id} value={staffMember.id.toString()}>
                                                        {staffMember.name}
                                                    </SelectItem>
                                                ))
                                            ) : (
                                                <SelectItem value="no-staff" disabled>
                                                    No staff members available
                                                </SelectItem>
                                            )}
                                        </SelectContent>
                                    </Select>
                                    {errors.staff_id && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.staff_id}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Status */}
                                <div className="space-y-2">
                                    <Label htmlFor="status" className="text-slate-700 dark:text-slate-300">
                                        📊 Status <span className="text-red-500">*</span>
                                    </Label>
                                    <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                        <SelectTrigger id="status" className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400">
                                            <SelectValue placeholder="Select station status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="available">✨ Available</SelectItem>
                                            <SelectItem value="occupied">💅 In Use</SelectItem>
                                            <SelectItem value="cleaning">🧽 Cleaning</SelectItem>
                                            <SelectItem value="maintenance">🔧 Maintenance</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.status && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.status}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Notes */}
                                <div className="space-y-2 md:col-span-2">
                                    <Label htmlFor="notes" className="text-slate-700 dark:text-slate-300">📝 Notes</Label>
                                    <Textarea
                                        id="notes"
                                        value={data.notes}
                                        onChange={(e) => setData('notes', e.target.value)}
                                        rows={3}
                                        placeholder="Add any special notes about this professional station..."
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.notes && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.notes}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>
                            </div>

                            {/* Form Actions */}
                            <div className="flex justify-end gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => window.history.back()}
                                    className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30"
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                                    ✨ Create Station
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
