import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { Scissors, Brush, Sparkles, Heart } from 'lucide-react';
import { toast } from 'sonner';

type FormData = {
    name: string;
    description: string;
    duration_minutes: string;
    price: string;
    is_active: boolean;
};

const breadcrumbs = [
    { title: '💄 Beauty Services', href: route('services.index') },
    { title: '✨ Create Service', href: '#' },
];

export default function Create() {
    const { data, setData, post, processing, errors } = useForm<FormData>({
        name: '',
        description: '',
        duration_minutes: '',
        price: '',
        is_active: true,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('services.store'), {
            onSuccess: () => {
                toast.success('Service created successfully');
            },
            onError: () => {
                toast.error('Failed to create service');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✨ Create Beauty Service" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center gap-3">
                    <div className="rounded-full bg-gradient-to-br from-violet-500 to-purple-500 p-3 shadow-lg">
                        <Brush className="h-6 w-6 text-white" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                            Create Beauty Service
                        </h1>
                        <p className="text-violet-600 dark:text-violet-400">Add a new beauty treatment to your salon</p>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-950 dark:to-purple-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-violet-500/10 p-2">
                                <Scissors className="h-5 w-5 text-violet-600" />
                            </div>
                            <CardTitle className="text-lg text-violet-800 dark:text-violet-200">
                                💄 Service Details
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                {/* Service Name */}
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-violet-700 dark:text-violet-300">
                                        💄 Service Name <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="Enter beauty service name"
                                        className="bg-white/70 dark:bg-violet-950/30 border-violet-200 dark:border-violet-700 focus:border-violet-400 focus:ring-violet-400"
                                    />
                                    {errors.name && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.name}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Duration */}
                                <div className="space-y-2">
                                    <Label htmlFor="duration_minutes" className="text-violet-700 dark:text-violet-300">
                                        ⏱️ Duration (minutes) <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="duration_minutes"
                                        type="number"
                                        min="1"
                                        value={data.duration_minutes}
                                        onChange={(e) => setData('duration_minutes', e.target.value)}
                                        placeholder="e.g., 60"
                                        className="bg-white/70 dark:bg-violet-950/30 border-violet-200 dark:border-violet-700 focus:border-violet-400 focus:ring-violet-400"
                                    />
                                    {errors.duration_minutes && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.duration_minutes}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Price */}
                                <div className="space-y-2">
                                    <Label htmlFor="price" className="text-violet-700 dark:text-violet-300">
                                        💰 Price (₹) <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="price"
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        value={data.price}
                                        onChange={(e) => setData('price', e.target.value)}
                                        placeholder="e.g., 1500.00"
                                        className="bg-white/70 dark:bg-violet-950/30 border-violet-200 dark:border-violet-700 focus:border-violet-400 focus:ring-violet-400"
                                    />
                                    {errors.price && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.price}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Status */}
                                <div className="flex items-center space-x-3">
                                    <Switch
                                        id="is_active"
                                        checked={data.is_active}
                                        onCheckedChange={(checked) => setData('is_active', checked)}
                                        className="data-[state=checked]:bg-violet-600"
                                    />
                                    <Label htmlFor="is_active" className="text-violet-700 dark:text-violet-300">✨ Active Service</Label>
                                    {errors.is_active && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.is_active}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Description */}
                                <div className="space-y-2 md:col-span-2">
                                    <Label htmlFor="description" className="text-violet-700 dark:text-violet-300">
                                        📝 Description <span className="text-red-500">*</span>
                                    </Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        rows={3}
                                        placeholder="Describe this beauty service in detail..."
                                        className="bg-white/70 dark:bg-violet-950/30 border-violet-200 dark:border-violet-700 focus:border-violet-400 focus:ring-violet-400"
                                    />
                                    {errors.description && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.description}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>
                            </div>

                            {/* Form Actions */}
                            <div className="flex justify-end gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => window.history.back()}
                                    className="border-violet-200 text-violet-600 hover:bg-violet-50 dark:border-violet-700 dark:text-violet-400 dark:hover:bg-violet-950/30"
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-violet-500 to-purple-500 hover:from-violet-600 hover:to-purple-600 text-white shadow-lg">
                                    ✨ Create Service
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
