import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { Sciss<PERSON>, Brush, Sparkles, Heart } from 'lucide-react';
import { toast } from 'sonner';

interface Service {
    id: number;
    name: string;
    description: string;
    duration_minutes: number;
    price: number;
    is_active: boolean;
}

interface Props {
    service: Service;
}

type FormData = {
    name: string;
    description: string;
    duration_minutes: string;
    price: string;
    is_active: boolean;
};

const breadcrumbs = [
    { title: '✂️ Professional Services', href: route('services.index') },
    { title: '✨ Edit Service', href: '#' },
];

export default function Edit({ service }: Props) {
    const { data, setData, put, processing, errors } = useForm<FormData>({
        name: service.name,
        description: service.description,
        duration_minutes: service.duration_minutes.toString(),
        price: service.price.toString(),
        is_active: service.is_active,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('services.update', service.id), {
            onSuccess: () => {
                toast.success('Service updated successfully');
            },
            onError: () => {
                toast.error('Failed to update service');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✨ Edit Professional Service" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center gap-3">
                    <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                        <Brush className="h-6 w-6 text-white" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                            Edit Professional Service
                        </h1>
                        <p className="text-slate-600 dark:text-slate-400">Update your salon's professional treatment details</p>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Scissors className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                ✂️ Service Details
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                {/* Service Name */}
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-slate-700 dark:text-slate-300">
                                        ✂️ Service Name <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="Enter professional service name"
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.name && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.name}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Duration */}
                                <div className="space-y-2">
                                    <Label htmlFor="duration_minutes" className="text-slate-700 dark:text-slate-300">
                                        ⏱️ Duration (minutes) <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="duration_minutes"
                                        type="number"
                                        min="1"
                                        value={data.duration_minutes}
                                        onChange={(e) => setData('duration_minutes', e.target.value)}
                                        placeholder="e.g., 60"
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.duration_minutes && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.duration_minutes}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Price */}
                                <div className="space-y-2">
                                    <Label htmlFor="price" className="text-slate-700 dark:text-slate-300">
                                        💰 Price (₹) <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="price"
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        value={data.price}
                                        onChange={(e) => setData('price', e.target.value)}
                                        placeholder="e.g., 1500.00"
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.price && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.price}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Status */}
                                <div className="flex items-center space-x-3">
                                    <Switch
                                        id="is_active"
                                        checked={data.is_active}
                                        onCheckedChange={(checked) => setData('is_active', checked)}
                                        className="data-[state=checked]:bg-blue-600"
                                    />
                                    <Label htmlFor="is_active" className="text-slate-700 dark:text-slate-300">✨ Active Service</Label>
                                    {errors.is_active && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.is_active}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Description */}
                                <div className="space-y-2 md:col-span-2">
                                    <Label htmlFor="description" className="text-slate-700 dark:text-slate-300">
                                        📝 Description <span className="text-red-500">*</span>
                                    </Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        rows={3}
                                        placeholder="Describe this professional service in detail..."
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.description && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.description}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>
                            </div>

                            {/* Form Actions */}
                            <div className="flex justify-end gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => window.history.back()}
                                    className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30"
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                                    ✨ Update Service
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
