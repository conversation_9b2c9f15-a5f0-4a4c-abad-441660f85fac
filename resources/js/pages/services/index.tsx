import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Page } from '@inertiajs/core';
import { Head, Link, router } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { ArrowDown, ArrowUp, Edit, Plus, Scissors, Search, Trash, X, Sparkles, Brush, Heart } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

interface Service {
    id: number;
    name: string;
    description: string;
    duration_minutes: number;
    price: number;
    is_active: boolean;
}

interface Props {
    services: {
        data: Service[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search?: string | null;
        status?: string | null;
        sort?: string | null;
        direction?: 'asc' | 'desc' | null;
        page?: number | null;
    };
}

// Define the page props interface that includes flash messages
interface PageProps {
    flash?: {
        message?: string;
        success?: string;
        error?: string;
    };
    [key: string]: unknown;
}

const breadcrumbs = [
    {
        title: '✂️ Professional Services',
        href: route('services.index'),
    },
];

export default function Index({ services, filters }: Props) {
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [selectedService, setSelectedService] = useState<Service | null>(null);

    const handleSearch = useCallback(
        debounce((value: string) => {
            router.get(
                route('services.index'),
                { search: value || null, page: 1 },
                {
                    preserveState: true,
                    replace: true,
                },
            );
        }, 300),
        [],
    );

    const handleStatusChange = (value: string) => {
        router.get(
            route('services.index'),
            {
                status: value === 'all' ? null : value,
                page: 1,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleSort = (field: string) => {
        const direction = field === filters.sort && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            route('services.index'),
            { sort: field, direction, page: 1 },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const confirmDelete = (service: Service) => {
        setSelectedService(service);
        setIsDeleteDialogOpen(true);
    };

    const handleDelete = () => {
        if (!selectedService) return;

        router.delete(route('services.destroy', selectedService.id), {
            onSuccess: (page: Page<PageProps>) => {
                toast.success(page.props.flash?.success || 'Service deleted successfully');
                setIsDeleteDialogOpen(false);
                setSelectedService(null);
            },
            onError: () => {
                toast.error('Failed to delete service');
                setIsDeleteDialogOpen(false);
                setSelectedService(null);
            },
        });
    };

    const getSortIcon = (field: string) => {
        if (field !== filters.sort) return null;
        return filters.direction === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />;
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            maximumFractionDigits: 2,
        }).format(price);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✂️ Professional Services" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <Brush className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                Professional Services
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400">Manage your salon's professional services and treatments</p>
                        </div>
                    </div>
                    <Link href={route('services.create')}>
                        <Button className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                            <Plus className="mr-2 h-4 w-4" />
                            ✨ Add Service
                        </Button>
                    </Link>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-950 dark:to-purple-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-violet-500/10 p-2">
                                <Scissors className="h-5 w-5 text-violet-600" />
                            </div>
                            <CardTitle className="text-lg text-violet-800 dark:text-violet-200">
                                💄 Beauty Service Management
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                <div className="flex w-full max-w-sm items-center space-x-2">
                                    <Search className="text-muted-foreground h-4 w-4" />
                                    <Input
                                        placeholder="Search beauty services..."
                                        defaultValue={filters.search || ''}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="h-9 bg-white/70 dark:bg-violet-950/30 border-violet-200 dark:border-violet-700 focus:border-violet-400 focus:ring-violet-400"
                                    />
                                    {filters.search && (
                                        <Button variant="ghost" size="icon" onClick={() => handleSearch('')} className="h-8 w-8">
                                            <X className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                                <div className="flex items-center gap-2">
                                    <Select value={filters.status || 'all'} onValueChange={handleStatusChange}>
                                        <SelectTrigger className="h-9 w-[180px] bg-white/70 dark:bg-violet-950/30 border-violet-200 dark:border-violet-700">
                                            <SelectValue placeholder="Filter by status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Statuses</SelectItem>
                                            <SelectItem value="active">✨ Active</SelectItem>
                                            <SelectItem value="inactive">💤 Inactive</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            <div className="rounded-lg border border-violet-200/50 dark:border-violet-700/50 bg-white/70 dark:bg-violet-950/20 shadow-sm">
                                <Table>
                                    <TableHeader>
                                        <TableRow className="border-violet-200/50 dark:border-violet-700/50 hover:bg-violet-50/50 dark:hover:bg-violet-950/30">
                                            <TableHead className="w-[50px]">#</TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('name')} className="flex items-center">
                                                    💄 Service Name
                                                    {getSortIcon('name')}
                                                </button>
                                            </TableHead>
                                            <TableHead>📝 Description</TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('duration_minutes')} className="flex items-center">
                                                    ⏱️ Duration
                                                    {getSortIcon('duration_minutes')}
                                                </button>
                                            </TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('price')} className="flex items-center">
                                                    💰 Price
                                                    {getSortIcon('price')}
                                                </button>
                                            </TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('is_active')} className="flex items-center">
                                                    📊 Status
                                                    {getSortIcon('is_active')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-right">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {services.data.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={7} className="h-24 text-center text-violet-600 dark:text-violet-400">
                                                    💄 No beauty services found.
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            services.data.map((service, index) => (
                                                <TableRow key={service.id} className="border-violet-200/50 dark:border-violet-700/50 hover:bg-violet-50/50 dark:hover:bg-violet-950/30">
                                                    <TableCell className="font-medium text-violet-900 dark:text-violet-100">
                                                        {(services.current_page - 1) * services.per_page + index + 1}
                                                    </TableCell>
                                                    <TableCell className="font-medium text-violet-800 dark:text-violet-200">{service.name}</TableCell>
                                                    <TableCell className="max-w-[200px] truncate text-violet-700 dark:text-violet-300">{service.description}</TableCell>
                                                    <TableCell className="text-violet-700 dark:text-violet-300">{service.duration_minutes} min</TableCell>
                                                    <TableCell className="font-semibold text-violet-800 dark:text-violet-200">{formatPrice(service.price)}</TableCell>
                                                    <TableCell>
                                                        {service.is_active ? (
                                                            <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700">
                                                                ✨ Active
                                                            </Badge>
                                                        ) : (
                                                            <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700">
                                                                💤 Inactive
                                                            </Badge>
                                                        )}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex justify-end gap-2">
                                                            <Link href={route('services.edit', service.id)}>
                                                                <Button variant="outline" size="icon" className="h-8 w-8 border-blue-200 text-blue-600 hover:bg-blue-50 dark:border-blue-700 dark:text-blue-400 dark:hover:bg-blue-950/30">
                                                                    <Edit className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Button
                                                                variant="outline"
                                                                size="icon"
                                                                className="h-8 w-8 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-950/30"
                                                                onClick={() => confirmDelete(service)}
                                                            >
                                                                <Trash className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {services.links.length > 3 && (
                                <Pagination>
                                    <PaginationContent>
                                        {services.links.map((link, i) => {
                                            if (link.url === null) {
                                                return (
                                                    <PaginationItem key={i} disabled>
                                                        {i === 0 ? (
                                                            <PaginationPrevious />
                                                        ) : i === services.links.length - 1 ? (
                                                            <PaginationNext />
                                                        ) : (
                                                            <PaginationLink disabled>
                                                                {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                            </PaginationLink>
                                                        )}
                                                    </PaginationItem>
                                                );
                                            }

                                            return (
                                                <PaginationItem key={i}>
                                                    {i === 0 ? (
                                                        <PaginationPrevious
                                                            href={link.url}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        />
                                                    ) : i === services.links.length - 1 ? (
                                                        <PaginationNext
                                                            href={link.url}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        />
                                                    ) : (
                                                        <PaginationLink
                                                            href={link.url}
                                                            isActive={link.active}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        >
                                                            {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                        </PaginationLink>
                                                    )}
                                                </PaginationItem>
                                            );
                                        })}
                                    </PaginationContent>
                                </Pagination>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Delete Dialog */}
                <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>💄 Delete Beauty Service</AlertDialogTitle>
                            <AlertDialogDescription>
                                Are you sure you want to delete the beauty service "{selectedService?.name}"? This action cannot be undone and will remove this service from your salon offerings.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDelete} className="bg-red-600 text-white hover:bg-red-700">
                                Delete Service
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </div>
        </AppLayout>
    );
}
