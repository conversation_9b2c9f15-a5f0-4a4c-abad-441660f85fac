import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { Ruler } from 'lucide-react';
import { toast } from 'sonner';

type FormData = {
    name: string;
    short_name: string;
    description: string;
};

const breadcrumbs = [
    { title: 'Units', href: route('products.units.index') },
    { title: 'Create Unit', href: '#' },
];

export default function Create() {
    const { data, setData, post, processing, errors } = useForm<FormData>({
        name: '',
        short_name: '',
        description: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('products.units.store'), {
            onSuccess: () => {
                toast.success('Unit created successfully');
            },
            onError: () => {
                toast.error('Failed to create unit');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Unit" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center gap-2">
                    <Ruler className="h-6 w-6" />
                    <h1 className="text-2xl font-bold">Create Unit</h1>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Unit Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">
                                    Name <span className="text-red-500">*</span>
                                </Label>
                                <Input id="name" value={data.name} onChange={(e) => setData('name', e.target.value)} />
                                {errors.name && (
                                    <Alert variant="destructive">
                                        <AlertDescription>{errors.name}</AlertDescription>
                                    </Alert>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="short_name">
                                    Short Name <span className="text-red-500">*</span>
                                </Label>
                                <Input id="short_name" value={data.short_name} onChange={(e) => setData('short_name', e.target.value)} />
                                {errors.short_name && (
                                    <Alert variant="destructive">
                                        <AlertDescription>{errors.short_name}</AlertDescription>
                                    </Alert>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    rows={4}
                                />
                                {errors.description && (
                                    <Alert variant="destructive">
                                        <AlertDescription>{errors.description}</AlertDescription>
                                    </Alert>
                                )}
                            </div>

                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="outline" onClick={() => window.history.back()}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Create Unit
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
