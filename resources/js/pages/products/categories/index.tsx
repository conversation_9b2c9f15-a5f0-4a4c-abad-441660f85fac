import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Page } from '@inertiajs/core';
import { Head, Link, router } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { ArrowDown, ArrowUp, CheckCircle2, CircleDot, FolderTree, Pencil, Plus, RotateCcw, Search, Trash, X, XCircle } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

interface Category {
    id: number;
    name: string;
    slug: string;
    parent_id: number | null;
    parent?: {
        id: number;
        name: string;
    };
    children_count: number;
    products_count: number;
    is_active: boolean;
    description: string | null;
}

interface Props {
    categories: {
        data: Category[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search?: string | null;
        status?: string | null;
        sort?: string | null;
        direction?: 'asc' | 'desc' | null;
        page?: number | null;
    };
}

// Define the page props interface that includes flash messages
interface PageProps {
    flash?: {
        message?: string;
        success?: string;
        error?: string;
    };
    [key: string]: unknown;
}

const breadcrumbs = [
    {
        title: 'Categories',
        href: route('products.categories.index'),
    },
];

export default function Index({ categories, filters = {} }: Props) {
    // Provide default values for filters
    const defaultFilters = {
        search: null,
        status: null,
        sort: 'created_at',
        direction: 'desc' as const,
        page: 1,
        ...filters, // Spread the provided filters over defaults
    };

    const [sortField, setSortField] = useState(defaultFilters.sort);
    const [sortDirection, setSortDirection] = useState(defaultFilters.direction);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);

    const handleSort = (field: string) => {
        const direction = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
        setSortField(field);
        setSortDirection(direction);
        router.get(
            route('products.categories.index'),
            {
                ...filters,
                sort: field,
                direction,
            },
            { preserveState: true },
        );
    };

    const handleSearch = useCallback(
        debounce((search: string) => {
            router.get(
                route('products.categories.index'),
                {
                    ...filters,
                    search,
                    page: 1,
                },
                { preserveState: true },
            );
        }, 300),
        [filters],
    );

    const handleStatusFilter = (status: string) => {
        router.get(
            route('products.categories.index'),
            {
                ...filters,
                status,
                page: 1,
            },
            { preserveState: true },
        );
    };

    const handleDeleteClick = (category: Category) => {
        setCategoryToDelete(category);
        setDeleteDialogOpen(true);
    };

    const handleDeleteConfirm = () => {
        if (!categoryToDelete) return;

        router.delete(route('products.categories.destroy', { category: categoryToDelete.id }), {
            onSuccess: (page: Page<PageProps>) => {
                setDeleteDialogOpen(false);
                setCategoryToDelete(null);

                if (page.props.flash?.error) {
                    toast.error(page.props.flash.error);
                } else {
                    toast.success(page.props.flash?.success || 'Category deleted successfully');
                }
            },
            onError: () => {
                setDeleteDialogOpen(false);
                setCategoryToDelete(null);
                toast.error('An unexpected error occurred');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Categories" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <FolderTree className="h-6 w-6" />
                        <h1 className="text-2xl font-bold">Categories</h1>
                    </div>
                    <Link href={route('products.categories.create')}>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Category
                        </Button>
                    </Link>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Category List</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Search and Filters */}
                            <div className="flex flex-col gap-4 md:flex-row md:items-center">
                                <div className="relative flex-1">
                                    <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                                    <Input
                                        placeholder="Search categories..."
                                        defaultValue={filters.search ?? ''}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Select value={filters.status || 'all'} onValueChange={handleStatusFilter}>
                                        <SelectTrigger className="bg-background w-[200px]">
                                            <SelectValue placeholder="All Status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">
                                                <span className="flex items-center">
                                                    <CircleDot className="mr-2 h-4 w-4" />
                                                    All Status
                                                </span>
                                            </SelectItem>
                                            <SelectItem value="active">
                                                <span className="flex items-center">
                                                    <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                                                    Active
                                                </span>
                                            </SelectItem>
                                            <SelectItem value="inactive">
                                                <span className="flex items-center">
                                                    <XCircle className="mr-2 h-4 w-4 text-gray-500" />
                                                    Inactive
                                                </span>
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            <div className="flex items-center justify-between border-b pb-4">
                                <div className="flex items-center gap-2">
                                    <Badge variant="outline" className="rounded-full">
                                        {categories.total} Categories
                                    </Badge>
                                    {filters.search && (
                                        <Badge variant="secondary" className="rounded-full">
                                            Search: {filters.search}
                                            <X className="ml-1 h-3 w-3 cursor-pointer" onClick={() => handleSearch('')} />
                                        </Badge>
                                    )}
                                    {filters.status && filters.status !== 'all' && (
                                        <Badge variant="secondary" className="rounded-full">
                                            Status: {filters.status}
                                            <X className="ml-1 h-3 w-3 cursor-pointer" onClick={() => handleStatusFilter('all')} />
                                        </Badge>
                                    )}
                                </div>
                                {(filters.search || (filters.status && filters.status !== 'all')) && (
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                            router.get(
                                                route('products.categories.index'),
                                                {
                                                    search: '',
                                                    status: 'all',
                                                    page: 1,
                                                },
                                                {
                                                    preserveState: true,
                                                    preserveScroll: true,
                                                },
                                            );
                                        }}
                                    >
                                        <RotateCcw className="mr-2 h-4 w-4" />
                                        Reset Filters
                                    </Button>
                                )}
                            </div>

                            <div className="rounded-md border">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead onClick={() => handleSort('name')} className="cursor-pointer">
                                                Name{' '}
                                                {sortField === 'name' &&
                                                    (sortDirection === 'asc' ? (
                                                        <ArrowUp className="ml-2 inline h-4 w-4" />
                                                    ) : (
                                                        <ArrowDown className="ml-2 inline h-4 w-4" />
                                                    ))}
                                            </TableHead>
                                            <TableHead>Parent Category</TableHead>
                                            <TableHead>Subcategories</TableHead>
                                            <TableHead>Products</TableHead>
                                            <TableHead>Status</TableHead>
                                            <TableHead>Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {categories.data.map((category) => (
                                            <TableRow key={category.id}>
                                                <TableCell>{category.name}</TableCell>
                                                <TableCell>
                                                    {category.parent ? (
                                                        <Badge variant="outline">{category.parent.name}</Badge>
                                                    ) : (
                                                        <Badge variant="secondary">Root Category</Badge>
                                                    )}
                                                </TableCell>
                                                <TableCell>{category.children_count}</TableCell>
                                                <TableCell>{category.products_count}</TableCell>
                                                <TableCell>
                                                    <Badge variant={category.is_active ? 'success' : 'secondary'}>
                                                        {category.is_active ? 'Active' : 'Inactive'}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex gap-2">
                                                        <Link href={route('products.categories.edit', { category: category.id })}>
                                                            <Button size="sm" variant="outline">
                                                                <Pencil className="h-4 w-4" />
                                                            </Button>
                                                        </Link>
                                                        <Button size="sm" variant="destructive" onClick={() => handleDeleteClick(category)}>
                                                            <Trash className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>

                            {/* Pagination */}
                            {categories.links && categories.links.length > 3 && (
                                <div className="mt-4">
                                    <Pagination>
                                        <PaginationContent>
                                            {categories.links.map((link, i) => {
                                                if (i === 0) {
                                                    return (
                                                        <PaginationItem key={i}>
                                                            <PaginationPrevious
                                                                href="#"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    if (!link.url) return;
                                                                    const page = new URL(link.url).searchParams.get('page');
                                                                    router.get(
                                                                        route('products.categories.index'),
                                                                        {
                                                                            ...filters,
                                                                            page: Number(page),
                                                                        },
                                                                        { preserveState: true },
                                                                    );
                                                                }}
                                                                className={!link.url ? 'pointer-events-none opacity-50' : ''}
                                                            />
                                                        </PaginationItem>
                                                    );
                                                }

                                                if (i === categories.links.length - 1) {
                                                    return (
                                                        <PaginationItem key={i}>
                                                            <PaginationNext
                                                                href="#"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    if (!link.url) return;
                                                                    const page = new URL(link.url).searchParams.get('page');
                                                                    router.get(
                                                                        route('products.categories.index'),
                                                                        {
                                                                            ...filters,
                                                                            page: Number(page),
                                                                        },
                                                                        { preserveState: true },
                                                                    );
                                                                }}
                                                                className={!link.url ? 'pointer-events-none opacity-50' : ''}
                                                            />
                                                        </PaginationItem>
                                                    );
                                                }

                                                return (
                                                    <PaginationItem key={i}>
                                                        <PaginationLink
                                                            href="#"
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                if (!link.url) return;
                                                                const page = new URL(link.url).searchParams.get('page');
                                                                router.get(
                                                                    route('products.categories.index'),
                                                                    {
                                                                        ...filters,
                                                                        page: Number(page),
                                                                    },
                                                                    { preserveState: true },
                                                                );
                                                            }}
                                                            isActive={link.active}
                                                        >
                                                            {link.label}
                                                        </PaginationLink>
                                                    </PaginationItem>
                                                );
                                            })}
                                        </PaginationContent>
                                    </Pagination>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>

            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            {categoryToDelete && (
                                <>
                                    This action will delete the category "{categoryToDelete.name}".
                                    {categoryToDelete.children_count > 0 && (
                                        <span className="text-red-500">
                                            {' '}
                                            This category has {categoryToDelete.children_count} subcategories. You must delete or move them first.
                                        </span>
                                    )}
                                </>
                            )}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDeleteConfirm}
                            className="bg-destructive hover:bg-destructive/90 text-white dark:bg-red-600 dark:text-white dark:hover:bg-red-700"
                        >
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
