import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { FolderTree } from 'lucide-react';
import { toast } from 'sonner';

interface Category {
    id: number;
    name: string;
    parent_id: number | null;
}

interface Props {
    categories: Category[];
}

type FormData = {
    name: string;
    parent_id: string | null;
    is_active: boolean;
    description: string;
};

const breadcrumbs = [
    { title: 'Categories', href: route('products.categories.index') },
    { title: 'Create Category', href: '#' },
];

export default function Create({ categories }: Props) {
    const { data, setData, post, processing, errors } = useForm<FormData>({
        name: '',
        parent_id: null,
        is_active: true,
        description: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('products.categories.store'), {
            onSuccess: () => {
                toast.success('Category created successfully');
            },
            onError: () => {
                toast.error('Failed to create category');
            },
        });
    };

    // Group categories by parent_id
    const categoriesMap = new Map<number | null, Category[]>();
    categories.forEach((cat) => {
        const parentId = cat.parent_id;
        if (!categoriesMap.has(parentId)) {
            categoriesMap.set(parentId, []);
        }
        categoriesMap.get(parentId)?.push(cat);
    });

    // Get root categories (parent_id is null)
    const rootCategories = categoriesMap.get(null) || [];

    const renderCategoryTree = (categoryList: Category[], level: number = 0) => {
        return categoryList.map((cat) => (
            <div key={cat.id} className="space-y-2">
                <div className="flex items-center space-x-2">
                    <Checkbox
                        id={`category-${cat.id}`}
                        checked={data.parent_id === cat.id.toString()}
                        onCheckedChange={(checked) => {
                            if (checked) {
                                setData('parent_id', cat.id.toString());
                            } else {
                                setData('parent_id', null);
                            }
                        }}
                    />
                    <Label htmlFor={`category-${cat.id}`} className={`${level > 0 ? 'ml-' + level * 4 : ''}`}>
                        {cat.name}
                    </Label>
                </div>
                {/* Render children */}
                {categoriesMap.has(cat.id) && <div className="ml-6">{renderCategoryTree(categoriesMap.get(cat.id) || [], level + 1)}</div>}
            </div>
        ));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Category" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center gap-2">
                    <FolderTree className="h-6 w-6" />
                    <h1 className="text-2xl font-bold">Create Category</h1>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Category Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">
                                    Name <span className="text-red-500">*</span>
                                </Label>
                                <Input id="name" value={data.name} onChange={(e) => setData('name', e.target.value)} />
                                {errors.name && (
                                    <Alert variant="destructive">
                                        <AlertDescription>{errors.name}</AlertDescription>
                                    </Alert>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label>Parent Category</Label>
                                <Card>
                                    <CardContent className="p-4">
                                        <div className="max-h-60 space-y-2 overflow-y-auto">
                                            <div className="flex items-center space-x-2">
                                                <Checkbox
                                                    id="no-parent"
                                                    checked={data.parent_id === null}
                                                    onCheckedChange={(checked) => {
                                                        if (checked) {
                                                            setData('parent_id', null);
                                                        }
                                                    }}
                                                />
                                                <Label htmlFor="no-parent">None (Root Category)</Label>
                                            </div>
                                            {renderCategoryTree(rootCategories)}
                                        </div>
                                    </CardContent>
                                </Card>
                                {errors.parent_id && (
                                    <Alert variant="destructive">
                                        <AlertDescription>{errors.parent_id}</AlertDescription>
                                    </Alert>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    rows={4}
                                />
                                {errors.description && (
                                    <Alert variant="destructive">
                                        <AlertDescription>{errors.description}</AlertDescription>
                                    </Alert>
                                )}
                            </div>

                            <div className="flex items-center space-x-2">
                                <Switch id="is_active" checked={data.is_active} onCheckedChange={(checked) => setData('is_active', checked)} />
                                <Label htmlFor="is_active">Active</Label>
                            </div>

                            <div className="flex justify-end">
                                <Button type="submit" disabled={processing}>
                                    Create Category
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
