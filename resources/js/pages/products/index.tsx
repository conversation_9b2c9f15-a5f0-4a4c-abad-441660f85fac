import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { type Product, type ProductCategory, type ProductFilters } from '@/types/product';
import { Head, Link, router } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { ArrowDown, ArrowUp, CheckCircle2, CircleDot, Package, Pencil, Plus, RotateCcw, Search, Trash, X, XCircle } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

interface Props {
    products: {
        data: Product[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    categories: ProductCategory[];
    filters: ProductFilters;
}

const breadcrumbs = [
    {
        title: 'Products',
        href: route('products.index'),
    },
];

export default function Index({ products, categories, filters }: Props) {
    const [sortField, setSortField] = useState(filters.sort || 'created_at');
    const [sortDirection, setSortDirection] = useState(filters.direction || 'desc');
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [productToDelete, setProductToDelete] = useState<Product | null>(null);

    const handleSort = (field: string) => {
        const direction = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
        setSortField(field);
        setSortDirection(direction);
        router.get(
            route('products.index'),
            {
                ...filters,
                sort: field,
                direction,
            },
            { preserveState: true },
        );
    };

    const handleSearch = useCallback(
        debounce((search: string) => {
            router.get(
                route('products.index'),
                {
                    ...filters,
                    search,
                    page: 1,
                },
                { preserveState: true },
            );
        }, 300),
        [filters],
    );

    const handleCategoryFilter = (category: string) => {
        router.get(
            route('products.index'),
            {
                ...filters,
                category,
                page: 1, // Reset to first page on filter change
            },
            { preserveState: true },
        );
    };

    const handleStatusFilter = (status: string) => {
        router.get(
            route('products.index'),
            {
                ...filters,
                status,
                page: 1, // Reset to first page on filter change
            },
            { preserveState: true },
        );
    };

    const handlePageChange = (page: number) => {
        router.get(
            route('products.index'),
            {
                ...filters,
                page,
            },
            { preserveState: true },
        );
    };

    const handleDeleteClick = (product: Product) => {
        setProductToDelete(product);
        setDeleteDialogOpen(true);
    };

    const handleDeleteConfirm = () => {
        if (!productToDelete) return;

        // Fix: Properly construct the delete route with the product ID
        router.delete(route('products.destroy', { product: productToDelete.id }), {
            onSuccess: () => {
                setDeleteDialogOpen(false);
                setProductToDelete(null);
                toast.success('Product deleted successfully');
            },
            onError: (error) => {
                toast.error(error.message || 'Failed to delete product. Please try again.');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Products" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Package className="h-6 w-6" />
                        <h1 className="text-2xl font-bold">Products</h1>
                    </div>
                    <Link href={route('products.create')}>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Product
                        </Button>
                    </Link>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Product List</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex flex-col gap-4 md:flex-row md:items-center">
                                <div className="relative flex-1">
                                    <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                                    <Input
                                        placeholder="Search products..."
                                        defaultValue={filters.search ?? ''}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                                <div className="flex flex-col gap-4 md:flex-row md:items-center">
                                    <div className="flex items-center space-x-2">
                                        <Select value={filters.category || 'all'} onValueChange={handleCategoryFilter}>
                                            <SelectTrigger className="bg-background w-[200px]">
                                                <SelectValue placeholder="All Categories" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">
                                                    <span className="flex items-center">
                                                        <Package className="mr-2 h-4 w-4" />
                                                        All Categories
                                                    </span>
                                                </SelectItem>
                                                {categories.map((category) => (
                                                    <SelectItem key={category.id} value={category.id.toString()}>
                                                        <span className="flex items-center">
                                                            <Package className="mr-2 h-4 w-4" />
                                                            {category.name}
                                                        </span>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Select value={filters.status || 'all'} onValueChange={handleStatusFilter}>
                                            <SelectTrigger className="bg-background w-[200px]">
                                                <SelectValue placeholder="All Status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">
                                                    <span className="flex items-center">
                                                        <CircleDot className="mr-2 h-4 w-4" />
                                                        All Status
                                                    </span>
                                                </SelectItem>
                                                <SelectItem value="active">
                                                    <span className="flex items-center">
                                                        <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                                                        Active
                                                    </span>
                                                </SelectItem>
                                                <SelectItem value="inactive">
                                                    <span className="flex items-center">
                                                        <XCircle className="mr-2 h-4 w-4 text-gray-500" />
                                                        Inactive
                                                    </span>
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center justify-between border-b pb-4">
                                <div className="flex items-center gap-2">
                                    <Badge variant="outline" className="rounded-full">
                                        {products.total} Products
                                    </Badge>
                                    {filters.search && (
                                        <Badge variant="secondary" className="rounded-full">
                                            Search: {filters.search}
                                            <X className="ml-1 h-3 w-3 cursor-pointer" onClick={() => handleSearch('')} />
                                        </Badge>
                                    )}
                                    {filters.category && filters.category !== 'all' && (
                                        <Badge variant="secondary" className="rounded-full">
                                            Category: {categories.find((c) => c.id.toString() === filters.category)?.name}
                                            <X className="ml-1 h-3 w-3 cursor-pointer" onClick={() => handleCategoryFilter('all')} />
                                        </Badge>
                                    )}
                                    {filters.status && filters.status !== 'all' && (
                                        <Badge variant="secondary" className="rounded-full">
                                            Status: {filters.status}
                                            <X className="ml-1 h-3 w-3 cursor-pointer" onClick={() => handleStatusFilter('all')} />
                                        </Badge>
                                    )}
                                </div>
                                {(filters.search ||
                                    (filters.category && filters.category !== 'all') ||
                                    (filters.status && filters.status !== 'all')) && (
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                            router.get(
                                                route('products.index'),
                                                {
                                                    search: '',
                                                    category: 'all',
                                                    status: 'all',
                                                    page: 1,
                                                },
                                                {
                                                    preserveState: true,
                                                    preserveScroll: true,
                                                },
                                            );
                                        }}
                                    >
                                        <RotateCcw className="mr-2 h-4 w-4" />
                                        Reset Filters
                                    </Button>
                                )}
                            </div>
                        </div>

                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead onClick={() => handleSort('name')} className="cursor-pointer">
                                            Name{' '}
                                            {sortField === 'name' &&
                                                (sortDirection === 'asc' ? (
                                                    <ArrowUp className="ml-2 inline h-4 w-4" />
                                                ) : (
                                                    <ArrowDown className="ml-2 inline h-4 w-4" />
                                                ))}
                                        </TableHead>
                                        <TableHead>SKU</TableHead>
                                        <TableHead>Categories</TableHead>
                                        <TableHead onClick={() => handleSort('price')} className="cursor-pointer">
                                            Price{' '}
                                            {sortField === 'price' &&
                                                (sortDirection === 'asc' ? (
                                                    <ArrowUp className="ml-2 inline h-4 w-4" />
                                                ) : (
                                                    <ArrowDown className="ml-2 inline h-4 w-4" />
                                                ))}
                                        </TableHead>
                                        <TableHead>Stock</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {products.data.map((product) => (
                                        <TableRow key={product.id}>
                                            <TableCell>{product.name}</TableCell>
                                            <TableCell>{product.sku}</TableCell>
                                            <TableCell>
                                                <div className="flex flex-wrap gap-1">
                                                    {product.categories.map((category) => (
                                                        <Badge key={category.id} variant="secondary">
                                                            {category.name}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            </TableCell>
                                            <TableCell>₹{product.price}</TableCell>
                                            <TableCell>{product.stock}</TableCell>
                                            <TableCell>
                                                <Badge variant={product.is_active ? 'success' : 'secondary'}>
                                                    {product.is_active ? 'Active' : 'Inactive'}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex gap-2">
                                                    <Link href={route('products.edit', { product: product.id })}>
                                                        <Button size="sm" variant="outline">
                                                            <Pencil className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Button size="sm" variant="destructive" onClick={() => handleDeleteClick(product)}>
                                                        <Trash className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        {products.links && products.links.length > 3 && (
                            <div className="mt-4">
                                <Pagination>
                                    <PaginationContent>
                                        {products.links.map((link, i) => {
                                            // Handle previous button
                                            if (i === 0) {
                                                return (
                                                    <PaginationItem key={i}>
                                                        <PaginationPrevious
                                                            href="#"
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                if (!link.url) return;
                                                                const page = new URL(link.url).searchParams.get('page');
                                                                handlePageChange(Number(page));
                                                            }}
                                                            className={!link.url ? 'pointer-events-none opacity-50' : ''}
                                                        />
                                                    </PaginationItem>
                                                );
                                            }

                                            // Handle next button
                                            if (i === products.links.length - 1) {
                                                return (
                                                    <PaginationItem key={i}>
                                                        <PaginationNext
                                                            href="#"
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                if (!link.url) return;
                                                                const page = new URL(link.url).searchParams.get('page');
                                                                handlePageChange(Number(page));
                                                            }}
                                                            className={!link.url ? 'pointer-events-none opacity-50' : ''}
                                                        />
                                                    </PaginationItem>
                                                );
                                            }

                                            // Handle ellipsis
                                            if (link.label.includes('...')) {
                                                return <PaginationEllipsis key={i} />;
                                            }

                                            // Handle numbered pages
                                            return (
                                                <PaginationItem key={i}>
                                                    <PaginationLink
                                                        href="#"
                                                        onClick={(e) => {
                                                            e.preventDefault();
                                                            const page = new URL(link.url as string).searchParams.get('page');
                                                            handlePageChange(Number(page));
                                                        }}
                                                        isActive={link.active}
                                                    >
                                                        {link.label}
                                                    </PaginationLink>
                                                </PaginationItem>
                                            );
                                        })}
                                    </PaginationContent>
                                </Pagination>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will permanently delete the product "{productToDelete?.name}". This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDeleteConfirm}
                            className="bg-destructive hover:bg-destructive/90 text-white dark:bg-red-600 dark:text-white dark:hover:bg-red-700"
                        >
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
