import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type Product, type ProductCategory, type ProductHsn, type ProductUnit } from '@/types/product';
import { Page } from '@inertiajs/core';
import { Head, useForm } from '@inertiajs/react';
import { Package } from 'lucide-react';
import { toast } from 'sonner';

interface PageProps {
    flash?: {
        message?: string;
        success?: string;
        error?: string;
    };
    [key: string]: any; // Add index signature to satisfy PageProps constraint
}

interface Props {
    product: Product;
    categories: ProductCategory[];
    units: ProductUnit[];
    hsns: ProductHsn[];
}

interface CategoryTreeProps {
    categoryList: ProductCategory[];
    selectedCategories: string[];
    onCategoryChange: (categoryId: string, checked: boolean) => void;
    level?: number;
}

const CategoryTree: React.FC<CategoryTreeProps> = ({ categoryList, selectedCategories, onCategoryChange, level = 0 }) => {
    // Group categories by parent_id
    const categoriesMap = new Map<number | null, ProductCategory[]>();
    categoryList.forEach((cat) => {
        const parentId = cat.parent_id;
        if (!categoriesMap.has(parentId)) {
            categoriesMap.set(parentId, []);
        }
        categoriesMap.get(parentId)?.push(cat);
    });

    // Get root categories (parent_id is null)
    const rootCategories = categoriesMap.get(null) || [];

    const renderCategories = (categories: ProductCategory[], currentLevel: number = 0) => {
        return categories.map((category) => (
            <div key={category.id} className="space-y-2">
                <div className="flex items-center space-x-2">
                    <Checkbox
                        id={`category-${category.id}`}
                        checked={selectedCategories.includes(category.id.toString())}
                        onCheckedChange={(checked: boolean) => {
                            onCategoryChange(category.id.toString(), checked);
                        }}
                    />
                    <Label htmlFor={`category-${category.id}`} className={`${currentLevel > 0 ? 'ml-' + currentLevel * 4 : ''}`}>
                        {category.name}
                    </Label>
                </div>
                {/* Render children */}
                {categoriesMap.has(category.id) && (
                    <div className="ml-6">{renderCategories(categoriesMap.get(category.id) || [], currentLevel + 1)}</div>
                )}
            </div>
        ));
    };

    return <>{renderCategories(rootCategories)}</>;
};

const breadcrumbs = [
    { title: 'Products', href: route('products.index') },
    { title: 'Edit Product', href: '#' },
];

export default function Edit({ product, categories, units, hsns }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        name: product.name,
        sku: product.sku,
        price: product.price.toString(),
        stock: product.stock.toString(),
        hsn_id: product.hsn_id?.toString() || '',
        unit_id: product.unit_id?.toString() || '',
        categories: product.categories.map((c) => c.id.toString()),
        is_active: product.is_active,
        is_taxable: product.is_taxable,
        description: product.description || '',
    });

    const handleCategoryChange = (categoryId: string, checked: boolean) => {
        if (checked) {
            setData('categories', [...data.categories, categoryId]);
        } else {
            setData(
                'categories',
                data.categories.filter((id) => id !== categoryId),
            );
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('products.update', product.id), {
            preserveScroll: true,
            onSuccess: (page: Page<PageProps>) => {
                toast.success(page.props.flash?.message ?? 'Product updated successfully');
            },
            onError: () => {
                toast.error('Failed to update product. Please check the form and try again.');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Product" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center gap-2">
                    <Package className="h-6 w-6" />
                    <h1 className="text-2xl font-bold">Edit Product</h1>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Product Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                {/* Basic Information */}
                                <div className="space-y-2">
                                    <Label htmlFor="name">
                                        Name <span className="text-red-500">*</span>
                                    </Label>
                                    <Input id="name" value={data.name} onChange={(e) => setData('name', e.target.value)} />
                                    {errors.name && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.name}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="sku">
                                        SKU <span className="text-red-500">*</span>
                                    </Label>
                                    <Input id="sku" value={data.sku} onChange={(e) => setData('sku', e.target.value)} />
                                    {errors.sku && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.sku}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="price">
                                        Price <span className="text-red-500">*</span>
                                    </Label>
                                    <Input id="price" type="number" value={data.price} onChange={(e) => setData('price', e.target.value)} />
                                    {errors.price && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.price}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="stock">
                                        Stock <span className="text-red-500">*</span>
                                    </Label>
                                    <Input id="stock" type="number" value={data.stock} onChange={(e) => setData('stock', e.target.value)} />
                                    {errors.stock && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.stock}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Categories Selection */}
                                <div className="col-span-2 space-y-2">
                                    <Label>
                                        Categories <span className="text-red-500">*</span>
                                    </Label>
                                    <Card>
                                        <CardContent className="p-4">
                                            <div className="max-h-60 space-y-2 overflow-y-auto">
                                                <CategoryTree
                                                    categoryList={categories}
                                                    selectedCategories={data.categories}
                                                    onCategoryChange={handleCategoryChange}
                                                />
                                            </div>
                                        </CardContent>
                                    </Card>
                                    {errors.categories && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.categories}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* HSN Select */}
                                <div className="space-y-2">
                                    <Label>
                                        HSN Code <span className="text-red-500">*</span>
                                    </Label>
                                    <Select value={data.hsn_id} onValueChange={(value) => setData('hsn_id', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select HSN" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {hsns.map((hsn) => (
                                                <SelectItem key={hsn.id} value={hsn.id.toString()}>
                                                    {hsn.code} - {hsn.description}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.hsn_id && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.hsn_id}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Unit Select */}
                                <div className="space-y-2">
                                    <Label>
                                        Unit <span className="text-red-500">*</span>
                                    </Label>
                                    <Select value={data.unit_id} onValueChange={(value) => setData('unit_id', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select Unit" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {units.map((unit) => (
                                                <SelectItem key={unit.id} value={unit.id.toString()}>
                                                    {unit.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.unit_id && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.unit_id}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>
                            </div>

                            {/* Description */}
                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    rows={4}
                                />
                                {errors.description && (
                                    <Alert variant="destructive">
                                        <AlertDescription>{errors.description}</AlertDescription>
                                    </Alert>
                                )}
                            </div>

                            {/* Switches */}
                            <div className="flex gap-6">
                                <div className="flex items-center gap-2">
                                    <Switch
                                        id="is_active"
                                        checked={data.is_active}
                                        onCheckedChange={(checked: boolean) => setData('is_active', checked)}
                                    />
                                    <Label htmlFor="is_active">Active</Label>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Switch
                                        id="is_taxable"
                                        checked={data.is_taxable}
                                        onCheckedChange={(checked: boolean) => setData('is_taxable', checked)}
                                    />
                                    <Label htmlFor="is_taxable">Taxable</Label>
                                </div>
                            </div>

                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="outline" onClick={() => window.history.back()}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Update Product
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
