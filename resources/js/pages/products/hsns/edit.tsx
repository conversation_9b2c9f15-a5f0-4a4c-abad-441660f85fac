import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { FileText } from 'lucide-react';
import { toast } from 'sonner';

interface ProductHsn {
    id: number;
    code: string;
    description: string;
    gst_rate: number;
}

interface Props {
    hsn: ProductHsn;
}

const breadcrumbs = [
    { title: 'HSN Codes', href: route('products.hsns.index') },
    { title: 'Edit HSN Code', href: '#' },
];

export default function Edit({ hsn }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        code: hsn.code,
        description: hsn.description,
        gst_rate: hsn.gst_rate.toString(),
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('products.hsns.update', hsn.id), {
            onSuccess: () => {
                toast.success('HSN code updated successfully');
            },
            onError: () => {
                toast.error('Failed to update HSN code');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit HSN Code" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center gap-2">
                    <FileText className="h-6 w-6" />
                    <h1 className="text-2xl font-bold">Edit HSN Code</h1>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>HSN Code Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="code">Code</Label>
                                <Input id="code" value={data.code} onChange={(e) => setData('code', e.target.value)} maxLength={8} />
                                {errors.code && (
                                    <Alert variant="destructive">
                                        <AlertDescription>{errors.code}</AlertDescription>
                                    </Alert>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea id="description" value={data.description} onChange={(e) => setData('description', e.target.value)} />
                                {errors.description && (
                                    <Alert variant="destructive">
                                        <AlertDescription>{errors.description}</AlertDescription>
                                    </Alert>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="gst_rate">GST Rate (%)</Label>
                                <Input
                                    id="gst_rate"
                                    type="number"
                                    min="0"
                                    max="100"
                                    step="0.01"
                                    value={data.gst_rate}
                                    onChange={(e) => setData('gst_rate', e.target.value)}
                                />
                                {errors.gst_rate && (
                                    <Alert variant="destructive">
                                        <AlertDescription>{errors.gst_rate}</AlertDescription>
                                    </Alert>
                                )}
                            </div>

                            <div className="flex justify-end">
                                <Button type="submit" disabled={processing}>
                                    Update HSN Code
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
