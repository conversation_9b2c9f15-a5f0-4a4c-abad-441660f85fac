import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Page } from '@inertiajs/core';
import { Head, Link, router } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { ArrowDown, ArrowUp, FileText, Pencil, Plus, RotateCcw, Search, Trash, X } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

interface Hsn {
    id: number;
    code: string;
    description: string;
    gst_rate: number;
    products_count: number;
}

interface Props {
    hsns: {
        data: Hsn[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search?: string | null;
        sort?: string | null;
        direction?: 'asc' | 'desc' | null;
        page?: number | null;
    };
}

interface PageProps {
    flash?: {
        message?: string;
        success?: string;
        error?: string;
    };
    [key: string]: unknown;
}

const breadcrumbs = [
    {
        title: 'HSN Codes',
        href: route('products.hsns.index'),
    },
];

export default function Index({ hsns, filters = {} }: Props) {
    const defaultFilters = {
        search: null,
        sort: 'created_at',
        direction: 'desc' as const,
        page: 1,
        ...filters,
    };

    const [sortField, setSortField] = useState(defaultFilters.sort);
    const [sortDirection, setSortDirection] = useState(defaultFilters.direction);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [hsnToDelete, setHsnToDelete] = useState<Hsn | null>(null);

    const handleSort = (field: string) => {
        const direction = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
        setSortField(field);
        setSortDirection(direction);
        router.get(
            route('products.hsns.index'),
            {
                ...filters,
                sort: field,
                direction,
            },
            { preserveState: true },
        );
    };

    const handleSearch = useCallback(
        debounce((search: string) => {
            router.get(
                route('products.hsns.index'),
                {
                    ...filters,
                    search,
                    page: 1,
                },
                { preserveState: true },
            );
        }, 300),
        [filters],
    );

    const handleDeleteClick = (hsn: Hsn) => {
        setHsnToDelete(hsn);
        setDeleteDialogOpen(true);
    };

    const handleDeleteConfirm = () => {
        if (!hsnToDelete) return;

        router.delete(route('products.hsns.destroy', { hsn: hsnToDelete.id }), {
            onSuccess: (page: Page<PageProps>) => {
                setDeleteDialogOpen(false);
                setHsnToDelete(null);

                if (page.props.flash?.error) {
                    toast.error(page.props.flash.error);
                } else {
                    toast.success(page.props.flash?.success || 'HSN code deleted successfully');
                }
            },
            onError: () => {
                setDeleteDialogOpen(false);
                setHsnToDelete(null);
                toast.error('An unexpected error occurred');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="HSN Codes" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <FileText className="h-6 w-6" />
                        <h1 className="text-2xl font-bold">HSN Codes</h1>
                    </div>
                    <Link href={route('products.hsns.create')}>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add HSN Code
                        </Button>
                    </Link>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>HSN Code List</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Search */}
                            <div className="flex flex-col gap-4 md:flex-row md:items-center">
                                <div className="relative flex-1">
                                    <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                                    <Input
                                        placeholder="Search HSN codes..."
                                        defaultValue={filters.search ?? ''}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </div>

                            <div className="flex items-center justify-between border-b pb-4">
                                <div className="flex items-center gap-2">
                                    <Badge variant="outline" className="rounded-full">
                                        {hsns.total} HSN Codes
                                    </Badge>
                                    {filters.search && (
                                        <Badge variant="secondary" className="rounded-full">
                                            Search: {filters.search}
                                            <X className="ml-1 h-3 w-3 cursor-pointer" onClick={() => handleSearch('')} />
                                        </Badge>
                                    )}
                                </div>
                                {filters.search && (
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                            router.get(
                                                route('products.hsns.index'),
                                                {
                                                    search: '',
                                                    page: 1,
                                                },
                                                {
                                                    preserveState: true,
                                                    preserveScroll: true,
                                                },
                                            );
                                        }}
                                    >
                                        <RotateCcw className="mr-2 h-4 w-4" />
                                        Reset Filters
                                    </Button>
                                )}
                            </div>

                            <div className="rounded-md border">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead onClick={() => handleSort('code')} className="cursor-pointer">
                                                Code{' '}
                                                {sortField === 'code' &&
                                                    (sortDirection === 'asc' ? (
                                                        <ArrowUp className="ml-2 inline h-4 w-4" />
                                                    ) : (
                                                        <ArrowDown className="ml-2 inline h-4 w-4" />
                                                    ))}
                                            </TableHead>
                                            <TableHead>Description</TableHead>
                                            <TableHead>GST Rate</TableHead>
                                            <TableHead>Products</TableHead>
                                            <TableHead>Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {hsns.data.map((hsn) => (
                                            <TableRow key={hsn.id}>
                                                <TableCell>{hsn.code}</TableCell>
                                                <TableCell>{hsn.description}</TableCell>
                                                <TableCell>{hsn.gst_rate}%</TableCell>
                                                <TableCell>{hsn.products_count}</TableCell>
                                                <TableCell>
                                                    <div className="flex gap-2">
                                                        <Link href={route('products.hsns.edit', { hsn: hsn.id })}>
                                                            <Button size="sm" variant="outline">
                                                                <Pencil className="h-4 w-4" />
                                                            </Button>
                                                        </Link>
                                                        <Button size="sm" variant="destructive" onClick={() => handleDeleteClick(hsn)}>
                                                            <Trash className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>

                            {/* Pagination */}
                            {hsns.links && hsns.links.length > 3 && (
                                <div className="mt-4">
                                    <Pagination>
                                        <PaginationContent>
                                            {hsns.links.map((link, i) => {
                                                if (i === 0) {
                                                    return (
                                                        <PaginationItem key={i}>
                                                            <PaginationPrevious
                                                                href="#"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    if (!link.url) return;
                                                                    const page = new URL(link.url).searchParams.get('page');
                                                                    router.get(
                                                                        route('products.hsns.index'),
                                                                        {
                                                                            ...filters,
                                                                            page: Number(page),
                                                                        },
                                                                        { preserveState: true },
                                                                    );
                                                                }}
                                                                className={!link.url ? 'pointer-events-none opacity-50' : ''}
                                                            />
                                                        </PaginationItem>
                                                    );
                                                }

                                                if (i === hsns.links.length - 1) {
                                                    return (
                                                        <PaginationItem key={i}>
                                                            <PaginationNext
                                                                href="#"
                                                                onClick={(e) => {
                                                                    e.preventDefault();
                                                                    if (!link.url) return;
                                                                    const page = new URL(link.url).searchParams.get('page');
                                                                    router.get(
                                                                        route('products.hsns.index'),
                                                                        {
                                                                            ...filters,
                                                                            page: Number(page),
                                                                        },
                                                                        { preserveState: true },
                                                                    );
                                                                }}
                                                                className={!link.url ? 'pointer-events-none opacity-50' : ''}
                                                            />
                                                        </PaginationItem>
                                                    );
                                                }

                                                return (
                                                    <PaginationItem key={i}>
                                                        <PaginationLink
                                                            href="#"
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                if (!link.url) return;
                                                                const page = new URL(link.url).searchParams.get('page');
                                                                router.get(
                                                                    route('products.hsns.index'),
                                                                    {
                                                                        ...filters,
                                                                        page: Number(page),
                                                                    },
                                                                    { preserveState: true },
                                                                );
                                                            }}
                                                            isActive={link.active}
                                                        >
                                                            {link.label}
                                                        </PaginationLink>
                                                    </PaginationItem>
                                                );
                                            })}
                                        </PaginationContent>
                                    </Pagination>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>

            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            {hsnToDelete && (
                                <>
                                    This action will delete the HSN code "{hsnToDelete.code}".
                                    {hsnToDelete.products_count > 0 && (
                                        <span className="text-red-500">
                                            {' '}
                                            This HSN code is being used by {hsnToDelete.products_count} products. You must update or delete these
                                            products first.
                                        </span>
                                    )}
                                </>
                            )}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDeleteConfirm}
                            className="bg-destructive hover:bg-destructive/90 text-white dark:bg-red-600 dark:text-white dark:hover:bg-red-700"
                        >
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
