import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { Plus, Search, Edit, Eye, Trash2, Clock, Calendar, Crown, Sparkles, Users, Building } from 'lucide-react';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    company_name: string;
    company_domain: string;
}

interface SubscriptionPlan {
    id: number;
    name: string;
    price: number;
    billing_cycle: string;
}

interface VendorSubscription {
    id: number;
    user: User;
    subscription_plan: SubscriptionPlan;
    starts_at: string;
    ends_at: string;
    trial_ends_at?: string;
    status: string;
    current_services_count: number;
    current_appointments_count: number;
    current_seats_count: number;
    current_branches_count: number;
    current_staff_count: number;
}

interface Props {
    subscriptions: {
        data: VendorSubscription[];
        links: any[];
        meta: any;
    };
    plans: SubscriptionPlan[];
    filters: {
        search?: string;
        status?: string;
        plan_id?: string;
    };
}

const breadcrumbs = [
    { title: '👑 Admin Dashboard', href: route('admin.dashboard') },
    { title: '🏢 Vendor Subscriptions', href: route('admin.vendor-subscriptions.index') },
];

export default function VendorSubscriptionsIndex({ subscriptions, plans, filters }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const [status, setStatus] = useState(filters.status || 'all');
    const [planId, setPlanId] = useState(filters.plan_id || 'all');
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [subscriptionToDelete, setSubscriptionToDelete] = useState<VendorSubscription | null>(null);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        const searchParams: any = { search };

        // Only include status and plan_id if they're not "all"
        if (status !== 'all') {
            searchParams.status = status;
        }
        if (planId !== 'all') {
            searchParams.plan_id = planId;
        }

        router.get(route('admin.vendor-subscriptions.index'), searchParams, {
            preserveState: true,
            replace: true,
        });
    };

    const handleDeleteClick = (subscription: VendorSubscription) => {
        setSubscriptionToDelete(subscription);
        setDeleteDialogOpen(true);
    };

    const handleDeleteConfirm = () => {
        if (subscriptionToDelete) {
            router.delete(route('admin.vendor-subscriptions.destroy', subscriptionToDelete.id));
            setDeleteDialogOpen(false);
            setSubscriptionToDelete(null);
        }
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            active: 'default',
            trial: 'secondary',
            expired: 'destructive',
            cancelled: 'outline',
            inactive: 'outline',
        } as const;

        return (
            <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="🏢 Salon Vendor Subscriptions" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <Building className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                Professional Vendor Subscriptions
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400">
                                Manage professional salon vendor subscription plans and billing
                            </p>
                        </div>
                    </div>
                    <Button asChild className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                        <Link href={route('admin.vendor-subscriptions.create')}>
                            <Plus className="h-4 w-4 mr-2" />
                            ✨ Create Subscription
                        </Link>
                    </Button>
                </div>

                {/* Search and Filters */}
                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Search className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                🔍 Search & Filter Subscriptions
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSearch} className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search salon vendors..."
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                />
                            </div>
                            <div className="w-48">
                                <Select value={status} onValueChange={setStatus}>
                                    <SelectTrigger className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700">
                                        <SelectValue placeholder="All statuses" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Statuses</SelectItem>
                                        <SelectItem value="active">✨ Active</SelectItem>
                                        <SelectItem value="trial">🆓 Trial</SelectItem>
                                        <SelectItem value="expired">⏰ Expired</SelectItem>
                                        <SelectItem value="cancelled">❌ Cancelled</SelectItem>
                                        <SelectItem value="inactive">💤 Inactive</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="w-48">
                                <Select value={planId} onValueChange={setPlanId}>
                                    <SelectTrigger className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700">
                                        <SelectValue placeholder="All plans" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Plans</SelectItem>
                                        {plans.map((plan) => (
                                            <SelectItem key={plan.id} value={plan.id.toString()}>
                                                💎 {plan.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <Button type="submit" className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </form>
                    </CardContent>
                </Card>

                {/* Subscriptions Table */}
                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Users className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                🏢 Subscriptions ({subscriptions.meta?.total || subscriptions.data?.length || 0})
                            </CardTitle>
                        </div>
                        <CardDescription className="text-slate-600 dark:text-slate-400">
                            All professional salon vendor subscriptions in the system
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="rounded-lg border border-slate-200/50 dark:border-slate-700/50 bg-white/70 dark:bg-slate-950/20 shadow-sm">
                            <Table>
                                <TableHeader>
                                    <TableRow className="border-slate-200/50 dark:border-slate-700/50 hover:bg-slate-50/50 dark:hover:bg-slate-950/30">
                                        <TableHead className="text-slate-800 dark:text-slate-200">👤 Vendor</TableHead>
                                        <TableHead className="text-slate-800 dark:text-slate-200">💎 Plan</TableHead>
                                        <TableHead className="text-slate-800 dark:text-slate-200">📊 Status</TableHead>
                                        <TableHead className="text-slate-800 dark:text-slate-200">📅 Period</TableHead>
                                        <TableHead className="text-slate-800 dark:text-slate-200">📈 Usage</TableHead>
                                        <TableHead className="text-slate-800 dark:text-slate-200">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                            <TableBody>
                                {subscriptions.data.map((subscription) => (
                                    <TableRow key={subscription.id}>
                                        <TableCell>
                                            <div>
                                                <div className="font-medium">{subscription.user.name}</div>
                                                <div className="text-sm text-muted-foreground">
                                                    {subscription.user.company_name}
                                                </div>
                                                <div className="text-xs text-muted-foreground">
                                                    {subscription.user.company_domain}.salon.test
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div>
                                                <div className="font-medium">{subscription.subscription_plan.name}</div>
                                                <div className="text-sm text-muted-foreground">
                                                    ₹{subscription.subscription_plan.price.toLocaleString('en-IN')} per {subscription.subscription_plan.billing_cycle}
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            {getStatusBadge(subscription.status)}
                                        </TableCell>
                                        <TableCell>
                                            <div className="text-sm">
                                                <div className="flex items-center gap-1">
                                                    <Calendar className="h-3 w-3" />
                                                    {formatDate(subscription.starts_at)} - {formatDate(subscription.ends_at)}
                                                </div>
                                                {subscription.trial_ends_at && (
                                                    <div className="flex items-center gap-1 text-muted-foreground">
                                                        <Clock className="h-3 w-3" />
                                                        Trial ends: {formatDate(subscription.trial_ends_at)}
                                                    </div>
                                                )}
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="text-xs space-y-1">
                                                <div>Services: {subscription.current_services_count}</div>
                                                <div>Appointments: {subscription.current_appointments_count}</div>
                                                <div>Seats: {subscription.current_seats_count}</div>
                                                <div>Branches: {subscription.current_branches_count}</div>
                                                <div>Staff: {subscription.current_staff_count}</div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex justify-end gap-2">
                                                <Link href={route('admin.vendor-subscriptions.show', subscription.id)}>
                                                    <Button variant="outline" size="icon" className="h-8 w-8 border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30">
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                                <Link href={route('admin.vendor-subscriptions.edit', subscription.id)}>
                                                    <Button variant="outline" size="icon" className="h-8 w-8 border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30">
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                                <Button
                                                    variant="outline"
                                                    size="icon"
                                                    className="h-8 w-8 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-950/30"
                                                    onClick={() => handleDeleteClick(subscription)}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        </div>

                        {subscriptions.data.length === 0 && (
                            <div className="text-center py-12">
                                <div className="rounded-full bg-slate-100 dark:bg-slate-900/30 w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <Building className="h-8 w-8 text-slate-600 dark:text-slate-400" />
                                </div>
                                <p className="text-slate-600 dark:text-slate-400 text-lg font-medium mb-2">🏢 No professional vendor subscriptions found</p>
                                <p className="text-slate-500 dark:text-slate-500 mb-4">Create your first professional salon vendor subscription to get started</p>
                                <Button asChild className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                                    <Link href={route('admin.vendor-subscriptions.create')}>
                                        <Plus className="h-4 w-4 mr-2" />
                                        ✨ Create your first subscription
                                    </Link>
                                </Button>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>🏢 Delete Vendor Subscription</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete the subscription for "{subscriptionToDelete?.user.company_name}"? This action cannot be undone and will permanently remove this salon vendor's subscription from the system.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDeleteConfirm}
                            className="bg-red-600 text-white hover:bg-red-700"
                        >
                            Delete Subscription
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
