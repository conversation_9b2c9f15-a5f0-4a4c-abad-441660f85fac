import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { ArrowLeft, Edit, Calendar, Clock, Users, Building, Wrench, Car, UserCheck, Crown, Sparkles, Eye } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
    company_name: string;
    company_domain: string;
}

interface SubscriptionPlan {
    id: number;
    name: string;
    description: string;
    price: number;
    billing_cycle: string;
    max_services: number;
    max_appointments_per_month: number;
    max_seats: number;
    max_branches: number;
    max_staff: number;
    has_analytics: boolean;
    has_api_access: boolean;
    has_custom_branding: boolean;
    has_priority_support: boolean;
}

interface VendorSubscription {
    id: number;
    user: User;
    subscription_plan: SubscriptionPlan;
    starts_at: string;
    ends_at: string;
    trial_ends_at?: string;
    status: string;
    current_services_count: number;
    current_appointments_count: number;
    current_seats_count: number;
    current_branches_count: number;
    current_staff_count: number;
    billing_period_start: string;
    billing_period_end: string;
}

interface Props {
    subscription: VendorSubscription;
}

const breadcrumbs = [
    { title: '👑 Admin Dashboard', href: route('admin.dashboard') },
    { title: '🏢 Vendor Subscriptions', href: route('admin.vendor-subscriptions.index') },
    { title: '👁️ Subscription Details', href: '#' },
];

export default function ShowVendorSubscription({ subscription }: Props) {
    const getStatusBadge = (status: string) => {
        const variants = {
            active: 'default',
            trial: 'secondary',
            expired: 'destructive',
            cancelled: 'outline',
            inactive: 'outline',
        } as const;

        return (
            <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const calculateUsagePercentage = (current: number, max: number) => {
        if (max === 0) return 0; // Unlimited
        return Math.min((current / max) * 100, 100);
    };

    const getUsageColor = (percentage: number) => {
        if (percentage >= 90) return 'text-red-600';
        if (percentage >= 75) return 'text-orange-600';
        return 'text-green-600';
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`👁️ Subscription - ${subscription.user.company_name}`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-blue-500 to-indigo-500 p-3 shadow-lg">
                            <Building className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                                Subscription Details
                            </h1>
                            <p className="text-blue-600 dark:text-blue-400">
                                {subscription.user.company_name} - {subscription.subscription_plan.name}
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-3">
                        <Button variant="outline" asChild className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30">
                            <Link href={route('admin.vendor-subscriptions.edit', subscription.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                ✨ Edit Subscription
                            </Link>
                        </Button>
                        <Button variant="outline" asChild className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30">
                            <Link href={route('admin.vendor-subscriptions.index')}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to List
                            </Link>
                        </Button>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Vendor Information */}
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                        <CardHeader className="pb-4">
                            <div className="flex items-center gap-2">
                                <div className="rounded-full bg-slate-500/10 p-2">
                                    <Users className="h-5 w-5 text-slate-600" />
                                </div>
                                <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                    👤 Professional Vendor Information
                                </CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="text-sm font-medium text-slate-700 dark:text-slate-300">🏢 Company Name</label>
                                <p className="text-lg font-semibold text-slate-900 dark:text-slate-100">{subscription.user.company_name}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-blue-700 dark:text-blue-300">👤 Owner Name</label>
                                <p className="text-blue-800 dark:text-blue-200">{subscription.user.name}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-blue-700 dark:text-blue-300">📧 Email</label>
                                <p className="text-blue-800 dark:text-blue-200">{subscription.user.email}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-blue-700 dark:text-blue-300">🌐 Domain</label>
                                <p className="font-mono text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                                    {subscription.user.company_domain}.salon.test
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Subscription Information */}
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
                        <CardHeader className="pb-4">
                            <div className="flex items-center gap-2">
                                <div className="rounded-full bg-blue-500/10 p-2">
                                    <Crown className="h-5 w-5 text-blue-600" />
                                </div>
                                <CardTitle className="text-lg text-blue-800 dark:text-blue-200">
                                    💎 Subscription Information
                                </CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <label className="text-sm font-medium text-muted-foreground">Status</label>
                                {getStatusBadge(subscription.status)}
                            </div>
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Plan</label>
                                <p className="text-lg font-semibold">{subscription.subscription_plan.name}</p>
                                <p className="text-sm text-muted-foreground">{subscription.subscription_plan.description}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Price</label>
                                <p className="text-lg font-semibold">
                                    ₹{subscription.subscription_plan.price.toLocaleString('en-IN')}
                                    <span className="text-sm font-normal text-muted-foreground">
                                        /{subscription.subscription_plan.billing_cycle}
                                    </span>
                                </p>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                                        <Calendar className="h-3 w-3" />
                                        Start Date
                                    </label>
                                    <p className="text-sm">{formatDate(subscription.starts_at)}</p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                                        <Calendar className="h-3 w-3" />
                                        End Date
                                    </label>
                                    <p className="text-sm">{formatDate(subscription.ends_at)}</p>
                                </div>
                            </div>
                            {subscription.trial_ends_at && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                                        <Clock className="h-3 w-3" />
                                        Trial Ends
                                    </label>
                                    <p className="text-sm">{formatDate(subscription.trial_ends_at)}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Usage Statistics */}
                <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-blue-500/10 p-2">
                                <Sparkles className="h-5 w-5 text-blue-600" />
                            </div>
                            <CardTitle className="text-lg text-blue-800 dark:text-blue-200">
                                📊 Current Usage
                            </CardTitle>
                        </div>
                        <CardDescription className="text-blue-600 dark:text-blue-400">
                            Usage statistics for the current billing period
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
                            {/* Services */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Wrench className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm font-medium">Services</span>
                                </div>
                                <div className="text-2xl font-bold">
                                    {subscription.current_services_count}
                                    {subscription.subscription_plan.max_services > 0 && (
                                        <span className="text-sm font-normal text-muted-foreground">
                                            /{subscription.subscription_plan.max_services}
                                        </span>
                                    )}
                                </div>
                                {subscription.subscription_plan.max_services > 0 && (
                                    <div className="w-full bg-muted rounded-full h-2">
                                        <div
                                            className={`h-2 rounded-full transition-all ${
                                                calculateUsagePercentage(subscription.current_services_count, subscription.subscription_plan.max_services) >= 90
                                                    ? 'bg-red-500'
                                                    : calculateUsagePercentage(subscription.current_services_count, subscription.subscription_plan.max_services) >= 75
                                                    ? 'bg-orange-500'
                                                    : 'bg-green-500'
                                            }`}
                                            style={{
                                                width: `${calculateUsagePercentage(subscription.current_services_count, subscription.subscription_plan.max_services)}%`
                                            }}
                                        />
                                    </div>
                                )}
                            </div>

                            {/* Appointments */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm font-medium">Appointments</span>
                                </div>
                                <div className="text-2xl font-bold">
                                    {subscription.current_appointments_count}
                                    {subscription.subscription_plan.max_appointments_per_month > 0 && (
                                        <span className="text-sm font-normal text-muted-foreground">
                                            /{subscription.subscription_plan.max_appointments_per_month}
                                        </span>
                                    )}
                                </div>
                                {subscription.subscription_plan.max_appointments_per_month > 0 && (
                                    <div className="w-full bg-muted rounded-full h-2">
                                        <div
                                            className={`h-2 rounded-full transition-all ${
                                                calculateUsagePercentage(subscription.current_appointments_count, subscription.subscription_plan.max_appointments_per_month) >= 90
                                                    ? 'bg-red-500'
                                                    : calculateUsagePercentage(subscription.current_appointments_count, subscription.subscription_plan.max_appointments_per_month) >= 75
                                                    ? 'bg-orange-500'
                                                    : 'bg-green-500'
                                            }`}
                                            style={{
                                                width: `${calculateUsagePercentage(subscription.current_appointments_count, subscription.subscription_plan.max_appointments_per_month)}%`
                                            }}
                                        />
                                    </div>
                                )}
                            </div>

                            {/* Seats */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Car className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm font-medium">Seats</span>
                                </div>
                                <div className="text-2xl font-bold">
                                    {subscription.current_seats_count}
                                    {subscription.subscription_plan.max_seats > 0 && (
                                        <span className="text-sm font-normal text-muted-foreground">
                                            /{subscription.subscription_plan.max_seats}
                                        </span>
                                    )}
                                </div>
                                {subscription.subscription_plan.max_seats > 0 && (
                                    <div className="w-full bg-muted rounded-full h-2">
                                        <div
                                            className={`h-2 rounded-full transition-all ${
                                                calculateUsagePercentage(subscription.current_seats_count, subscription.subscription_plan.max_seats) >= 90
                                                    ? 'bg-red-500'
                                                    : calculateUsagePercentage(subscription.current_seats_count, subscription.subscription_plan.max_seats) >= 75
                                                    ? 'bg-orange-500'
                                                    : 'bg-green-500'
                                            }`}
                                            style={{
                                                width: `${calculateUsagePercentage(subscription.current_seats_count, subscription.subscription_plan.max_seats)}%`
                                            }}
                                        />
                                    </div>
                                )}
                            </div>

                            {/* Branches */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Building className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm font-medium">Branches</span>
                                </div>
                                <div className="text-2xl font-bold">
                                    {subscription.current_branches_count}
                                    <span className="text-sm font-normal text-muted-foreground">
                                        /{subscription.subscription_plan.max_branches}
                                    </span>
                                </div>
                                <div className="w-full bg-muted rounded-full h-2">
                                    <div
                                        className={`h-2 rounded-full transition-all ${
                                            calculateUsagePercentage(subscription.current_branches_count, subscription.subscription_plan.max_branches) >= 90
                                                ? 'bg-red-500'
                                                : calculateUsagePercentage(subscription.current_branches_count, subscription.subscription_plan.max_branches) >= 75
                                                ? 'bg-orange-500'
                                                : 'bg-green-500'
                                        }`}
                                        style={{
                                            width: `${calculateUsagePercentage(subscription.current_branches_count, subscription.subscription_plan.max_branches)}%`
                                        }}
                                    />
                                </div>
                            </div>

                            {/* Staff */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <UserCheck className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm font-medium">Staff</span>
                                </div>
                                <div className="text-2xl font-bold">
                                    {subscription.current_staff_count}
                                    {subscription.subscription_plan.max_staff > 0 && (
                                        <span className="text-sm font-normal text-muted-foreground">
                                            /{subscription.subscription_plan.max_staff}
                                        </span>
                                    )}
                                </div>
                                {subscription.subscription_plan.max_staff > 0 && (
                                    <div className="w-full bg-muted rounded-full h-2">
                                        <div
                                            className={`h-2 rounded-full transition-all ${
                                                calculateUsagePercentage(subscription.current_staff_count, subscription.subscription_plan.max_staff) >= 90
                                                    ? 'bg-red-500'
                                                    : calculateUsagePercentage(subscription.current_staff_count, subscription.subscription_plan.max_staff) >= 75
                                                    ? 'bg-orange-500'
                                                    : 'bg-green-500'
                                            }`}
                                            style={{
                                                width: `${calculateUsagePercentage(subscription.current_staff_count, subscription.subscription_plan.max_staff)}%`
                                            }}
                                        />
                                    </div>
                                )}
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
