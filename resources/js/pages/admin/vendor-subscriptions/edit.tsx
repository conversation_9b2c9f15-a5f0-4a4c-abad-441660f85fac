import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save, Crown, Sparkles, Building, Edit, Users } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
    company_name: string;
    company_domain: string;
}

interface SubscriptionPlan {
    id: number;
    name: string;
    description: string;
    price: number;
    billing_cycle: string;
    max_services: number;
    max_appointments_per_month: number;
    max_seats: number;
    max_branches: number;
    max_staff: number;
}

interface VendorSubscription {
    id: number;
    user: User;
    subscription_plan: SubscriptionPlan;
    starts_at: string;
    ends_at: string;
    trial_ends_at?: string;
    status: string;
}

interface Props {
    subscription: VendorSubscription;
    plans: SubscriptionPlan[];
}

const breadcrumbs = [
    { title: '👑 Admin Dashboard', href: route('admin.dashboard') },
    { title: '🏢 Vendor Subscriptions', href: route('admin.vendor-subscriptions.index') },
    { title: '✨ Edit Subscription', href: '#' },
];

export default function EditVendorSubscription({ subscription, plans }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        subscription_plan_id: subscription.subscription_plan.id.toString(),
        ends_at: subscription.ends_at.split('T')[0], // Convert to date format
        status: subscription.status,
        trial_ends_at: subscription.trial_ends_at ? subscription.trial_ends_at.split('T')[0] : '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.vendor-subscriptions.update', subscription.id));
    };

    const selectedPlan = plans.find(plan => plan.id === parseInt(data.subscription_plan_id));

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`✨ Edit Subscription - ${subscription.user.company_name}`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-blue-500 to-indigo-500 p-3 shadow-lg">
                            <Building className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                                Edit Vendor Subscription
                            </h1>
                            <p className="text-blue-600 dark:text-blue-400">
                                Update subscription settings for {subscription.user.company_name}
                            </p>
                        </div>
                    </div>
                    <Button variant="outline" asChild className="border-blue-200 text-blue-600 hover:bg-blue-50 dark:border-blue-700 dark:text-blue-400 dark:hover:bg-blue-950/30">
                        <Link href={route('admin.vendor-subscriptions.index')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Subscriptions
                        </Link>
                    </Button>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Vendor Information (Read-only) */}
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
                        <CardHeader className="pb-4">
                            <div className="flex items-center gap-2">
                                <div className="rounded-full bg-blue-500/10 p-2">
                                    <Users className="h-5 w-5 text-blue-600" />
                                </div>
                                <CardTitle className="text-lg text-blue-800 dark:text-blue-200">
                                    👤 Vendor Information
                                </CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="text-sm font-medium text-blue-700 dark:text-blue-300">🏢 Company Name</label>
                                <p className="text-lg font-semibold text-blue-900 dark:text-blue-100">{subscription.user.company_name}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-blue-700 dark:text-blue-300">👤 Owner Name</label>
                                <p className="text-blue-800 dark:text-blue-200">{subscription.user.name}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-blue-700 dark:text-blue-300">📧 Email</label>
                                <p className="text-blue-800 dark:text-blue-200">{subscription.user.email}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-blue-700 dark:text-blue-300">🌐 Domain</label>
                                <p className="font-mono text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                                    {subscription.user.company_domain}.salon.test
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Edit Form */}
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
                        <CardHeader className="pb-4">
                            <div className="flex items-center gap-2">
                                <div className="rounded-full bg-blue-500/10 p-2">
                                    <Edit className="h-5 w-5 text-blue-600" />
                                </div>
                                <CardTitle className="text-lg text-blue-800 dark:text-blue-200">
                                    ✨ Subscription Settings
                                </CardTitle>
                            </div>
                            <CardDescription className="text-blue-600 dark:text-blue-400">
                                Update the subscription plan and settings
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Subscription Plan */}
                                <div className="space-y-2">
                                    <Label htmlFor="subscription_plan_id" className="text-blue-700 dark:text-blue-300">💎 Subscription Plan *</Label>
                                    <Select
                                        value={data.subscription_plan_id}
                                        onValueChange={(value) => setData('subscription_plan_id', value)}
                                    >
                                        <SelectTrigger className="bg-white/70 dark:bg-blue-950/30 border-blue-200 dark:border-blue-700 focus:border-blue-400 focus:ring-blue-400">
                                            <SelectValue placeholder="Select a subscription plan" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {plans.map((plan) => (
                                                <SelectItem key={plan.id} value={plan.id.toString()}>
                                                    💎 {plan.name} - ₹{plan.price.toLocaleString('en-IN')} per {plan.billing_cycle}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.subscription_plan_id && (
                                        <p className="text-sm text-destructive">{errors.subscription_plan_id}</p>
                                    )}
                                </div>

                                {/* End Date */}
                                <div className="space-y-2">
                                    <Label htmlFor="ends_at" className="text-blue-700 dark:text-blue-300">📅 End Date *</Label>
                                    <Input
                                        id="ends_at"
                                        type="date"
                                        value={data.ends_at}
                                        onChange={(e) => setData('ends_at', e.target.value)}
                                        className="bg-white/70 dark:bg-blue-950/30 border-blue-200 dark:border-blue-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.ends_at && (
                                        <p className="text-sm text-destructive">{errors.ends_at}</p>
                                    )}
                                </div>

                                {/* Status */}
                                <div className="space-y-2">
                                    <Label htmlFor="status" className="text-blue-700 dark:text-blue-300">📊 Status *</Label>
                                    <Select
                                        value={data.status}
                                        onValueChange={(value) => setData('status', value)}
                                    >
                                        <SelectTrigger className="bg-white/70 dark:bg-blue-950/30 border-blue-200 dark:border-blue-700 focus:border-blue-400 focus:ring-blue-400">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="active">✨ Active</SelectItem>
                                            <SelectItem value="trial">🆓 Trial</SelectItem>
                                            <SelectItem value="inactive">💤 Inactive</SelectItem>
                                            <SelectItem value="cancelled">❌ Cancelled</SelectItem>
                                            <SelectItem value="expired">⏰ Expired</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.status && (
                                        <p className="text-sm text-destructive">{errors.status}</p>
                                    )}
                                </div>

                                {/* Trial End Date (only if status is trial) */}
                                {data.status === 'trial' && (
                                    <div className="space-y-2">
                                        <Label htmlFor="trial_ends_at">Trial End Date</Label>
                                        <Input
                                            id="trial_ends_at"
                                            type="date"
                                            value={data.trial_ends_at}
                                            onChange={(e) => setData('trial_ends_at', e.target.value)}
                                        />
                                        {errors.trial_ends_at && (
                                            <p className="text-sm text-destructive">{errors.trial_ends_at}</p>
                                        )}
                                    </div>
                                )}

                                {/* Submit Button */}
                                <div className="flex justify-end space-x-4">
                                    <Button variant="outline" asChild className="border-blue-200 text-blue-600 hover:bg-blue-50 dark:border-blue-700 dark:text-blue-400 dark:hover:bg-blue-950/30">
                                        <Link href={route('admin.vendor-subscriptions.index')}>
                                            Cancel
                                        </Link>
                                    </Button>
                                    <Button type="submit" disabled={processing} className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white shadow-lg">
                                        <Save className="h-4 w-4 mr-2" />
                                        {processing ? '✨ Updating...' : '✨ Update Subscription'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>

                {/* Plan Details */}
                {selectedPlan && (
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
                        <CardHeader className="pb-4">
                            <div className="flex items-center gap-2">
                                <div className="rounded-full bg-blue-500/10 p-2">
                                    <Crown className="h-5 w-5 text-blue-600" />
                                </div>
                                <CardTitle className="text-lg text-blue-800 dark:text-blue-200">
                                    💎 Selected Plan Details
                                </CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <h4 className="font-semibold mb-3 text-blue-800 dark:text-blue-200 flex items-center gap-2">
                                        <Sparkles className="h-4 w-4" />
                                        💎 Plan Information
                                    </h4>
                                    <div className="space-y-2 text-sm">
                                        <p className="text-blue-700 dark:text-blue-300"><span className="font-medium">Name:</span> {selectedPlan.name}</p>
                                        <p className="text-blue-700 dark:text-blue-300"><span className="font-medium">Price:</span> ₹{selectedPlan.price.toLocaleString('en-IN')} per {selectedPlan.billing_cycle}</p>
                                        <p className="text-blue-700 dark:text-blue-300"><span className="font-medium">Description:</span> {selectedPlan.description}</p>
                                    </div>
                                </div>
                                <div>
                                    <h4 className="font-semibold mb-3 text-blue-800 dark:text-blue-200 flex items-center gap-2">
                                        <Building className="h-4 w-4" />
                                        📊 Usage Limits
                                    </h4>
                                    <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                                        <p>💄 Services: {selectedPlan.max_services === 0 ? '∞ Unlimited' : selectedPlan.max_services}</p>
                                        <p>📅 Appointments: {selectedPlan.max_appointments_per_month === 0 ? '∞ Unlimited' : `${selectedPlan.max_appointments_per_month}/month`}</p>
                                        <p>💺 Seats: {selectedPlan.max_seats === 0 ? '∞ Unlimited' : selectedPlan.max_seats}</p>
                                        <p>🏢 Branches: {selectedPlan.max_branches}</p>
                                        <p>👥 Staff: {selectedPlan.max_staff === 0 ? '∞ Unlimited' : selectedPlan.max_staff}</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
