import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save, Crown, Sparkles, Building, Plus, Users } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
    company_name: string;
    company_domain: string;
}

interface SubscriptionPlan {
    id: number;
    name: string;
    description: string;
    price: number;
    billing_cycle: string;
    max_services: number;
    max_appointments_per_month: number;
    max_seats: number;
    max_branches: number;
    max_staff: number;
}

interface Props {
    vendors: User[];
    plans: SubscriptionPlan[];
    selectedVendor?: User;
}

const breadcrumbs = [
    { title: '👑 Admin Dashboard', href: route('admin.dashboard') },
    { title: '🏢 Vendor Subscriptions', href: route('admin.vendor-subscriptions.index') },
    { title: '✨ Create Subscription', href: route('admin.vendor-subscriptions.create') },
];

export default function CreateVendorSubscription({ vendors, plans, selectedVendor }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        user_id: selectedVendor?.id || '',
        subscription_plan_id: '',
        starts_at: new Date().toISOString().split('T')[0],
        billing_cycle_count: 1,
        status: 'active',
        trial_days: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.vendor-subscriptions.store'));
    };

    const selectedPlan = plans.find(plan => plan.id === parseInt(data.subscription_plan_id));

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✨ Create Salon Vendor Subscription" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <Building className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                Create Professional Subscription
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400">
                                Assign a subscription plan to a professional salon vendor
                            </p>
                        </div>
                    </div>
                    <Button variant="outline" asChild className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30">
                        <Link href={route('admin.vendor-subscriptions.index')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Subscriptions
                        </Link>
                    </Button>
                </div>

                {/* Form */}
                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Plus className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                ✨ New Subscription Configuration
                            </CardTitle>
                        </div>
                        <CardDescription className="text-slate-600 dark:text-slate-400">
                            Configure subscription plan and settings for the professional salon vendor
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid gap-6 md:grid-cols-2">
                                {/* Vendor Selection */}
                                <div className="space-y-2">
                                    <Label htmlFor="user_id" className="text-slate-700 dark:text-slate-300">🏢 Professional Salon Vendor *</Label>
                                    <Select
                                        value={data.user_id.toString()}
                                        onValueChange={(value) => setData('user_id', value)}
                                    >
                                        <SelectTrigger className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400">
                                            <SelectValue placeholder="Select a professional salon vendor" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {vendors.map((vendor) => (
                                                <SelectItem key={vendor.id} value={vendor.id.toString()}>
                                                    👤 {vendor.name} ({vendor.company_name})
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.user_id && (
                                        <p className="text-sm text-destructive">{errors.user_id}</p>
                                    )}
                                </div>

                                {/* Subscription Plan */}
                                <div className="space-y-2">
                                    <Label htmlFor="subscription_plan_id" className="text-slate-700 dark:text-slate-300">💎 Subscription Plan *</Label>
                                    <Select
                                        value={data.subscription_plan_id.toString()}
                                        onValueChange={(value) => setData('subscription_plan_id', value)}
                                    >
                                        <SelectTrigger className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400">
                                            <SelectValue placeholder="Select a subscription plan" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {plans.map((plan) => (
                                                <SelectItem key={plan.id} value={plan.id.toString()}>
                                                    💎 {plan.name} - ₹{plan.price.toLocaleString('en-IN')} per {plan.billing_cycle}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.subscription_plan_id && (
                                        <p className="text-sm text-destructive">{errors.subscription_plan_id}</p>
                                    )}
                                </div>

                                {/* Start Date */}
                                <div className="space-y-2">
                                    <Label htmlFor="starts_at">Start Date *</Label>
                                    <Input
                                        id="starts_at"
                                        type="date"
                                        value={data.starts_at}
                                        onChange={(e) => setData('starts_at', e.target.value)}
                                    />
                                    {errors.starts_at && (
                                        <p className="text-sm text-destructive">{errors.starts_at}</p>
                                    )}
                                </div>

                                {/* Billing Cycle Count */}
                                <div className="space-y-2">
                                    <Label htmlFor="billing_cycle_count">
                                        Duration ({selectedPlan?.billing_cycle || 'cycles'}) *
                                    </Label>
                                    <Input
                                        id="billing_cycle_count"
                                        type="number"
                                        min="1"
                                        max="12"
                                        value={data.billing_cycle_count}
                                        onChange={(e) => setData('billing_cycle_count', parseInt(e.target.value))}
                                    />
                                    {errors.billing_cycle_count && (
                                        <p className="text-sm text-destructive">{errors.billing_cycle_count}</p>
                                    )}
                                </div>

                                {/* Status */}
                                <div className="space-y-2">
                                    <Label htmlFor="status">Status *</Label>
                                    <Select
                                        value={data.status}
                                        onValueChange={(value) => setData('status', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="trial">Trial</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.status && (
                                        <p className="text-sm text-destructive">{errors.status}</p>
                                    )}
                                </div>

                                {/* Trial Days (only if status is trial) */}
                                {data.status === 'trial' && (
                                    <div className="space-y-2">
                                        <Label htmlFor="trial_days">Trial Days</Label>
                                        <Input
                                            id="trial_days"
                                            type="number"
                                            min="0"
                                            max="365"
                                            value={data.trial_days}
                                            onChange={(e) => setData('trial_days', e.target.value)}
                                            placeholder="e.g., 14"
                                        />
                                        {errors.trial_days && (
                                            <p className="text-sm text-destructive">{errors.trial_days}</p>
                                        )}
                                    </div>
                                )}
                            </div>

                            {/* Plan Details */}
                            {selectedPlan && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Plan Details</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div>
                                                <h4 className="font-medium">Limits</h4>
                                                <ul className="text-sm text-muted-foreground space-y-1">
                                                    <li>Services: {selectedPlan.max_services === 0 ? 'Unlimited' : selectedPlan.max_services}</li>
                                                    <li>Appointments: {selectedPlan.max_appointments_per_month === 0 ? 'Unlimited' : selectedPlan.max_appointments_per_month}/month</li>
                                                    <li>Seats: {selectedPlan.max_seats === 0 ? 'Unlimited' : selectedPlan.max_seats}</li>
                                                    <li>Branches: {selectedPlan.max_branches}</li>
                                                    <li>Staff: {selectedPlan.max_staff === 0 ? 'Unlimited' : selectedPlan.max_staff}</li>
                                                </ul>
                                            </div>
                                            <div>
                                                <h4 className="font-medium">Description</h4>
                                                <p className="text-sm text-muted-foreground">{selectedPlan.description}</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Submit Button */}
                            <div className="flex justify-end space-x-4">
                                <Button variant="outline" asChild className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30">
                                    <Link href={route('admin.vendor-subscriptions.index')}>
                                        Cancel
                                    </Link>
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                                    <Plus className="h-4 w-4 mr-2" />
                                    {processing ? '✨ Creating...' : '✨ Create Subscription'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
