import { Head, <PERSON>, router } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { <PERSON>Down, ArrowUp, Eye, Plus, Search, Store, Trash, X } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import Heading from '@/components/heading';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Page } from '@inertiajs/core';

interface Vendor {
    id: number;
    name: string;
    email: string;
    company_name: string;
    company_domain: string;
    tenant_id: string;
    created_at: string;
    is_active: boolean;
}

interface Props {
    vendors: {
        data: Vendor[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search?: string | null;
        sort?: string | null;
        direction?: 'asc' | 'desc' | null;
        page?: number | null;
    };
}

export default function VendorsIndex({ vendors, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [deleteVendor, setDeleteVendor] = useState<Vendor | null>(null);

    const debouncedSearch = useCallback(
        debounce((term: string) => {
            router.get(
                route('vendors.index'),
                { search: term || undefined },
                { preserveState: true, replace: true }
            );
        }, 300),
        []
    );

    const handleSearch = (value: string) => {
        setSearchTerm(value);
        debouncedSearch(value);
    };

    const clearSearch = () => {
        setSearchTerm('');
        router.get(route('vendors.index'), {}, { preserveState: true, replace: true });
    };

    const handleSort = (field: string) => {
        const direction = filters.sort === field && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            route('vendors.index'),
            { ...filters, sort: field, direction },
            { preserveState: true, replace: true }
        );
    };

    const handleDelete = () => {
        if (!deleteVendor) return;

        router.delete(route('vendors.destroy', deleteVendor.id), {
            onSuccess: () => {
                toast.success('Vendor deleted successfully');
                setDeleteVendor(null);
            },
            onError: () => {
                toast.error('Failed to delete vendor');
            },
        });
    };

    const getSortIcon = (field: string) => {
        if (filters.sort !== field) return null;
        return filters.direction === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
    };

    return (
        <AppLayout
            breadcrumbs={[
                { label: 'Dashboard', href: route('dashboard') },
                { label: 'Vendors', href: route('vendors.index') },
            ]}
        >
            <Head title="Vendors" />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <Heading level={1} className="flex items-center gap-2">
                        <Store className="h-6 w-6" />
                        Vendors
                        <Badge variant="secondary">{vendors.total}</Badge>
                    </Heading>
                </div>

                <div className="flex items-center gap-4">
                    <div className="relative flex-1 max-w-sm">
                        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input
                            placeholder="Search vendors..."
                            value={searchTerm}
                            onChange={(e) => handleSearch(e.target.value)}
                            className="pl-9 pr-9"
                        />
                        {searchTerm && (
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={clearSearch}
                                className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 p-0"
                            >
                                <X className="h-4 w-4" />
                            </Button>
                        )}
                    </div>
                </div>

                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>
                                    <Button
                                        variant="ghost"
                                        onClick={() => handleSort('name')}
                                        className="h-auto p-0 font-semibold hover:bg-transparent"
                                    >
                                        Name
                                        {getSortIcon('name')}
                                    </Button>
                                </TableHead>
                                <TableHead>
                                    <Button
                                        variant="ghost"
                                        onClick={() => handleSort('company_name')}
                                        className="h-auto p-0 font-semibold hover:bg-transparent"
                                    >
                                        Company
                                        {getSortIcon('company_name')}
                                    </Button>
                                </TableHead>
                                <TableHead>
                                    <Button
                                        variant="ghost"
                                        onClick={() => handleSort('company_domain')}
                                        className="h-auto p-0 font-semibold hover:bg-transparent"
                                    >
                                        Domain
                                        {getSortIcon('company_domain')}
                                    </Button>
                                </TableHead>
                                <TableHead>
                                    <Button
                                        variant="ghost"
                                        onClick={() => handleSort('email')}
                                        className="h-auto p-0 font-semibold hover:bg-transparent"
                                    >
                                        Email
                                        {getSortIcon('email')}
                                    </Button>
                                </TableHead>
                                <TableHead>
                                    <Button
                                        variant="ghost"
                                        onClick={() => handleSort('created_at')}
                                        className="h-auto p-0 font-semibold hover:bg-transparent"
                                    >
                                        Registered
                                        {getSortIcon('created_at')}
                                    </Button>
                                </TableHead>
                                <TableHead className="w-[100px]">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {vendors.data.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                                        No vendors found
                                    </TableCell>
                                </TableRow>
                            ) : (
                                vendors.data.map((vendor) => (
                                    <TableRow key={vendor.id}>
                                        <TableCell className="font-medium">{vendor.name}</TableCell>
                                        <TableCell>{vendor.company_name}</TableCell>
                                        <TableCell>
                                            <a
                                                href={`${window.location.protocol}//${vendor.company_domain}.salon.test`}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-blue-600 hover:underline"
                                            >
                                                {vendor.company_domain}.salon.test
                                            </a>
                                        </TableCell>
                                        <TableCell>{vendor.email}</TableCell>
                                        <TableCell>
                                            {new Date(vendor.created_at).toLocaleDateString()}
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    asChild
                                                    className="h-8 w-8 p-0"
                                                >
                                                    <Link href={route('vendors.show', vendor.id)}>
                                                        <Eye className="h-4 w-4" />
                                                    </Link>
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => setDeleteVendor(vendor)}
                                                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                                >
                                                    <Trash className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>

                {vendors.total > vendors.per_page && (
                    <div className="flex items-center justify-center gap-2">
                        {vendors.links.map((link, index) => (
                            <Button
                                key={index}
                                variant={link.active ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => link.url && router.get(link.url)}
                                disabled={!link.url}
                                dangerouslySetInnerHTML={{ __html: link.label }}
                            />
                        ))}
                    </div>
                )}
            </div>

            <AlertDialog open={!!deleteVendor} onOpenChange={() => setDeleteVendor(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Vendor</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete {deleteVendor?.name}? This will also delete their tenant database and cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDelete}
                            className="bg-red-600 text-white hover:bg-red-700"
                        >
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
