import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, ExternalLink, Store, Trash, User } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import Heading from '@/components/heading';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';

interface Vendor {
    id: number;
    name: string;
    email: string;
    company_name: string;
    company_domain: string;
    tenant_id: string;
    created_at: string;
    email_verified_at: string | null;
    is_active: boolean;
}

interface Tenant {
    id: string;
    created_at: string;
    updated_at: string;
}

interface Domain {
    id: number;
    domain: string;
    tenant_id: string;
    created_at: string;
    updated_at: string;
}

interface Props {
    vendor: Vendor;
    tenant: Tenant | null;
    domain: Domain | null;
}

export default function VendorShow({ vendor, tenant, domain }: Props) {
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);

    const handleDelete = () => {
        router.delete(route('vendors.destroy', vendor.id), {
            onSuccess: () => {
                toast.success('Vendor deleted successfully');
                router.visit(route('vendors.index'));
            },
            onError: () => {
                toast.error('Failed to delete vendor');
            },
        });
    };

    const vendorUrl = domain ? `${window.location.protocol}//${domain.domain}` : null;

    return (
        <AppLayout
            breadcrumbs={[
                { label: 'Dashboard', href: route('dashboard') },
                { label: 'Vendors', href: route('vendors.index') },
                { label: vendor.name },
            ]}
        >
            <Head title={`Vendor: ${vendor.name}`} />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="ghost" size="sm" asChild>
                            <Link href={route('vendors.index')}>
                                <ArrowLeft className="h-4 w-4" />
                                Back to Vendors
                            </Link>
                        </Button>
                        <Heading level={1} className="flex items-center gap-2">
                            <Store className="h-6 w-6" />
                            {vendor.name}
                            <Badge variant={vendor.is_active ? 'default' : 'secondary'}>
                                {vendor.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                        </Heading>
                    </div>
                    <div className="flex items-center gap-2">
                        {vendorUrl && (
                            <Button variant="outline" asChild>
                                <a href={vendorUrl} target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="h-4 w-4" />
                                    Visit Site
                                </a>
                            </Button>
                        )}
                        <Button
                            variant="destructive"
                            onClick={() => setShowDeleteDialog(true)}
                        >
                            <Trash className="h-4 w-4" />
                            Delete Vendor
                        </Button>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Vendor Information
                            </CardTitle>
                            <CardDescription>
                                Basic information about the vendor
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Name</label>
                                <p className="text-sm">{vendor.name}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Email</label>
                                <p className="text-sm">{vendor.email}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Company Name</label>
                                <p className="text-sm">{vendor.company_name}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Company Domain</label>
                                <p className="text-sm">{vendor.company_domain}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Email Verified</label>
                                <p className="text-sm">
                                    {vendor.email_verified_at ? (
                                        <Badge variant="default">Verified</Badge>
                                    ) : (
                                        <Badge variant="destructive">Not Verified</Badge>
                                    )}
                                </p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Registered</label>
                                <p className="text-sm">
                                    {new Date(vendor.created_at).toLocaleDateString('en-US', {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit',
                                    })}
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Store className="h-5 w-5" />
                                Tenant Information
                            </CardTitle>
                            <CardDescription>
                                Multi-tenancy configuration details
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {tenant ? (
                                <>
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Tenant ID</label>
                                        <p className="text-sm font-mono">{tenant.id}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Tenant Created</label>
                                        <p className="text-sm">
                                            {new Date(tenant.created_at).toLocaleDateString('en-US', {
                                                year: 'numeric',
                                                month: 'long',
                                                day: 'numeric',
                                                hour: '2-digit',
                                                minute: '2-digit',
                                            })}
                                        </p>
                                    </div>
                                    {domain && (
                                        <>
                                            <div>
                                                <label className="text-sm font-medium text-muted-foreground">Domain</label>
                                                <p className="text-sm">
                                                    <a
                                                        href={`${window.location.protocol}//${domain.domain}`}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="text-blue-600 hover:underline"
                                                    >
                                                        {domain.domain}
                                                    </a>
                                                </p>
                                            </div>
                                            <div>
                                                <label className="text-sm font-medium text-muted-foreground">Domain Created</label>
                                                <p className="text-sm">
                                                    {new Date(domain.created_at).toLocaleDateString('en-US', {
                                                        year: 'numeric',
                                                        month: 'long',
                                                        day: 'numeric',
                                                        hour: '2-digit',
                                                        minute: '2-digit',
                                                    })}
                                                </p>
                                            </div>
                                        </>
                                    )}
                                </>
                            ) : (
                                <div className="text-center py-8 text-muted-foreground">
                                    <Store className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                    <p>No tenant information available</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>

            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Vendor</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete {vendor.name}? This will permanently delete their tenant database and all associated data. This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDelete}
                            className="bg-red-600 text-white hover:bg-red-700"
                        >
                            Delete Vendor
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
