import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';
import {
    Users,
    CreditCard,
    AlertTriangle,
    Clock,
    IndianRupee,
    Plus,
    Eye,
    Settings,
    TrendingUp,
    Building2,
    Calendar,
    Star,
    Activity,
    ArrowUpRight,
    ArrowDownRight,
    Zap
} from 'lucide-react';

interface DashboardStats {
    total_vendors: number;
    active_subscriptions: number;
    expired_subscriptions: number;
    trial_subscriptions: number;
    total_revenue: number;
}

interface RecentVendor {
    id: number;
    name: string;
    email: string;
    company_name: string;
    company_domain: string;
    created_at: string;
    subscription: {
        plan_name: string;
        status: string;
        ends_at: string;
    } | null;
}

interface PlanStat {
    id: number;
    name: string;
    price: string;
    active_subscriptions: number;
    revenue: number;
}

interface ExpiringSubscription {
    id: number;
    vendor_name: string;
    company_name: string;
    plan_name: string;
    ends_at: string;
    days_until_expiry: number;
}

interface Props {
    stats: DashboardStats;
    recentVendors: RecentVendor[];
    planStats: PlanStat[];
    expiringSubscriptions: ExpiringSubscription[];
}

const breadcrumbs = [
    {
        title: 'Admin Dashboard',
        href: route('admin.dashboard'),
    },
];

export default function AdminDashboard({
    stats,
    recentVendors,
    planStats,
    expiringSubscriptions
}: Props) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Admin Dashboard" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="space-y-1">
                        <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-slate-700 via-blue-700 to-indigo-700 bg-clip-text text-transparent">
                            Professional Salon Admin Dashboard
                        </h1>
                        <p className="text-lg text-muted-foreground">
                            ✨ Welcome back! Manage your professional salon empire with elegance and style.
                        </p>
                    </div>
                    <div className="flex items-center gap-3">
                        <Button size="lg" asChild className="shadow-lg bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white">
                            <Link href={route('admin.vendor-subscriptions.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                New Subscription
                            </Link>
                        </Button>
                        <Button variant="outline" size="lg" asChild className="shadow-sm border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30">
                            <Link href={route('admin.subscription-plans.index')}>
                                <Settings className="h-4 w-4 mr-2" />
                                Manage Plans
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
                    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-slate-50 to-blue-100 dark:from-slate-950 dark:to-blue-900 shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-slate-700 dark:text-slate-300">Professional Salons</CardTitle>
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Users className="h-4 w-4 text-slate-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-slate-900 dark:text-slate-100">{stats.total_vendors}</div>
                            <div className="flex items-center text-xs text-slate-600 mt-1">
                                <Building2 className="h-3 w-3 mr-1" />
                                Registered salons
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-emerald-50 to-teal-100 dark:from-emerald-950 dark:to-teal-900 shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-emerald-700 dark:text-emerald-300">Active Members</CardTitle>
                            <div className="rounded-full bg-emerald-500/10 p-2">
                                <CreditCard className="h-4 w-4 text-emerald-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-emerald-900 dark:text-emerald-100">{stats.active_subscriptions}</div>
                            <div className="flex items-center text-xs text-emerald-600 mt-1">
                                <ArrowUpRight className="h-3 w-3 mr-1" />
                                Premium members
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-amber-50 to-orange-100 dark:from-amber-950 dark:to-orange-900 shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-amber-700 dark:text-amber-300">Trial Members</CardTitle>
                            <div className="rounded-full bg-amber-500/10 p-2">
                                <Clock className="h-4 w-4 text-amber-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-amber-900 dark:text-amber-100">{stats.trial_subscriptions}</div>
                            <div className="flex items-center text-xs text-amber-600 mt-1">
                                <Zap className="h-3 w-3 mr-1" />
                                Exploring services
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-red-50 to-rose-100 dark:from-red-950 dark:to-rose-900 shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-red-700 dark:text-red-300">Expired</CardTitle>
                            <div className="rounded-full bg-red-500/10 p-2">
                                <AlertTriangle className="h-4 w-4 text-red-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-red-900 dark:text-red-100">{stats.expired_subscriptions}</div>
                            <div className="flex items-center text-xs text-red-600 mt-1">
                                <ArrowDownRight className="h-3 w-3 mr-1" />
                                Need renewal
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-indigo-50 to-blue-100 dark:from-indigo-950 dark:to-blue-900 shadow-lg">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-indigo-700 dark:text-indigo-300">Monthly Revenue</CardTitle>
                            <div className="rounded-full bg-indigo-500/10 p-2">
                                <IndianRupee className="h-4 w-4 text-indigo-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-indigo-900 dark:text-indigo-100">₹{stats.total_revenue.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</div>
                            <div className="flex items-center text-xs text-indigo-600 mt-1">
                                <TrendingUp className="h-3 w-3 mr-1" />
                                Professional earnings
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Recent Vendors */}
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-rose-50 to-pink-50 dark:from-rose-950 dark:to-pink-950">
                        <CardHeader className="pb-4">
                            <div className="flex items-center gap-2">
                                <div className="rounded-full bg-rose-500/10 p-2">
                                    <Users className="h-5 w-5 text-rose-600" />
                                </div>
                                <div>
                                    <CardTitle className="text-lg text-rose-800 dark:text-rose-200">Recent Salon Partners</CardTitle>
                                    <CardDescription className="text-rose-600 dark:text-rose-400">Latest beauty business registrations</CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentVendors.map((vendor) => (
                                    <div key={vendor.id} className="flex items-center justify-between p-3 rounded-lg bg-white/70 dark:bg-rose-900/30 border border-rose-200/50 dark:border-rose-700/50">
                                        <div className="flex items-center gap-3">
                                            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-rose-500 to-pink-500 flex items-center justify-center text-white font-semibold text-sm shadow-md">
                                                {vendor.name.charAt(0).toUpperCase()}
                                            </div>
                                            <div>
                                                <p className="font-medium text-rose-900 dark:text-rose-100">{vendor.name}</p>
                                                <p className="text-sm text-rose-600 dark:text-rose-400">{vendor.company_name}</p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            {vendor.subscription ? (
                                                <Badge variant={vendor.subscription.status === 'active' ? 'default' : 'secondary'} className="shadow-sm bg-rose-100 text-rose-800 border-rose-300">
                                                    {vendor.subscription.plan_name}
                                                </Badge>
                                            ) : (
                                                <Badge variant="outline" className="shadow-sm border-rose-300 text-rose-600">No Plan</Badge>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                            <div className="mt-6">
                                <Button variant="outline" size="sm" asChild className="w-full shadow-sm border-rose-200 text-rose-600 hover:bg-rose-50">
                                    <Link href={route('vendors.index')}>
                                        <Eye className="h-4 w-4 mr-2" />
                                        View All Salon Partners
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Plan Statistics */}
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-950 dark:to-purple-950">
                        <CardHeader className="pb-4">
                            <div className="flex items-center gap-2">
                                <div className="rounded-full bg-violet-500/10 p-2">
                                    <Star className="h-5 w-5 text-violet-600" />
                                </div>
                                <div>
                                    <CardTitle className="text-lg text-violet-800 dark:text-violet-200">Membership Plans</CardTitle>
                                    <CardDescription className="text-violet-600 dark:text-violet-400">Beauty package performance</CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {planStats.map((plan) => (
                                    <div key={plan.id} className="flex items-center justify-between p-3 rounded-lg bg-white/70 dark:bg-violet-900/30 border border-violet-200/50 dark:border-violet-700/50">
                                        <div className="flex items-center gap-3">
                                            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-violet-500 to-purple-500 flex items-center justify-center text-white font-semibold text-xs shadow-md">
                                                {plan.active_subscriptions}
                                            </div>
                                            <div>
                                                <p className="font-medium text-violet-900 dark:text-violet-100">{plan.name}</p>
                                                <p className="text-sm text-violet-600 dark:text-violet-400">{plan.price}/{plan.name.includes('Yearly') ? 'year' : 'month'}</p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="font-medium text-sm text-violet-800 dark:text-violet-200">{plan.active_subscriptions} members</p>
                                            <p className="text-xs text-violet-600 dark:text-violet-400">₹{plan.revenue.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} earned</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                            <div className="mt-6">
                                <Button variant="outline" size="sm" asChild className="w-full shadow-sm border-violet-200 text-violet-600 hover:bg-violet-50">
                                    <Link href={route('admin.subscription-plans.index')}>
                                        <Settings className="h-4 w-4 mr-2" />
                                        Manage Beauty Plans
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Expiring Subscriptions */}
                {expiringSubscriptions.length > 0 && (
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950 dark:to-orange-950">
                        <CardHeader className="pb-4">
                            <div className="flex items-center gap-2">
                                <div className="rounded-full bg-amber-500/10 p-2">
                                    <AlertTriangle className="h-5 w-5 text-amber-600" />
                                </div>
                                <div>
                                    <CardTitle className="text-lg text-amber-800 dark:text-amber-200">
                                        💄 Memberships Expiring Soon (30 Days)
                                    </CardTitle>
                                    <CardDescription className="text-amber-600 dark:text-amber-400">
                                        Beauty partners needing renewal attention
                                    </CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="rounded-lg bg-white/70 dark:bg-amber-900/20 p-1">
                                <Table>
                                    <TableHeader>
                                        <TableRow className="border-amber-200 dark:border-amber-800">
                                            <TableHead className="text-amber-800 dark:text-amber-200">Salon Owner</TableHead>
                                            <TableHead className="text-amber-800 dark:text-amber-200">Beauty Salon</TableHead>
                                            <TableHead className="text-amber-800 dark:text-amber-200">Plan</TableHead>
                                            <TableHead className="text-amber-800 dark:text-amber-200">Expires</TableHead>
                                            <TableHead className="text-amber-800 dark:text-amber-200">Days Left</TableHead>
                                            <TableHead className="text-amber-800 dark:text-amber-200">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {expiringSubscriptions.map((subscription) => (
                                            <TableRow key={subscription.id} className="border-amber-100 dark:border-amber-900 hover:bg-amber-50/50 dark:hover:bg-amber-950/50">
                                                <TableCell className="font-medium text-amber-900 dark:text-amber-100">{subscription.vendor_name}</TableCell>
                                                <TableCell className="text-amber-800 dark:text-amber-200">{subscription.company_name}</TableCell>
                                                <TableCell>
                                                    <Badge variant="outline" className="border-amber-300 text-amber-700 dark:border-amber-700 dark:text-amber-300 bg-amber-50 dark:bg-amber-900/30">
                                                        {subscription.plan_name}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell className="text-sm text-amber-700 dark:text-amber-300">{subscription.ends_at}</TableCell>
                                                <TableCell>
                                                    <Badge variant={subscription.days_until_expiry <= 7 ? 'destructive' : 'secondary'} className="shadow-sm">
                                                        <Clock className="h-3 w-3 mr-1" />
                                                        {subscription.days_until_expiry} days
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    <Button variant="ghost" size="sm" asChild className="hover:bg-amber-100 dark:hover:bg-amber-900 text-amber-700 dark:text-amber-300">
                                                        <Link href={route('admin.vendor-subscriptions.show', subscription.id)}>
                                                            <Eye className="h-4 w-4" />
                                                        </Link>
                                                    </Button>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
