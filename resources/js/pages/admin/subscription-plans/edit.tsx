import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save, Crown, Sparkles, IndianRupee, Settings } from 'lucide-react';

interface SubscriptionPlan {
    id: number;
    name: string;
    description: string;
    price: number;
    billing_cycle: string;
    max_services: number;
    max_appointments_per_month: number;
    max_seats: number;
    max_branches: number;
    max_staff: number;
    has_analytics: boolean;
    has_api_access: boolean;
    has_custom_branding: boolean;
    has_priority_support: boolean;
    is_active: boolean;
    sort_order: number;
}

interface Props {
    plan: SubscriptionPlan;
}

const breadcrumbs = [
    { title: '👑 Admin Dashboard', href: route('admin.dashboard') },
    { title: '💎 Subscription Plans', href: route('admin.subscription-plans.index') },
    { title: '✨ Edit Plan', href: '#' },
];

export default function EditSubscriptionPlan({ plan }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        name: plan.name,
        description: plan.description || '',
        price: plan.price.toString(),
        billing_cycle: plan.billing_cycle,
        max_services: plan.max_services.toString(),
        max_appointments_per_month: plan.max_appointments_per_month.toString(),
        max_seats: plan.max_seats.toString(),
        max_branches: plan.max_branches.toString(),
        max_staff: plan.max_staff.toString(),
        has_analytics: plan.has_analytics,
        has_api_access: plan.has_api_access,
        has_custom_branding: plan.has_custom_branding,
        has_priority_support: plan.has_priority_support,
        is_active: plan.is_active,
        sort_order: plan.sort_order.toString(),
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.subscription-plans.update', plan.id));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`✨ Edit Plan - ${plan.name}`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-violet-500 to-purple-500 p-3 shadow-lg">
                            <Crown className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                                Edit Subscription Plan
                            </h1>
                            <p className="text-violet-600 dark:text-violet-400">
                                Update pricing tier settings and features for "{plan.name}"
                            </p>
                        </div>
                    </div>
                    <Button variant="outline" asChild className="border-violet-200 text-violet-600 hover:bg-violet-50 dark:border-violet-700 dark:text-violet-400 dark:hover:bg-violet-950/30">
                        <Link href={route('admin.subscription-plans.index')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Plans
                        </Link>
                    </Button>
                </div>

                {/* Form */}
                <Card className="shadow-lg border-0 bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-950 dark:to-purple-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-violet-500/10 p-2">
                                <Settings className="h-5 w-5 text-violet-600" />
                            </div>
                            <CardTitle className="text-lg text-violet-800 dark:text-violet-200">
                                💎 Plan Configuration
                            </CardTitle>
                        </div>
                        <CardDescription className="text-violet-600 dark:text-violet-400">
                            Configure subscription plan settings, pricing, and features for salon vendors
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid gap-6 md:grid-cols-2">
                                {/* Basic Information */}
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-violet-800 dark:text-violet-200 flex items-center gap-2">
                                        <Crown className="h-5 w-5" />
                                        💎 Basic Information
                                    </h3>

                                    <div className="space-y-2">
                                        <Label htmlFor="name" className="text-violet-700 dark:text-violet-300">💎 Plan Name *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="e.g., Professional Salon Plan"
                                            className="bg-white/70 dark:bg-violet-950/30 border-violet-200 dark:border-violet-700 focus:border-violet-400 focus:ring-violet-400"
                                        />
                                        {errors.name && (
                                            <p className="text-sm text-destructive">{errors.name}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="description" className="text-violet-700 dark:text-violet-300">📝 Description</Label>
                                        <Textarea
                                            id="description"
                                            value={data.description}
                                            onChange={(e) => setData('description', e.target.value)}
                                            placeholder="Brief description of the salon plan features"
                                            rows={3}
                                            className="bg-white/70 dark:bg-violet-950/30 border-violet-200 dark:border-violet-700 focus:border-violet-400 focus:ring-violet-400"
                                        />
                                        {errors.description && (
                                            <p className="text-sm text-destructive">{errors.description}</p>
                                        )}
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="price">Price (₹) *</Label>
                                            <Input
                                                id="price"
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                value={data.price}
                                                onChange={(e) => setData('price', e.target.value)}
                                                placeholder="2499.00"
                                            />
                                            {errors.price && (
                                                <p className="text-sm text-destructive">{errors.price}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="billing_cycle">Billing Cycle *</Label>
                                            <Select
                                                value={data.billing_cycle}
                                                onValueChange={(value) => setData('billing_cycle', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="monthly">Monthly</SelectItem>
                                                    <SelectItem value="yearly">Yearly</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.billing_cycle && (
                                                <p className="text-sm text-destructive">{errors.billing_cycle}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="sort_order">Sort Order</Label>
                                            <Input
                                                id="sort_order"
                                                type="number"
                                                min="0"
                                                value={data.sort_order}
                                                onChange={(e) => setData('sort_order', e.target.value)}
                                            />
                                        </div>

                                        <div className="flex items-center space-x-2 pt-6">
                                            <Checkbox
                                                id="is_active"
                                                checked={data.is_active}
                                                onCheckedChange={(checked) => setData('is_active', !!checked)}
                                            />
                                            <Label htmlFor="is_active">Active Plan</Label>
                                        </div>
                                    </div>
                                </div>

                                {/* Limits */}
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-violet-800 dark:text-violet-200 flex items-center gap-2">
                                        <Sparkles className="h-5 w-5" />
                                        📊 Usage Limits
                                    </h3>
                                    <p className="text-sm text-violet-600 dark:text-violet-400">Set 0 for unlimited access</p>

                                    <div className="space-y-2">
                                        <Label htmlFor="max_services">Max Services *</Label>
                                        <Input
                                            id="max_services"
                                            type="number"
                                            min="0"
                                            value={data.max_services}
                                            onChange={(e) => setData('max_services', e.target.value)}
                                            placeholder="0 for unlimited"
                                        />
                                        {errors.max_services && (
                                            <p className="text-sm text-destructive">{errors.max_services}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="max_appointments_per_month">Max Appointments per Month *</Label>
                                        <Input
                                            id="max_appointments_per_month"
                                            type="number"
                                            min="0"
                                            value={data.max_appointments_per_month}
                                            onChange={(e) => setData('max_appointments_per_month', e.target.value)}
                                            placeholder="0 for unlimited"
                                        />
                                        {errors.max_appointments_per_month && (
                                            <p className="text-sm text-destructive">{errors.max_appointments_per_month}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="max_seats">Max Seats *</Label>
                                        <Input
                                            id="max_seats"
                                            type="number"
                                            min="0"
                                            value={data.max_seats}
                                            onChange={(e) => setData('max_seats', e.target.value)}
                                            placeholder="0 for unlimited"
                                        />
                                        {errors.max_seats && (
                                            <p className="text-sm text-destructive">{errors.max_seats}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="max_branches">Max Branches *</Label>
                                        <Input
                                            id="max_branches"
                                            type="number"
                                            min="1"
                                            value={data.max_branches}
                                            onChange={(e) => setData('max_branches', e.target.value)}
                                        />
                                        {errors.max_branches && (
                                            <p className="text-sm text-destructive">{errors.max_branches}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="max_staff">Max Staff *</Label>
                                        <Input
                                            id="max_staff"
                                            type="number"
                                            min="0"
                                            value={data.max_staff}
                                            onChange={(e) => setData('max_staff', e.target.value)}
                                            placeholder="0 for unlimited"
                                        />
                                        {errors.max_staff && (
                                            <p className="text-sm text-destructive">{errors.max_staff}</p>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Features */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-violet-800 dark:text-violet-200 flex items-center gap-2">
                                    <Sparkles className="h-5 w-5" />
                                    ✨ Premium Features
                                </h3>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="has_analytics"
                                            checked={data.has_analytics}
                                            onCheckedChange={(checked) => setData('has_analytics', !!checked)}
                                            className="data-[state=checked]:bg-violet-600 data-[state=checked]:border-violet-600"
                                        />
                                        <Label htmlFor="has_analytics" className="text-violet-700 dark:text-violet-300">📊 Analytics</Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="has_api_access"
                                            checked={data.has_api_access}
                                            onCheckedChange={(checked) => setData('has_api_access', !!checked)}
                                            className="data-[state=checked]:bg-violet-600 data-[state=checked]:border-violet-600"
                                        />
                                        <Label htmlFor="has_api_access" className="text-violet-700 dark:text-violet-300">🔌 API Access</Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="has_custom_branding"
                                            checked={data.has_custom_branding}
                                            onCheckedChange={(checked) => setData('has_custom_branding', !!checked)}
                                            className="data-[state=checked]:bg-violet-600 data-[state=checked]:border-violet-600"
                                        />
                                        <Label htmlFor="has_custom_branding" className="text-violet-700 dark:text-violet-300">🎨 Custom Branding</Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="has_priority_support"
                                            checked={data.has_priority_support}
                                            onCheckedChange={(checked) => setData('has_priority_support', !!checked)}
                                            className="data-[state=checked]:bg-violet-600 data-[state=checked]:border-violet-600"
                                        />
                                        <Label htmlFor="has_priority_support" className="text-violet-700 dark:text-violet-300">⚡ Priority Support</Label>
                                    </div>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <div className="flex justify-end space-x-4">
                                <Button variant="outline" asChild className="border-violet-200 text-violet-600 hover:bg-violet-50 dark:border-violet-700 dark:text-violet-400 dark:hover:bg-violet-950/30">
                                    <Link href={route('admin.subscription-plans.index')}>
                                        Cancel
                                    </Link>
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-violet-500 to-purple-500 hover:from-violet-600 hover:to-purple-600 text-white shadow-lg">
                                    <Save className="h-4 w-4 mr-2" />
                                    {processing ? '✨ Updating...' : '✨ Update Plan'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
