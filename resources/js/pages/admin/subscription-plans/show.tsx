import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { <PERSON>, Link } from '@inertiajs/react';
import { ArrowLeft, Edit, Users, IndianRupee, Check, X, Crown, Sparkles, Eye, Settings } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
    company_name: string;
    company_domain: string;
}

interface VendorSubscription {
    id: number;
    user: User;
    starts_at: string;
    ends_at: string;
    status: string;
    created_at: string;
}

interface SubscriptionPlan {
    id: number;
    name: string;
    description: string;
    price: number;
    billing_cycle: string;
    max_services: number;
    max_appointments_per_month: number;
    max_seats: number;
    max_branches: number;
    max_staff: number;
    has_analytics: boolean;
    has_api_access: boolean;
    has_custom_branding: boolean;
    has_priority_support: boolean;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    vendor_subscriptions_count: number;
    active_vendor_subscriptions_count: number;
}

interface Props {
    plan: SubscriptionPlan;
    subscriptions: {
        data: VendorSubscription[];
        links: any[];
        meta: any;
    };
}

const breadcrumbs = [
    { title: '👑 Admin Dashboard', href: route('admin.dashboard') },
    { title: '💎 Subscription Plans', href: route('admin.subscription-plans.index') },
    { title: '👁️ Plan Details', href: '#' },
];

export default function ShowSubscriptionPlan({ plan, subscriptions }: Props) {
    const getStatusBadge = (status: string) => {
        const variants = {
            active: 'default',
            trial: 'secondary',
            expired: 'destructive',
            cancelled: 'outline',
            inactive: 'outline',
        } as const;

        return (
            <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`👁️ Plan - ${plan.name}`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-violet-500 to-purple-500 p-3 shadow-lg">
                            <Crown className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                                {plan.name}
                            </h1>
                            <p className="text-violet-600 dark:text-violet-400">
                                {plan.description || 'Subscription plan details and active subscribers'}
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-3">
                        <Button variant="outline" asChild className="border-violet-200 text-violet-600 hover:bg-violet-50 dark:border-violet-700 dark:text-violet-400 dark:hover:bg-violet-950/30">
                            <Link href={route('admin.subscription-plans.edit', plan.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                ✨ Edit Plan
                            </Link>
                        </Button>
                        <Button variant="outline" asChild className="border-violet-200 text-violet-600 hover:bg-violet-50 dark:border-violet-700 dark:text-violet-400 dark:hover:bg-violet-950/30">
                            <Link href={route('admin.subscription-plans.index')}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Plans
                            </Link>
                        </Button>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Plan Information */}
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-950 dark:to-purple-950">
                        <CardHeader className="pb-4">
                            <div className="flex items-center gap-2">
                                <div className="rounded-full bg-violet-500/10 p-2">
                                    <IndianRupee className="h-5 w-5 text-violet-600" />
                                </div>
                                <CardTitle className="text-lg text-violet-800 dark:text-violet-200">
                                    💎 Plan Information
                                </CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-violet-700 dark:text-violet-300">💰 Price</label>
                                    <p className="text-2xl font-bold text-violet-900 dark:text-violet-100">
                                        ₹{plan.price.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                    </p>
                                    <p className="text-sm text-violet-600 dark:text-violet-400">per {plan.billing_cycle}</p>
                                </div>
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-violet-700 dark:text-violet-300">📊 Status</label>
                                    <div className="mt-1">
                                        <Badge variant={plan.is_active ? 'default' : 'secondary'} className={plan.is_active ? 'bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700' : 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700'}>
                                            {plan.is_active ? '✨ Active' : '💤 Inactive'}
                                        </Badge>
                                    </div>
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-violet-700 dark:text-violet-300">🔢 Sort Order</label>
                                    <p className="text-violet-800 dark:text-violet-200">{plan.sort_order}</p>
                                </div>
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-violet-700 dark:text-violet-300">📅 Created</label>
                                    <p className="text-violet-800 dark:text-violet-200">{formatDate(plan.created_at)}</p>
                                </div>
                            </div>

                            <div className="space-y-2">
                                <label className="text-sm font-medium text-violet-700 dark:text-violet-300">👥 Subscribers</label>
                                <div className="flex items-center gap-4 mt-1">
                                    <div className="flex items-center gap-1">
                                        <Users className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                                        <span className="font-bold text-emerald-700 dark:text-emerald-300">{plan.active_vendor_subscriptions_count}</span>
                                        <span className="text-sm text-emerald-600 dark:text-emerald-400">active</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                        <Users className="h-4 w-4 text-violet-600 dark:text-violet-400" />
                                        <span className="font-bold text-violet-700 dark:text-violet-300">{plan.vendor_subscriptions_count}</span>
                                        <span className="text-sm text-violet-600 dark:text-violet-400">total</span>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Limits & Features */}
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-950 dark:to-purple-950">
                        <CardHeader className="pb-4">
                            <div className="flex items-center gap-2">
                                <div className="rounded-full bg-violet-500/10 p-2">
                                    <Sparkles className="h-5 w-5 text-violet-600" />
                                </div>
                                <CardTitle className="text-lg text-violet-800 dark:text-violet-200">
                                    ✨ Limits & Features
                                </CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div>
                                    <h4 className="font-semibold mb-3 text-violet-800 dark:text-violet-200 flex items-center gap-2">
                                        <Settings className="h-4 w-4" />
                                        📊 Usage Limits
                                    </h4>
                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                        <div className="text-violet-700 dark:text-violet-300">💄 Services: {plan.max_services === 0 ? '∞ Unlimited' : plan.max_services}</div>
                                        <div className="text-violet-700 dark:text-violet-300">📅 Appointments: {plan.max_appointments_per_month === 0 ? '∞ Unlimited' : `${plan.max_appointments_per_month}/month`}</div>
                                        <div className="text-violet-700 dark:text-violet-300">💺 Seats: {plan.max_seats === 0 ? '∞ Unlimited' : plan.max_seats}</div>
                                        <div className="text-violet-700 dark:text-violet-300">🏢 Branches: {plan.max_branches}</div>
                                        <div className="text-violet-700 dark:text-violet-300">👥 Staff: {plan.max_staff === 0 ? '∞ Unlimited' : plan.max_staff}</div>
                                    </div>
                                </div>

                                <div>
                                    <h4 className="font-semibold mb-3 text-violet-800 dark:text-violet-200 flex items-center gap-2">
                                        <Crown className="h-4 w-4" />
                                        🎯 Premium Features
                                    </h4>
                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                        <div className="flex items-center gap-2">
                                            {plan.has_analytics ? <Check className="h-3 w-3 text-emerald-600 dark:text-emerald-400" /> : <X className="h-3 w-3 text-red-500 dark:text-red-400" />}
                                            <span className="text-violet-700 dark:text-violet-300">📊 Analytics</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {plan.has_api_access ? <Check className="h-3 w-3 text-emerald-600 dark:text-emerald-400" /> : <X className="h-3 w-3 text-red-500 dark:text-red-400" />}
                                            <span className="text-violet-700 dark:text-violet-300">🔌 API Access</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {plan.has_custom_branding ? <Check className="h-3 w-3 text-emerald-600 dark:text-emerald-400" /> : <X className="h-3 w-3 text-red-500 dark:text-red-400" />}
                                            <span className="text-violet-700 dark:text-violet-300">🎨 Custom Branding</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {plan.has_priority_support ? <Check className="h-3 w-3 text-emerald-600 dark:text-emerald-400" /> : <X className="h-3 w-3 text-red-500 dark:text-red-400" />}
                                            <span className="text-violet-700 dark:text-violet-300">⚡ Priority Support</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Subscriptions */}
                <Card className="shadow-lg border-0 bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-950 dark:to-purple-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-violet-500/10 p-2">
                                <Users className="h-5 w-5 text-violet-600" />
                            </div>
                            <CardTitle className="text-lg text-violet-800 dark:text-violet-200">
                                👥 Active Subscriptions ({subscriptions.meta?.total || subscriptions.data?.length || 0})
                            </CardTitle>
                        </div>
                        <CardDescription className="text-violet-600 dark:text-violet-400">
                            Salon vendors currently subscribed to this plan
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {subscriptions.data.length > 0 ? (
                            <div className="rounded-lg border border-violet-200/50 dark:border-violet-700/50 bg-white/70 dark:bg-violet-950/20 shadow-sm">
                                <Table>
                                    <TableHeader>
                                        <TableRow className="border-violet-200/50 dark:border-violet-700/50 hover:bg-violet-50/50 dark:hover:bg-violet-950/30">
                                            <TableHead className="text-violet-800 dark:text-violet-200">👤 Vendor</TableHead>
                                            <TableHead className="text-violet-800 dark:text-violet-200">🏢 Company</TableHead>
                                            <TableHead className="text-violet-800 dark:text-violet-200">📊 Status</TableHead>
                                            <TableHead className="text-violet-800 dark:text-violet-200">📅 Period</TableHead>
                                            <TableHead className="text-violet-800 dark:text-violet-200">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {subscriptions.data.map((subscription) => (
                                            <TableRow key={subscription.id} className="border-violet-200/50 dark:border-violet-700/50 hover:bg-violet-50/50 dark:hover:bg-violet-950/30">
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium text-violet-900 dark:text-violet-100">{subscription.user.name}</div>
                                                        <div className="text-sm text-violet-600 dark:text-violet-400">{subscription.user.email}</div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium text-violet-900 dark:text-violet-100">{subscription.user.company_name}</div>
                                                        <div className="text-xs text-violet-600 dark:text-violet-400 font-mono">
                                                            {subscription.user.company_domain}.salon.test
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    {getStatusBadge(subscription.status)}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="text-sm text-violet-700 dark:text-violet-300">
                                                        <div>{formatDate(subscription.starts_at)} - {formatDate(subscription.ends_at)}</div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <Button variant="outline" size="sm" asChild className="border-violet-200 text-violet-600 hover:bg-violet-50 dark:border-violet-700 dark:text-violet-400 dark:hover:bg-violet-950/30">
                                                        <Link href={route('admin.vendor-subscriptions.show', subscription.id)}>
                                                            <Eye className="h-3 w-3 mr-1" />
                                                            View
                                                        </Link>
                                                    </Button>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                        ) : (
                            <div className="text-center py-12">
                                <div className="rounded-full bg-violet-100 dark:bg-violet-900/30 w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <Users className="h-8 w-8 text-violet-600 dark:text-violet-400" />
                                </div>
                                <p className="text-violet-600 dark:text-violet-400 text-lg font-medium mb-2">👥 No active subscriptions</p>
                                <p className="text-violet-500 dark:text-violet-500">No salon vendors are currently subscribed to this plan</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
