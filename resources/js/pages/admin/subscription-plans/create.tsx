import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save, Crown, Sparkles, IndianRupee, Settings, Plus } from 'lucide-react';

const breadcrumbs = [
    { title: '👑 Admin Dashboard', href: route('admin.dashboard') },
    { title: '💎 Subscription Plans', href: route('admin.subscription-plans.index') },
    { title: '✨ Create Plan', href: route('admin.subscription-plans.create') },
];

export default function CreateSubscriptionPlan() {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        description: '',
        price: '',
        billing_cycle: 'monthly',
        max_services: '',
        max_appointments_per_month: '',
        max_seats: '',
        max_branches: '1',
        max_staff: '',
        has_analytics: false,
        has_api_access: false,
        has_custom_branding: false,
        has_priority_support: false,
        is_active: true,
        sort_order: '0',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.subscription-plans.store'));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✨ Create Professional Subscription Plan" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <Crown className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                Create Professional Subscription Plan
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400">
                                Create a new pricing tier for professional salon vendors with features and limits
                            </p>
                        </div>
                    </div>
                    <Button variant="outline" asChild className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30">
                        <Link href={route('admin.subscription-plans.index')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Plans
                        </Link>
                    </Button>
                </div>

                {/* Form */}
                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Plus className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                ✨ New Plan Configuration
                            </CardTitle>
                        </div>
                        <CardDescription className="text-slate-600 dark:text-slate-400">
                            Configure new subscription plan settings, pricing, and features for professional salon vendors
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid gap-6 md:grid-cols-2">
                                {/* Basic Information */}
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 flex items-center gap-2">
                                        <Crown className="h-5 w-5" />
                                        💎 Basic Information
                                    </h3>

                                    <div className="space-y-2">
                                        <Label htmlFor="name" className="text-slate-700 dark:text-slate-300">💎 Plan Name *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="e.g., Professional Salon Plan"
                                            className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                        />
                                        {errors.name && (
                                            <p className="text-sm text-destructive">{errors.name}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="description" className="text-slate-700 dark:text-slate-300">📝 Description</Label>
                                        <Textarea
                                            id="description"
                                            value={data.description}
                                            onChange={(e) => setData('description', e.target.value)}
                                            placeholder="Brief description of the professional salon plan features"
                                            rows={3}
                                            className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                        />
                                        {errors.description && (
                                            <p className="text-sm text-destructive">{errors.description}</p>
                                        )}
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="price">Price (₹) *</Label>
                                            <Input
                                                id="price"
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                value={data.price}
                                                onChange={(e) => setData('price', e.target.value)}
                                                placeholder="2499.00"
                                            />
                                            {errors.price && (
                                                <p className="text-sm text-destructive">{errors.price}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="billing_cycle">Billing Cycle *</Label>
                                            <Select
                                                value={data.billing_cycle}
                                                onValueChange={(value) => setData('billing_cycle', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="monthly">Monthly</SelectItem>
                                                    <SelectItem value="yearly">Yearly</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.billing_cycle && (
                                                <p className="text-sm text-destructive">{errors.billing_cycle}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="sort_order">Sort Order</Label>
                                            <Input
                                                id="sort_order"
                                                type="number"
                                                min="0"
                                                value={data.sort_order}
                                                onChange={(e) => setData('sort_order', e.target.value)}
                                            />
                                        </div>

                                        <div className="flex items-center space-x-2 pt-6">
                                            <Checkbox
                                                id="is_active"
                                                checked={data.is_active}
                                                onCheckedChange={(checked) => setData('is_active', !!checked)}
                                            />
                                            <Label htmlFor="is_active">Active Plan</Label>
                                        </div>
                                    </div>
                                </div>

                                {/* Limits */}
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 flex items-center gap-2">
                                        <Sparkles className="h-5 w-5" />
                                        📊 Usage Limits
                                    </h3>
                                    <p className="text-sm text-slate-600 dark:text-slate-400">Set 0 for unlimited access</p>

                                    <div className="space-y-2">
                                        <Label htmlFor="max_services">Max Services *</Label>
                                        <Input
                                            id="max_services"
                                            type="number"
                                            min="0"
                                            value={data.max_services}
                                            onChange={(e) => setData('max_services', e.target.value)}
                                            placeholder="0 for unlimited"
                                        />
                                        {errors.max_services && (
                                            <p className="text-sm text-destructive">{errors.max_services}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="max_appointments_per_month">Max Appointments per Month *</Label>
                                        <Input
                                            id="max_appointments_per_month"
                                            type="number"
                                            min="0"
                                            value={data.max_appointments_per_month}
                                            onChange={(e) => setData('max_appointments_per_month', e.target.value)}
                                            placeholder="0 for unlimited"
                                        />
                                        {errors.max_appointments_per_month && (
                                            <p className="text-sm text-destructive">{errors.max_appointments_per_month}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="max_seats">Max Seats *</Label>
                                        <Input
                                            id="max_seats"
                                            type="number"
                                            min="0"
                                            value={data.max_seats}
                                            onChange={(e) => setData('max_seats', e.target.value)}
                                            placeholder="0 for unlimited"
                                        />
                                        {errors.max_seats && (
                                            <p className="text-sm text-destructive">{errors.max_seats}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="max_branches">Max Branches *</Label>
                                        <Input
                                            id="max_branches"
                                            type="number"
                                            min="1"
                                            value={data.max_branches}
                                            onChange={(e) => setData('max_branches', e.target.value)}
                                        />
                                        {errors.max_branches && (
                                            <p className="text-sm text-destructive">{errors.max_branches}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="max_staff">Max Staff *</Label>
                                        <Input
                                            id="max_staff"
                                            type="number"
                                            min="0"
                                            value={data.max_staff}
                                            onChange={(e) => setData('max_staff', e.target.value)}
                                            placeholder="0 for unlimited"
                                        />
                                        {errors.max_staff && (
                                            <p className="text-sm text-destructive">{errors.max_staff}</p>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Features */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 flex items-center gap-2">
                                    <Sparkles className="h-5 w-5" />
                                    ✨ Premium Features
                                </h3>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="has_analytics"
                                            checked={data.has_analytics}
                                            onCheckedChange={(checked) => setData('has_analytics', !!checked)}
                                            className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                                        />
                                        <Label htmlFor="has_analytics" className="text-slate-700 dark:text-slate-300">📊 Analytics</Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="has_api_access"
                                            checked={data.has_api_access}
                                            onCheckedChange={(checked) => setData('has_api_access', !!checked)}
                                            className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                                        />
                                        <Label htmlFor="has_api_access" className="text-slate-700 dark:text-slate-300">🔌 API Access</Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="has_custom_branding"
                                            checked={data.has_custom_branding}
                                            onCheckedChange={(checked) => setData('has_custom_branding', !!checked)}
                                            className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                                        />
                                        <Label htmlFor="has_custom_branding" className="text-slate-700 dark:text-slate-300">🎨 Custom Branding</Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="has_priority_support"
                                            checked={data.has_priority_support}
                                            onCheckedChange={(checked) => setData('has_priority_support', !!checked)}
                                            className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                                        />
                                        <Label htmlFor="has_priority_support" className="text-slate-700 dark:text-slate-300">⚡ Priority Support</Label>
                                    </div>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <div className="flex justify-end space-x-4">
                                <Button variant="outline" asChild className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30">
                                    <Link href={route('admin.subscription-plans.index')}>
                                        Cancel
                                    </Link>
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                                    <Plus className="h-4 w-4 mr-2" />
                                    {processing ? '✨ Creating...' : '✨ Create Plan'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
