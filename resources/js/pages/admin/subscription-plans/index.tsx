import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { Plus, Search, Edit, Eye, Trash2, Users, Crown, Sparkles, IndianRupee, Zap } from 'lucide-react';
import { useState } from 'react';

interface SubscriptionPlan {
    id: number;
    name: string;
    description: string;
    price: number;
    billing_cycle: string;
    max_services: number;
    max_appointments_per_month: number;
    max_seats: number;
    max_branches: number;
    max_staff: number;
    has_analytics: boolean;
    has_api_access: boolean;
    has_custom_branding: boolean;
    has_priority_support: boolean;
    is_active: boolean;
    vendor_subscriptions_count: number;
    active_vendor_subscriptions_count: number;
}

interface Props {
    plans: {
        data: SubscriptionPlan[];
        links: any[];
        meta: any;
    };
    filters: {
        search?: string;
        status?: string;
    };
}

const breadcrumbs = [
    { title: '👑 Admin Dashboard', href: route('admin.dashboard') },
    { title: '💎 Subscription Plans', href: route('admin.subscription-plans.index') },
];

export default function SubscriptionPlansIndex({ plans, filters }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [planToDelete, setPlanToDelete] = useState<SubscriptionPlan | null>(null);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(route('admin.subscription-plans.index'), { search }, {
            preserveState: true,
            replace: true,
        });
    };

    const handleDeleteClick = (plan: SubscriptionPlan) => {
        setPlanToDelete(plan);
        setDeleteDialogOpen(true);
    };

    const handleDeleteConfirm = () => {
        if (planToDelete) {
            router.delete(route('admin.subscription-plans.destroy', planToDelete.id));
            setDeleteDialogOpen(false);
            setPlanToDelete(null);
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="💎 Salon Subscription Plans" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <Crown className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                Professional Subscription Plans
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400">
                                Manage pricing tiers and features for professional salon vendors
                            </p>
                        </div>
                    </div>
                    <Button asChild className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                        <Link href={route('admin.subscription-plans.create')}>
                            <Plus className="h-4 w-4 mr-2" />
                            ✨ Create Plan
                        </Link>
                    </Button>
                </div>

                {/* Search and Filters */}
                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Search className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                🔍 Search Plans
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSearch} className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search subscription plans..."
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                />
                            </div>
                            <Button type="submit" className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </form>
                    </CardContent>
                </Card>

                {/* Plans Table */}
                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Crown className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                💎 Plans ({plans.meta?.total || plans.data?.length || 0})
                            </CardTitle>
                        </div>
                        <CardDescription className="text-slate-600 dark:text-slate-400">
                            All subscription plans for professional salon vendors
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow className="border-slate-200/50 dark:border-slate-700/50 hover:bg-slate-50/50 dark:hover:bg-slate-950/30">
                                    <TableHead className="text-slate-800 dark:text-slate-200">💎 Plan</TableHead>
                                    <TableHead className="text-slate-800 dark:text-slate-200">💰 Price</TableHead>
                                    <TableHead className="text-slate-800 dark:text-slate-200">📊 Limits</TableHead>
                                    <TableHead className="text-slate-800 dark:text-slate-200">✨ Features</TableHead>
                                    <TableHead className="text-slate-800 dark:text-slate-200">👥 Subscribers</TableHead>
                                    <TableHead className="text-slate-800 dark:text-slate-200">📈 Status</TableHead>
                                    <TableHead className="text-slate-800 dark:text-slate-200">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {plans.data.map((plan) => (
                                    <TableRow key={plan.id} className="border-slate-200/50 dark:border-slate-700/50 hover:bg-slate-50/50 dark:hover:bg-slate-950/30">
                                        <TableCell>
                                            <div>
                                                <div className="font-semibold text-slate-900 dark:text-slate-100">{plan.name}</div>
                                                <div className="text-sm text-slate-600 dark:text-slate-400">
                                                    {plan.description}
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div>
                                                <div className="font-bold text-lg text-slate-800 dark:text-slate-200">₹{plan.price.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</div>
                                                <div className="text-sm text-slate-600 dark:text-slate-400">
                                                    per {plan.billing_cycle}
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="text-sm space-y-1 text-violet-700 dark:text-violet-300">
                                                <div>💄 Services: {plan.max_services === 0 ? '∞ Unlimited' : plan.max_services}</div>
                                                <div>📅 Appointments: {plan.max_appointments_per_month === 0 ? '∞ Unlimited' : plan.max_appointments_per_month}/mo</div>
                                                <div>💺 Seats: {plan.max_seats === 0 ? '∞ Unlimited' : plan.max_seats}</div>
                                                <div>🏢 Branches: {plan.max_branches}</div>
                                                <div>👥 Staff: {plan.max_staff === 0 ? '∞ Unlimited' : plan.max_staff}</div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex flex-wrap gap-1">
                                                {plan.has_analytics && <Badge variant="secondary" className="text-xs bg-violet-100 text-violet-700 border-violet-200 dark:bg-violet-900/30 dark:text-violet-300 dark:border-violet-700">📊 Analytics</Badge>}
                                                {plan.has_api_access && <Badge variant="secondary" className="text-xs bg-violet-100 text-violet-700 border-violet-200 dark:bg-violet-900/30 dark:text-violet-300 dark:border-violet-700">🔌 API</Badge>}
                                                {plan.has_custom_branding && <Badge variant="secondary" className="text-xs bg-violet-100 text-violet-700 border-violet-200 dark:bg-violet-900/30 dark:text-violet-300 dark:border-violet-700">🎨 Branding</Badge>}
                                                {plan.has_priority_support && <Badge variant="secondary" className="text-xs bg-violet-100 text-violet-700 border-violet-200 dark:bg-violet-900/30 dark:text-violet-300 dark:border-violet-700">⚡ Priority Support</Badge>}
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <Users className="h-4 w-4 text-violet-600 dark:text-violet-400" />
                                                <span className="font-bold text-violet-800 dark:text-violet-200">{plan.active_vendor_subscriptions_count}</span>
                                                <span className="text-violet-600 dark:text-violet-400">active</span>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant={plan.is_active ? 'default' : 'secondary'} className={plan.is_active ? 'bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700' : 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700'}>
                                                {plan.is_active ? '✨ Active' : '💤 Inactive'}
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex justify-end gap-2">
                                                <Link href={route('admin.subscription-plans.show', plan.id)}>
                                                    <Button variant="outline" size="icon" className="h-8 w-8 border-violet-200 text-violet-600 hover:bg-violet-50 dark:border-violet-700 dark:text-violet-400 dark:hover:bg-violet-950/30">
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                                <Link href={route('admin.subscription-plans.edit', plan.id)}>
                                                    <Button variant="outline" size="icon" className="h-8 w-8 border-violet-200 text-violet-600 hover:bg-violet-50 dark:border-violet-700 dark:text-violet-400 dark:hover:bg-violet-950/30">
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                                {plan.active_vendor_subscriptions_count === 0 && (
                                                    <Button
                                                        variant="outline"
                                                        size="icon"
                                                        className="h-8 w-8 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-950/30"
                                                        onClick={() => handleDeleteClick(plan)}
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                )}
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        {plans.data.length === 0 && (
                            <div className="text-center py-12">
                                <div className="rounded-full bg-violet-100 dark:bg-violet-900/30 w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <Crown className="h-8 w-8 text-violet-600 dark:text-violet-400" />
                                </div>
                                <p className="text-violet-600 dark:text-violet-400 text-lg font-medium mb-2">💎 No subscription plans found</p>
                                <p className="text-violet-500 dark:text-violet-500 mb-4">Create your first salon subscription plan to get started</p>
                                <Button asChild className="bg-gradient-to-r from-violet-500 to-purple-500 hover:from-violet-600 hover:to-purple-600 text-white shadow-lg">
                                    <Link href={route('admin.subscription-plans.create')}>
                                        <Plus className="h-4 w-4 mr-2" />
                                        ✨ Create your first plan
                                    </Link>
                                </Button>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>💎 Delete Subscription Plan</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete the "{planToDelete?.name}" subscription plan? This action cannot be undone and will permanently remove this pricing tier from your salon management system.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDeleteConfirm}
                            className="bg-red-600 text-white hover:bg-red-700"
                        >
                            Delete Plan
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
