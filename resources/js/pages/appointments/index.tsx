import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { Head, Link, router } from '@inertiajs/react';
import { format } from 'date-fns';
import debounce from 'lodash/debounce';
import { ArrowDown, ArrowUp, Calendar as CalendarIcon, Clock, Edit, Plus, Search, Trash, X, Sparkles, Crown, Heart } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

interface Service {
    id: number;
    name: string;
    duration_minutes: number;
    price: number;
}

interface Appointment {
    id: number;
    user: {
        id: number;
        name: string;
        email: string;
        phone: string;
    };
    appointment_date: string;
    appointment_time: string;
    ticket_number: string;
    status: string;
    services: Service[];
    notes: string;
}

interface Props {
    appointments: {
        data: Appointment[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search?: string | null;
        status?: string | null;
        date?: string | null;
        sort?: string | null;
        direction?: 'asc' | 'desc' | null;
    };
}

const breadcrumbs = [{ title: '✂️ Professional Appointments', href: route('appointments.index') }];

export default function Index({ appointments, filters }: Props) {
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
    const [date, setDate] = useState<Date | undefined>(filters.date ? new Date(filters.date) : undefined);

    const handleSearch = useCallback(
        debounce((value: string) => {
            router.get(
                route('appointments.index'),
                { search: value || null, page: 1 },
                {
                    preserveState: true,
                    replace: true,
                },
            );
        }, 300),
        [],
    );

    const handleStatusChange = (value: string) => {
        router.get(
            route('appointments.index'),
            {
                status: value === 'all' ? null : value,
                page: 1,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleDateChange = (selectedDate: Date | undefined) => {
        setDate(selectedDate);
        router.get(
            route('appointments.index'),
            {
                date: selectedDate ? format(selectedDate, 'yyyy-MM-dd') : null,
                page: 1,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const clearDate = () => {
        setDate(undefined);
        router.get(
            route('appointments.index'),
            {
                date: null,
                page: 1,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleSort = (field: string) => {
        const direction = field === filters.sort && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            route('appointments.index'),
            { sort: field, direction, page: 1 },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const confirmDelete = (appointment: Appointment) => {
        setSelectedAppointment(appointment);
        setIsDeleteDialogOpen(true);
    };

    const handleDelete = () => {
        if (!selectedAppointment) return;

        router.delete(route('appointments.destroy', selectedAppointment.id), {
            onSuccess: () => {
                toast.success('Appointment deleted successfully');
                setIsDeleteDialogOpen(false);
            },
            onError: () => {
                toast.error('Failed to delete appointment');
            },
        });
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'pending':
                return (
                    <Badge variant="outline" className="border-amber-200 bg-amber-50 text-amber-700 dark:border-amber-700 dark:bg-amber-900/30 dark:text-amber-300">
                        ⏳ Pending
                    </Badge>
                );
            case 'in_progress':
                return (
                    <Badge variant="outline" className="border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-700 dark:bg-blue-900/30 dark:text-blue-300">
                        ✂️ In Progress
                    </Badge>
                );
            case 'completed':
                return (
                    <Badge variant="outline" className="border-emerald-200 bg-emerald-50 text-emerald-700 dark:border-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300">
                        ✨ Completed
                    </Badge>
                );
            case 'cancelled':
                return (
                    <Badge variant="outline" className="border-red-200 bg-red-50 text-red-700 dark:border-red-700 dark:bg-red-900/30 dark:text-red-300">
                        ❌ Cancelled
                    </Badge>
                );
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const getSortIcon = (field: string) => {
        if (filters.sort !== field) return null;
        return filters.direction === 'asc' ? <ArrowUp className="ml-2 h-4 w-4" /> : <ArrowDown className="ml-2 h-4 w-4" />;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✂️ Salon Appointments" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <Sparkles className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                Salon Appointments
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400">Manage your salon's appointments</p>
                        </div>
                    </div>
                    <Link href={route('appointments.create')}>
                        <Button className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                            <Plus className="mr-2 h-4 w-4" />
                            ✨ Book Appointment
                        </Button>
                    </Link>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <CalendarIcon className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                ✂️ Appointment Management
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                <div className="flex w-full max-w-sm items-center space-x-2">
                                    <Search className="text-muted-foreground h-4 w-4" />
                                    <Input
                                        placeholder="Search professional appointments..."
                                        defaultValue={filters.search || ''}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="h-9 bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {filters.search && (
                                        <Button variant="ghost" size="icon" onClick={() => handleSearch('')} className="h-8 w-8">
                                            <X className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Select value={filters.status || 'all'} onValueChange={handleStatusChange}>
                                        <SelectTrigger className="h-9 w-[180px] bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700">
                                            <SelectValue placeholder="Status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Statuses</SelectItem>
                                            <SelectItem value="pending">⏳ Pending</SelectItem>
                                            <SelectItem value="in_progress">✂️ In Progress</SelectItem>
                                            <SelectItem value="completed">✨ Completed</SelectItem>
                                            <SelectItem value="cancelled">❌ Cancelled</SelectItem>
                                        </SelectContent>
                                    </Select>

                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button
                                                variant="outline"
                                                className={cn('h-9 w-[180px] justify-start text-left font-normal bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700', !date && 'text-muted-foreground')}
                                            >
                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                {date ? format(date, 'PPP') : <span>📅 Pick a date</span>}
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-0">
                                            <Calendar mode="single" selected={date} onSelect={handleDateChange} initialFocus />
                                        </PopoverContent>
                                    </Popover>

                                    {date && (
                                        <Button variant="ghost" size="icon" onClick={clearDate} className="h-8 w-8">
                                            <X className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                            </div>

                            <div className="rounded-lg border border-rose-200/50 dark:border-rose-700/50 bg-white/70 dark:bg-rose-950/20 shadow-sm">
                                <Table>
                                    <TableHeader>
                                        <TableRow className="border-rose-200/50 dark:border-rose-700/50 hover:bg-rose-50/50 dark:hover:bg-rose-950/30">
                                            <TableHead className="w-[50px]">#</TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('ticket_number')} className="flex items-center">
                                                    Ticket
                                                    {getSortIcon('ticket_number')}
                                                </button>
                                            </TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('user.name')} className="flex items-center">
                                                    👤 Customer
                                                    {getSortIcon('user.name')}
                                                </button>
                                            </TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('appointment_date')} className="flex items-center">
                                                    Date
                                                    {getSortIcon('appointment_date')}
                                                </button>
                                            </TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('appointment_time')} className="flex items-center">
                                                    Time
                                                    {getSortIcon('appointment_time')}
                                                </button>
                                            </TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('status')} className="flex items-center">
                                                    Status
                                                    {getSortIcon('status')}
                                                </button>
                                            </TableHead>
                                            <TableHead>💄 Beauty Services</TableHead>
                                            <TableHead className="text-right">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {appointments.data.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={8} className="h-24 text-center text-rose-600 dark:text-rose-400">
                                                    💅 No beauty appointments found.
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            appointments.data.map((appointment, index) => (
                                                <TableRow key={appointment.id} className="border-rose-200/50 dark:border-rose-700/50 hover:bg-rose-50/50 dark:hover:bg-rose-950/30">
                                                    <TableCell className="font-medium">
                                                        {(appointments.current_page - 1) * appointments.per_page + index + 1}
                                                    </TableCell>
                                                    <TableCell>{appointment.ticket_number}</TableCell>
                                                    <TableCell>
                                                        <div>
                                                            <div className="font-medium">{appointment.user.name}</div>
                                                            <div className="text-sm text-gray-500">{appointment.user.phone}</div>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>{new Date(appointment.appointment_date).toLocaleDateString()}</TableCell>
                                                    <TableCell>
                                                        {new Date(`2000-01-01T${appointment.appointment_time}`).toLocaleTimeString([], {
                                                            hour: '2-digit',
                                                            minute: '2-digit',
                                                        })}
                                                    </TableCell>
                                                    <TableCell>{getStatusBadge(appointment.status)}</TableCell>
                                                    <TableCell>
                                                        <div className="flex flex-wrap gap-1">
                                                            {appointment.services &&
                                                                appointment.services.map((service) => (
                                                                    <Badge key={service.id} variant="secondary" className="flex items-center gap-1 bg-violet-100 text-violet-700 border-violet-200 dark:bg-violet-900/30 dark:text-violet-300 dark:border-violet-700">
                                                                        <Sparkles className="h-3 w-3" />
                                                                        {service.name}
                                                                    </Badge>
                                                                ))}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex justify-end gap-2">
                                                            <Link href={route('appointments.edit', appointment.id)}>
                                                                <Button variant="outline" size="icon" className="h-8 w-8 border-rose-200 text-rose-600 hover:bg-rose-50 dark:border-rose-700 dark:text-rose-400 dark:hover:bg-rose-950/30">
                                                                    <Edit className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Button
                                                                variant="outline"
                                                                size="icon"
                                                                className="h-8 w-8 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-950/30"
                                                                onClick={() => confirmDelete(appointment)}
                                                            >
                                                                <Trash className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {appointments.links.length > 3 && (
                                <Pagination>
                                    <PaginationContent>
                                        {appointments.links.map((link, i) => {
                                            if (link.url === null) {
                                                return (
                                                    <PaginationItem key={i} disabled>
                                                        {i === 0 ? (
                                                            <PaginationPrevious />
                                                        ) : i === appointments.links.length - 1 ? (
                                                            <PaginationNext />
                                                        ) : (
                                                            <PaginationLink disabled>
                                                                {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                            </PaginationLink>
                                                        )}
                                                    </PaginationItem>
                                                );
                                            }

                                            return (
                                                <PaginationItem key={i}>
                                                    {i === 0 ? (
                                                        <PaginationPrevious
                                                            href={link.url}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        />
                                                    ) : i === appointments.links.length - 1 ? (
                                                        <PaginationNext
                                                            href={link.url}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        />
                                                    ) : (
                                                        <PaginationLink
                                                            href={link.url}
                                                            isActive={link.active}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        >
                                                            {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                        </PaginationLink>
                                                    )}
                                                </PaginationItem>
                                            );
                                        })}
                                    </PaginationContent>
                                </Pagination>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Delete Confirmation Dialog */}
                <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>💄 Delete Beauty Appointment</AlertDialogTitle>
                            <AlertDialogDescription>
                                Are you sure you want to delete this beauty appointment for "{selectedAppointment?.user.name}"? This action cannot be undone.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDelete} className="bg-red-600 text-white hover:bg-red-700">
                                Delete Appointment
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </div>
        </AppLayout>
    );
}
