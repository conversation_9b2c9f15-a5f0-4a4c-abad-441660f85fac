import { Head, useForm } from '@inertiajs/react';
import { Calendar as CalendarI<PERSON>, <PERSON>, Sparkles, Crown, Heart } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';

interface Service {
    id: number;
    name: string;
    duration_minutes: number;
    price: number;
}

interface Customer {
    id: number;
    name: string;
    email: string;
    phone: string;
}

interface Props {
    services: Service[];
    customers: Customer[];
}

type FormData = {
    user_id: string;
    appointment_date: string;
    appointment_time: string;
    services: { id: number }[];
    notes: string;
}

const breadcrumbs = [
    { title: '✂️ Professional Appointments', href: route('appointments.index') },
    { title: '✨ Book Appointment', href: '#' },
];

// Time slots from 9 AM to 8 PM
const timeSlots = Array.from({ length: 22 }, (_, i) => {
    const hour = Math.floor(i / 2) + 9;
    const minute = (i % 2) * 30;
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour;
    return {
        value: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
        label: `${displayHour}:${minute.toString().padStart(2, '0')} ${period}`
    };
});

export default function Create({ services, customers }: Props) {
    const [date, setDate] = useState<Date | undefined>(undefined);
    const [selectedServices, setSelectedServices] = useState<Service[]>([]);

    const { data, setData, post, processing, errors } = useForm<FormData>({
        user_id: '',
        appointment_date: '',
        appointment_time: '',
        services: [],
        notes: '',
    });

    const handleDateChange = (selectedDate: Date | undefined) => {
        setDate(selectedDate);
        if (selectedDate) {
            setData('appointment_date', format(selectedDate, 'yyyy-MM-dd'));
        } else {
            setData('appointment_date', '');
        }
    };

    const handleServiceSelect = (serviceId: string) => {
        const service = services.find(s => s.id.toString() === serviceId);
        if (service && !selectedServices.some(s => s.id === service.id)) {
            const updatedServices = [...selectedServices, service];
            setSelectedServices(updatedServices);
            setData('services', updatedServices.map(s => ({ id: s.id })));
        }
    };

    const removeService = (serviceId: number) => {
        const updatedServices = selectedServices.filter(s => s.id !== serviceId);
        setSelectedServices(updatedServices);
        setData('services', updatedServices.map(s => ({ id: s.id })));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('appointments.store'), {
            onSuccess: () => {
                toast.success('Appointment created successfully');
            },
            onError: () => {
                toast.error('Failed to create appointment');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✨ Book Salon Appointment" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center gap-3">
                    <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                        <Sparkles className="h-6 w-6 text-white" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                            Book Salon Appointment
                        </h1>
                        <p className="text-slate-600 dark:text-slate-400">Schedule a new salon session for your client</p>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <CalendarIcon className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                ✂️ Appointment Details
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                {/* Customer */}
                                <div className="space-y-2">
                                    <Label htmlFor="user_id" className="text-slate-700 dark:text-slate-300">👤 Customer <span className="text-red-500">*</span></Label>
                                    <Select
                                        value={data.user_id}
                                        onValueChange={(value) => setData('user_id', value)}
                                    >
                                        <SelectTrigger id="user_id" className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400">
                                            <SelectValue placeholder="Select professional client" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {customers && customers.length > 0 ? (
                                                customers.map((customer) => (
                                                    <SelectItem key={customer.id} value={customer.id.toString()}>
                                                        {customer.name} ({customer.phone})
                                                    </SelectItem>
                                                ))
                                            ) : (
                                                <SelectItem value="no-customers" disabled>No customers available</SelectItem>
                                            )}
                                        </SelectContent>
                                    </Select>
                                    {errors.user_id && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.user_id}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Services */}
                                <div className="space-y-2">
                                    <Label htmlFor="service" className="text-slate-700 dark:text-slate-300">✂️ Professional Services <span className="text-red-500">*</span></Label>
                                    <Select
                                        onValueChange={handleServiceSelect}
                                    >
                                        <SelectTrigger id="service" className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400">
                                            <SelectValue placeholder="Select professional services" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {services && services.length > 0 ? (
                                                services.map((service) => (
                                                    <SelectItem key={service.id} value={service.id.toString()}>
                                                        {service.name} ({service.duration_minutes} min)
                                                    </SelectItem>
                                                ))
                                            ) : (
                                                <SelectItem value="no-services" disabled>No professional services available</SelectItem>
                                            )}
                                        </SelectContent>
                                    </Select>
                                    {errors.services && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.services}</AlertDescription>
                                        </Alert>
                                    )}

                                    {selectedServices.length > 0 && (
                                        <div className="mt-2 flex flex-wrap gap-2">
                                            {selectedServices.map(service => (
                                                <Badge key={service.id} variant="secondary" className="flex items-center gap-1 bg-indigo-100 text-indigo-700 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:border-indigo-700">
                                                    <Sparkles className="h-3 w-3" />
                                                    {service.name} ({service.duration_minutes} min)
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        className="h-4 w-4 ml-1 p-0 hover:bg-indigo-200 dark:hover:bg-indigo-800"
                                                        onClick={() => removeService(service.id)}
                                                    >
                                                        <X className="h-3 w-3" />
                                                    </Button>
                                                </Badge>
                                            ))}
                                        </div>
                                    )}
                                </div>

                                {/* Appointment Date */}
                                <div className="space-y-2">
                                    <Label htmlFor="appointment_date" className="text-slate-700 dark:text-slate-300">📅 Date <span className="text-red-500">*</span></Label>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button
                                                variant="outline"
                                                className={cn(
                                                    "w-full justify-start text-left font-normal bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-950/50",
                                                    !date && "text-muted-foreground"
                                                )}
                                            >
                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                {date ? format(date, 'PPP') : <span>Select appointment date</span>}
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-0">
                                            <Calendar
                                                mode="single"
                                                selected={date}
                                                onSelect={handleDateChange}
                                                initialFocus
                                                disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
                                            />
                                        </PopoverContent>
                                    </Popover>
                                    {errors.appointment_date && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.appointment_date}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Appointment Time */}
                                <div className="space-y-2">
                                    <Label htmlFor="appointment_time" className="text-slate-700 dark:text-slate-300">⏰ Time <span className="text-red-500">*</span></Label>
                                    <Select
                                        value={data.appointment_time}
                                        onValueChange={(value) => setData('appointment_time', value)}
                                    >
                                        <SelectTrigger id="appointment_time" className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400">
                                            <SelectValue placeholder="Select appointment time" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {timeSlots.map((slot) => (
                                                <SelectItem key={slot.value} value={slot.value}>
                                                    {slot.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.appointment_time && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.appointment_time}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Notes */}
                                <div className="space-y-2 md:col-span-2">
                                    <Label htmlFor="notes" className="text-slate-700 dark:text-slate-300">📝 Notes</Label>
                                    <Textarea
                                        id="notes"
                                        value={data.notes}
                                        onChange={e => setData('notes', e.target.value)}
                                        rows={3}
                                        placeholder="Add any special notes for this professional appointment..."
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.notes && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.notes}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>
                            </div>

                            {/* Form Actions */}
                            <div className="flex justify-end gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => window.history.back()}
                                    className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30"
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                                    ✨ Book Appointment
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}