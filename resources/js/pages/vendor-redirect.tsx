import { Head } from '@inertiajs/react';
import { useEffect } from 'react';

interface Props {
    redirectUrl: string;
}

export default function VendorRedirect({ redirectUrl }: Props) {
    useEffect(() => {
        // Redirect to the vendor domain after a short delay
        const timer = setTimeout(() => {
            window.location.href = redirectUrl;
        }, 1500);

        return () => clearTimeout(timer);
    }, [redirectUrl]);

    return (
        <>
            <Head title="✨ Redirecting to your salon dashboard..." />

            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-blue-950 dark:to-indigo-950">
                <div className="max-w-lg w-full space-y-8 p-8">
                    <div className="text-center">
                        {/* Stunning Salon Loading Animation */}
                        <div className="mx-auto relative">
                            <div className="h-32 w-32 mx-auto mb-8 relative">
                                {/* Floating Salon Elements */}
                                <div className="absolute inset-0 animate-pulse">
                                    {/* Main Center Circle */}
                                    <div className="absolute inset-6 rounded-full bg-gradient-to-br from-slate-500 via-blue-500 to-indigo-500 shadow-2xl flex items-center justify-center">
                                        <div className="text-white text-2xl">✂️</div>
                                    </div>

                                    {/* Floating Beauty Icons */}
                                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 text-2xl animate-bounce" style={{animationDelay: '0s', animationDuration: '2s'}}>
                                        ✨
                                    </div>
                                    <div className="absolute top-1/2 right-0 transform translate-x-2 -translate-y-1/2 text-2xl animate-bounce" style={{animationDelay: '0.5s', animationDuration: '2s'}}>
                                        💅
                                    </div>
                                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-2 text-2xl animate-bounce" style={{animationDelay: '1s', animationDuration: '2s'}}>
                                        👑
                                    </div>
                                    <div className="absolute top-1/2 left-0 transform -translate-x-2 -translate-y-1/2 text-2xl animate-bounce" style={{animationDelay: '1.5s', animationDuration: '2s'}}>
                                        💎
                                    </div>
                                </div>

                                {/* Orbiting Dots */}
                                <div className="absolute inset-0">
                                    <div className="absolute inset-0 animate-spin" style={{animationDuration: '3s'}}>
                                        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1 w-3 h-3 bg-slate-500 rounded-full shadow-lg"></div>
                                        <div className="absolute top-1/2 right-0 transform translate-x-1 -translate-y-1/2 w-3 h-3 bg-blue-500 rounded-full shadow-lg"></div>
                                        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1 w-3 h-3 bg-indigo-500 rounded-full shadow-lg"></div>
                                        <div className="absolute top-1/2 left-0 transform -translate-x-1 -translate-y-1/2 w-3 h-3 bg-cyan-500 rounded-full shadow-lg"></div>
                                    </div>
                                </div>

                                {/* Counter-rotating Inner Dots */}
                                <div className="absolute inset-2">
                                    <div className="absolute inset-0 animate-spin" style={{animationDuration: '2s', animationDirection: 'reverse'}}>
                                        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1 w-2 h-2 bg-blue-400 rounded-full shadow-md"></div>
                                        <div className="absolute top-1/2 right-0 transform translate-x-1 -translate-y-1/2 w-2 h-2 bg-indigo-400 rounded-full shadow-md"></div>
                                        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1 w-2 h-2 bg-cyan-400 rounded-full shadow-md"></div>
                                        <div className="absolute top-1/2 left-0 transform -translate-x-1 -translate-y-1/2 w-2 h-2 bg-slate-400 rounded-full shadow-md"></div>
                                    </div>
                                </div>

                                {/* Magical Sparkles */}
                                <div className="absolute inset-0 pointer-events-none">
                                    <div className="absolute top-3 right-3 text-yellow-400 animate-ping" style={{animationDelay: '0s'}}>⭐</div>
                                    <div className="absolute bottom-4 left-4 text-amber-400 animate-ping" style={{animationDelay: '0.7s'}}>✨</div>
                                    <div className="absolute top-5 left-3 text-yellow-300 animate-ping" style={{animationDelay: '1.4s'}}>💫</div>
                                    <div className="absolute bottom-3 right-5 text-orange-400 animate-ping" style={{animationDelay: '2.1s'}}>⚡</div>
                                </div>

                                {/* Breathing Glow Effect */}
                                <div className="absolute inset-4 rounded-full bg-gradient-to-br from-slate-300/20 via-blue-300/20 to-indigo-300/20 animate-pulse shadow-2xl" style={{animationDuration: '3s'}}></div>
                            </div>
                        </div>

                        <h2 className="text-4xl font-bold bg-gradient-to-r from-slate-700 via-blue-700 to-indigo-700 bg-clip-text text-transparent">
                            ✨ Login Successful!
                        </h2>
                        <p className="mt-4 text-lg text-slate-700 dark:text-slate-300">
                            Welcome to your professional salon dashboard
                        </p>
                        <p className="mt-2 text-sm text-slate-600 dark:text-slate-400">
                            Redirecting you to your personalized salon management system...
                        </p>

                        {/* Elegant redirect URL display */}
                        <div className="mt-6 p-4 bg-white/70 dark:bg-slate-950/30 rounded-lg border border-slate-200 dark:border-slate-700 shadow-sm">
                            <p className="text-xs text-slate-600 dark:text-slate-400 mb-2">
                                🌐 You will be redirected to:
                            </p>
                            <span className="font-mono text-sm text-slate-800 dark:text-slate-200 bg-slate-100 dark:bg-slate-900/50 px-3 py-1 rounded-md">
                                {redirectUrl}
                            </span>
                        </div>

                        {/* Beautiful salon-themed button */}
                        <div className="mt-8">
                            <a
                                href={redirectUrl}
                                className="inline-flex items-center px-6 py-3 text-sm font-medium rounded-lg text-white bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg transform hover:scale-105 transition-all duration-200"
                            >
                                <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                                ✨ Click here if not redirected automatically
                            </a>
                        </div>

                        {/* Decorative elements */}
                        <div className="mt-8 flex justify-center space-x-2">
                            <div className="h-2 w-2 bg-slate-400 rounded-full animate-pulse"></div>
                            <div className="h-2 w-2 bg-blue-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                            <div className="h-2 w-2 bg-indigo-400 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
