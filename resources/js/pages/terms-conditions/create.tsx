import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { FileText, Scale, Sparkles, Crown } from 'lucide-react';
import { toast } from 'sonner';

type FormData = {
    title: string;
    condition: string;
    is_default: boolean;
    is_active: boolean;
};

const breadcrumbs = [
    { title: '📋 Professional Policies', href: route('terms-conditions.index') },
    { title: '✨ Create Policy', href: '#' },
];

export default function Create() {
    const { data, setData, post, processing, errors } = useForm<FormData>({
        title: '',
        condition: '',
        is_default: false,
        is_active: true,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('terms-conditions.store'), {
            onSuccess: () => {
                toast.success('Term & Condition created successfully');
            },
            onError: () => {
                toast.error('Failed to create Term & Condition');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✨ Create Professional Policy" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center gap-3">
                    <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                        <Scale className="h-6 w-6 text-white" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                            Create Professional Policy
                        </h1>
                        <p className="text-slate-600 dark:text-slate-400">Add new terms and conditions for your professional salon</p>
                    </div>
                </div>

                <form onSubmit={handleSubmit}>
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                        <CardHeader className="pb-4">
                            <div className="flex items-center gap-2">
                                <div className="rounded-full bg-slate-500/10 p-2">
                                    <FileText className="h-5 w-5 text-slate-600" />
                                </div>
                                <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                    📋 Policy Details
                                </CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="title" className="text-slate-700 dark:text-slate-300">
                                        📋 Policy Title <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="title"
                                        value={data.title}
                                        onChange={(e) => setData('title', e.target.value)}
                                        placeholder="Enter professional policy title"
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.title && (
                                        <Alert variant="destructive" className="py-2">
                                            <AlertDescription>{errors.title}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="condition" className="text-slate-700 dark:text-slate-300">
                                        📝 Policy Content <span className="text-red-500">*</span>
                                    </Label>
                                    <Textarea
                                        id="condition"
                                        value={data.condition}
                                        onChange={(e) => setData('condition', e.target.value)}
                                        placeholder="Enter detailed terms and conditions for your professional salon..."
                                        rows={6}
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.condition && (
                                        <Alert variant="destructive" className="py-2">
                                            <AlertDescription>{errors.condition}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                                    <div className="flex items-center space-x-3">
                                        <Switch
                                            id="is_default"
                                            checked={data.is_default}
                                            onCheckedChange={(checked) => setData('is_default', checked)}
                                            className="data-[state=checked]:bg-amber-600"
                                        />
                                        <Label htmlFor="is_default" className="text-slate-700 dark:text-slate-300">⭐ Default Policy</Label>
                                        {errors.is_default && (
                                            <Alert variant="destructive" className="py-2">
                                                <AlertDescription>{errors.is_default}</AlertDescription>
                                            </Alert>
                                        )}
                                    </div>

                                    <div className="flex items-center space-x-3">
                                        <Switch
                                            id="is_active"
                                            checked={data.is_active}
                                            onCheckedChange={(checked) => setData('is_active', checked)}
                                            className="data-[state=checked]:bg-blue-600"
                                        />
                                        <Label htmlFor="is_active" className="text-slate-700 dark:text-slate-300">✨ Active Policy</Label>
                                        {errors.is_active && (
                                            <Alert variant="destructive" className="py-2">
                                                <AlertDescription>{errors.is_active}</AlertDescription>
                                            </Alert>
                                        )}
                                    </div>
                                </div>
                            </div>

                            <div className="flex justify-end gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => window.history.back()}
                                    className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30"
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                                    {processing ? 'Creating...' : '✨ Create Policy'}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </form>
            </div>
        </AppLayout>
    );
}
