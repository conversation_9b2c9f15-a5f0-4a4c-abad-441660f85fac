import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Page } from '@inertiajs/core';
import { Head, Link, router } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { ArrowDown, ArrowUp, FileText, Pencil, Plus, Search, Trash, Sparkles, Crown, Heart, Scale } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

interface TermCondition {
    id: number;
    title: string;
    condition: string;
    is_default: boolean;
    is_active: boolean;
    user: {
        name: string;
    };
}

interface Props {
    termConditions: {
        data: TermCondition[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search?: string | null;
        status?: string | null;
        sort?: string | null;
        direction?: 'asc' | 'desc' | null;
    };
}

interface PageProps {
    flash?: {
        success?: string;
        error?: string;
    };
    [key: string]: unknown;
}

const breadcrumbs = [{ title: '📋 Professional Policies', href: route('terms-conditions.index') }];

export default function Index({ termConditions, filters = {} }: Props) {
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [selectedTermCondition, setSelectedTermCondition] = useState<TermCondition | null>(null);

    const handleSearch = useCallback(
        debounce((value: string) => {
            router.get(
                route('terms-conditions.index'),
                { search: value || null, page: 1 },
                {
                    preserveState: true,
                    replace: true,
                },
            );
        }, 300),
        [],
    );

    const handleStatusFilter = (value: string) => {
        router.get(
            route('terms-conditions.index'),
            { status: value === 'all' ? null : value, page: 1 },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleSort = (field: string) => {
        const direction = field === filters.sort && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            route('terms-conditions.index'),
            { sort: field, direction, page: 1 },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleDeleteClick = (termCondition: TermCondition) => {
        setSelectedTermCondition(termCondition);
        setIsDeleteDialogOpen(true);
    };

    const handleDelete = () => {
        if (!selectedTermCondition) return;

        router.delete(route('terms-conditions.destroy', selectedTermCondition.id), {
            onSuccess: (page: Page<PageProps>) => {
                toast.success(page.props.flash?.success || 'Term & Condition deleted successfully');
                setIsDeleteDialogOpen(false);
                setSelectedTermCondition(null);
            },
            onError: () => {
                toast.error('Failed to delete Term & Condition');
                setIsDeleteDialogOpen(false);
                setSelectedTermCondition(null);
            },
        });
    };

    const getSortIcon = (field: string) => {
        if (field !== filters.sort) return null;
        return filters.direction === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="📋 Salon Policies" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <Scale className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                Professional Policies
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400">Manage your professional salon's terms and conditions</p>
                        </div>
                    </div>
                    <div className="flex gap-3">
                        <Link href={route('terms-conditions.trashed')}>
                            <Button variant="outline" className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30">
                                <Trash className="mr-2 h-4 w-4" />
                                🗑️ Trashed
                            </Button>
                        </Link>
                        <Link href={route('terms-conditions.create')}>
                            <Button className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                                <Plus className="mr-2 h-4 w-4" />
                                ✨ Add Policy
                            </Button>
                        </Link>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-950 dark:to-purple-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-indigo-500/10 p-2">
                                <FileText className="h-5 w-5 text-indigo-600" />
                            </div>
                            <CardTitle className="text-lg text-indigo-800 dark:text-indigo-200">
                                📋 Salon Policy Management
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Search and Filters */}
                            <div className="flex flex-col gap-4 md:flex-row md:items-center">
                                <div className="relative flex-1">
                                    <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                                    <Input
                                        placeholder="Search salon policies..."
                                        defaultValue={filters.search ?? ''}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="pl-10 bg-white/70 dark:bg-indigo-950/30 border-indigo-200 dark:border-indigo-700 focus:border-indigo-400 focus:ring-indigo-400"
                                    />
                                </div>
                                <div className="flex flex-col gap-2 md:flex-row md:items-center">
                                    <div className="flex items-center gap-2">
                                        <Select value={filters.status || 'all'} onValueChange={handleStatusFilter}>
                                            <SelectTrigger className="w-[180px] bg-white/70 dark:bg-indigo-950/30 border-indigo-200 dark:border-indigo-700">
                                                <SelectValue placeholder="Filter by status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Status</SelectItem>
                                                <SelectItem value="active">✨ Active</SelectItem>
                                                <SelectItem value="inactive">💤 Inactive</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            </div>

                            {/* Table */}
                            <div className="rounded-lg border border-indigo-200/50 dark:border-indigo-700/50 bg-white/70 dark:bg-indigo-950/20 shadow-sm">
                                <Table>
                                    <TableHeader>
                                        <TableRow className="border-indigo-200/50 dark:border-indigo-700/50 hover:bg-indigo-50/50 dark:hover:bg-indigo-950/30">
                                            <TableHead className="text-indigo-800 dark:text-indigo-200">
                                                <button onClick={() => handleSort('id')} className="flex items-center">
                                                    🆔 ID
                                                    {getSortIcon('id')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-indigo-800 dark:text-indigo-200">
                                                <button onClick={() => handleSort('title')} className="flex items-center">
                                                    📋 Policy Title
                                                    {getSortIcon('title')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-indigo-800 dark:text-indigo-200">⭐ Default</TableHead>
                                            <TableHead className="text-indigo-800 dark:text-indigo-200">📊 Status</TableHead>
                                            <TableHead className="text-indigo-800 dark:text-indigo-200">👤 Created By</TableHead>
                                            <TableHead className="text-indigo-800 dark:text-indigo-200">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {termConditions.data.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={6} className="text-center text-indigo-600 dark:text-indigo-400">
                                                    📋 No salon policies found
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            termConditions.data.map((termCondition) => (
                                                <TableRow key={termCondition.id} className="border-indigo-200/50 dark:border-indigo-700/50 hover:bg-indigo-50/50 dark:hover:bg-indigo-950/30">
                                                    <TableCell className="font-medium text-indigo-900 dark:text-indigo-100">{termCondition.id}</TableCell>
                                                    <TableCell className="font-medium text-indigo-800 dark:text-indigo-200">{termCondition.title}</TableCell>
                                                    <TableCell>
                                                        <Badge variant={termCondition.is_default ? 'default' : 'secondary'} className={termCondition.is_default ? 'bg-amber-100 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-700' : ''}>
                                                            {termCondition.is_default ? '⭐ Yes' : '❌ No'}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant={termCondition.is_active ? 'success' : 'secondary'} className={termCondition.is_active ? 'bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700' : 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700'}>
                                                            {termCondition.is_active ? '✨ Active' : '💤 Inactive'}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell className="text-indigo-700 dark:text-indigo-300">{termCondition.user.name}</TableCell>
                                                    <TableCell>
                                                        <div className="flex gap-2">
                                                            <Link href={route('terms-conditions.edit', { termCondition: termCondition.id })}>
                                                                <Button size="sm" variant="outline" className="border-indigo-200 text-indigo-600 hover:bg-indigo-50 dark:border-indigo-700 dark:text-indigo-400 dark:hover:bg-indigo-950/30">
                                                                    <Pencil className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Button size="sm" variant="destructive" onClick={() => handleDeleteClick(termCondition)} className="bg-red-600 text-white hover:bg-red-700">
                                                                <Trash className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {/* Pagination */}
                            {termConditions.data.length > 0 && (
                                <Pagination>
                                    <PaginationContent>
                                        {termConditions.current_page > 1 && (
                                            <PaginationItem>
                                                <PaginationPrevious
                                                    href={route('terms-conditions.index', {
                                                        ...filters,
                                                        page: termConditions.current_page - 1,
                                                    })}
                                                />
                                            </PaginationItem>
                                        )}

                                        {termConditions.links.slice(1, -1).map((link, i) => (
                                            <PaginationItem key={i}>
                                                <PaginationLink href={link.url || '#'} isActive={link.active}>
                                                    {link.label}
                                                </PaginationLink>
                                            </PaginationItem>
                                        ))}

                                        {termConditions.current_page < Math.ceil(termConditions.total / termConditions.per_page) && (
                                            <PaginationItem>
                                                <PaginationNext
                                                    href={route('terms-conditions.index', {
                                                        ...filters,
                                                        page: termConditions.current_page + 1,
                                                    })}
                                                />
                                            </PaginationItem>
                                        )}
                                    </PaginationContent>
                                </Pagination>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Delete Confirmation Dialog */}
                <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>📋 Delete Salon Policy</AlertDialogTitle>
                            <AlertDialogDescription>
                                Are you sure you want to delete the salon policy "{selectedTermCondition?.title}"? This action will move it to the trash and can be restored later.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDelete} className="bg-red-600 text-white hover:bg-red-700">
                                Delete Policy
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </div>
        </AppLayout>
    );
}
