import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Page } from '@inertiajs/core';
import { Head, Link, router } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { ArrowDown, ArrowUp, FileText, RotateCcw, Search, X, Scale, Sparkles, Crown } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

interface TermCondition {
    id: number;
    title: string;
    is_default: boolean;
    is_active: boolean;
    deleted_at: string;
    user: {
        name: string;
    };
}

interface Props {
    trashedTermConditions: {
        data: TermCondition[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search?: string | null;
        sort?: string | null;
        direction?: 'asc' | 'desc' | null;
        page?: number | null;
    };
}

// Define the page props interface that includes flash messages
interface PageProps {
    flash?: {
        message?: string;
        success?: string;
        error?: string;
    };
    [key: string]: unknown;
}

const breadcrumbs = [
    {
        title: '📋 Salon Policies',
        href: route('terms-conditions.index'),
    },
    {
        title: '🗑️ Trashed',
        href: route('terms-conditions.trashed'),
    },
];

export default function Trashed({ trashedTermConditions, filters }: Props) {
    const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false);
    const [isForceDeleteDialogOpen, setIsForceDeleteDialogOpen] = useState(false);
    const [selectedTermCondition, setSelectedTermCondition] = useState<TermCondition | null>(null);

    const handleSearch = useCallback(
        debounce((value: string) => {
            router.get(
                route('terms-conditions.trashed'),
                { search: value || null, page: 1 },
                {
                    preserveState: true,
                    replace: true,
                },
            );
        }, 300),
        [],
    );

    const handleSort = (field: string) => {
        const direction = field === filters.sort && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            route('terms-conditions.trashed'),
            { sort: field, direction, page: 1 },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const confirmRestore = (termCondition: TermCondition) => {
        setSelectedTermCondition(termCondition);
        setIsRestoreDialogOpen(true);
    };

    const confirmForceDelete = (termCondition: TermCondition) => {
        setSelectedTermCondition(termCondition);
        setIsForceDeleteDialogOpen(true);
    };

    const handleRestore = () => {
        if (!selectedTermCondition) return;

        router.post(
            route('terms-conditions.restore', selectedTermCondition.id),
            {},
            {
                onSuccess: (page: Page<PageProps>) => {
                    toast.success(page.props.flash?.success || 'Term & Condition restored successfully');
                    setIsRestoreDialogOpen(false);
                    setSelectedTermCondition(null);
                },
                onError: () => {
                    toast.error('Failed to restore Term & Condition');
                    setIsRestoreDialogOpen(false);
                    setSelectedTermCondition(null);
                },
            },
        );
    };

    const handleForceDelete = () => {
        if (!selectedTermCondition) return;

        router.delete(route('terms-conditions.force-delete', selectedTermCondition.id), {
            onSuccess: (page: Page<PageProps>) => {
                toast.success(page.props.flash?.success || 'Term & Condition permanently deleted');
                setIsForceDeleteDialogOpen(false);
                setSelectedTermCondition(null);
            },
            onError: () => {
                toast.error('Failed to permanently delete Term & Condition');
                setIsForceDeleteDialogOpen(false);
                setSelectedTermCondition(null);
            },
        });
    };

    const getSortIcon = (field: string) => {
        if (field !== filters.sort) return null;
        return filters.direction === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="🗑️ Trashed Salon Policies" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <Scale className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                Trashed Professional Policies
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400">Restore or permanently delete professional policies</p>
                        </div>
                    </div>
                    <Link href={route('terms-conditions.index')}>
                        <Button variant="outline" className="border-red-200 text-red-600 hover:bg-red-50 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-950/30">
                            ← Back to Policies
                        </Button>
                    </Link>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <FileText className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                🗑️ Trashed Policy Management
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                <div className="flex w-full max-w-sm items-center space-x-2">
                                    <Search className="text-muted-foreground h-4 w-4" />
                                    <Input
                                        placeholder="Search trashed professional policies..."
                                        defaultValue={filters.search || ''}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="h-9 bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {filters.search && (
                                        <Button variant="ghost" size="icon" onClick={() => handleSearch('')} className="h-8 w-8">
                                            <X className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                            </div>

                            <div className="rounded-lg border border-slate-200/50 dark:border-slate-700/50 bg-white/70 dark:bg-slate-950/20 shadow-sm">
                                <Table>
                                    <TableHeader>
                                        <TableRow className="border-slate-200/50 dark:border-slate-700/50 hover:bg-slate-50/50 dark:hover:bg-slate-950/30">
                                            <TableHead className="text-slate-800 dark:text-slate-200">
                                                <button onClick={() => handleSort('id')} className="flex items-center">
                                                    🆔 ID
                                                    {getSortIcon('id')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-red-800 dark:text-red-200">
                                                <button onClick={() => handleSort('title')} className="flex items-center">
                                                    📋 Policy Title
                                                    {getSortIcon('title')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-red-800 dark:text-red-200">⭐ Default</TableHead>
                                            <TableHead className="text-red-800 dark:text-red-200">📊 Status</TableHead>
                                            <TableHead className="text-red-800 dark:text-red-200">🗑️ Deleted At</TableHead>
                                            <TableHead className="text-red-800 dark:text-red-200">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {trashedTermConditions.data.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={6} className="h-24 text-center text-red-600 dark:text-red-400">
                                                    🗑️ No trashed salon policies found.
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            trashedTermConditions.data.map((termCondition) => (
                                                <TableRow key={termCondition.id} className="border-red-200/50 dark:border-red-700/50 hover:bg-red-50/50 dark:hover:bg-red-950/30">
                                                    <TableCell className="font-medium text-red-900 dark:text-red-100">{termCondition.id}</TableCell>
                                                    <TableCell className="font-medium text-red-800 dark:text-red-200">{termCondition.title}</TableCell>
                                                    <TableCell>
                                                        <Badge variant={termCondition.is_default ? 'default' : 'secondary'} className={termCondition.is_default ? 'bg-amber-100 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-700' : ''}>
                                                            {termCondition.is_default ? '⭐ Yes' : '❌ No'}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant={termCondition.is_active ? 'success' : 'secondary'} className={termCondition.is_active ? 'bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700' : 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700'}>
                                                            {termCondition.is_active ? '✨ Active' : '💤 Inactive'}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell className="text-red-700 dark:text-red-300">{new Date(termCondition.deleted_at).toLocaleString()}</TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex justify-end gap-2">
                                                            <Button
                                                                variant="outline"
                                                                size="icon"
                                                                className="h-8 w-8 border-emerald-200 text-emerald-600 hover:bg-emerald-50 dark:border-emerald-700 dark:text-emerald-400 dark:hover:bg-emerald-950/30"
                                                                onClick={() => confirmRestore(termCondition)}
                                                            >
                                                                <RotateCcw className="h-4 w-4" />
                                                            </Button>
                                                            {/* <Button
                                                                variant="outline"
                                                                size="icon"
                                                                className="h-8 w-8 text-red-500 hover:text-red-600"
                                                                onClick={() => confirmForceDelete(termCondition)}
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button> */}
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {trashedTermConditions.links.length > 3 && (
                                <Pagination>
                                    <PaginationContent>
                                        {trashedTermConditions.links.map((link, i) => {
                                            if (link.url === null) {
                                                return (
                                                    <PaginationItem key={i} disabled>
                                                        {i === 0 ? (
                                                            <PaginationPrevious />
                                                        ) : i === trashedTermConditions.links.length - 1 ? (
                                                            <PaginationNext />
                                                        ) : (
                                                            <PaginationLink disabled>
                                                                {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                            </PaginationLink>
                                                        )}
                                                    </PaginationItem>
                                                );
                                            }

                                            return (
                                                <PaginationItem key={i}>
                                                    {i === 0 ? (
                                                        <PaginationPrevious href={link.url} />
                                                    ) : i === trashedTermConditions.links.length - 1 ? (
                                                        <PaginationNext href={link.url} />
                                                    ) : (
                                                        <PaginationLink href={link.url} isActive={link.active}>
                                                            {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                        </PaginationLink>
                                                    )}
                                                </PaginationItem>
                                            );
                                        })}
                                    </PaginationContent>
                                </Pagination>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Restore Dialog */}
                <AlertDialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>🔄 Restore Salon Policy</AlertDialogTitle>
                            <AlertDialogDescription>
                                Are you sure you want to restore the salon policy "{selectedTermCondition?.title}"? This will move it back to active policies.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleRestore} className="bg-emerald-600 text-white hover:bg-emerald-700">
                                Restore Policy
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>

                {/* Force Delete Dialog */}
                <AlertDialog open={isForceDeleteDialogOpen} onOpenChange={setIsForceDeleteDialogOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>⚠️ Permanently Delete Salon Policy</AlertDialogTitle>
                            <AlertDialogDescription>
                                This will permanently delete the salon policy "{selectedTermCondition?.title}". This action cannot be undone and the policy will be lost forever.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleForceDelete} className="bg-red-600 text-white hover:bg-red-700">
                                Delete Permanently
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </div>
        </AppLayout>
    );
}
