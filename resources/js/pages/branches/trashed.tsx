import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Page } from '@inertiajs/core';
import { Head, Link, router } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { ArrowDown, ArrowUp, Building, RotateCcw, Search, X } from 'lucide-react';
import { use<PERSON><PERSON>back, useState } from 'react';
import { toast } from 'sonner';

interface Branch {
    id: number;
    name: string;
    address: string;
    phone: string;
    email: string | null;
    deleted_at: string;
}

interface Props {
    branches: {
        data: Branch[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search?: string | null;
        sort?: string | null;
        direction?: 'asc' | 'desc' | null;
        page?: number | null;
    };
}

// Define the page props interface that includes flash messages
interface PageProps {
    flash?: {
        message?: string;
        success?: string;
        error?: string;
    };
    [key: string]: unknown;
}

const breadcrumbs = [
    {
        title: '🏢 Salon Locations',
        href: route('branches.index'),
    },
    {
        title: '🗑️ Trashed',
        href: route('branches.trashed'),
    },
];

export default function Trashed({ branches, filters }: Props) {
    const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false);
    const [isForceDeleteDialogOpen, setIsForceDeleteDialogOpen] = useState(false);
    const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null);

    const handleSearch = useCallback(
        debounce((value: string) => {
            router.get(
                route('branches.trashed'),
                { search: value || null, page: 1 },
                {
                    preserveState: true,
                    replace: true,
                },
            );
        }, 300),
        [],
    );

    const handleSort = (field: string) => {
        const direction = field === filters.sort && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            route('branches.trashed'),
            { sort: field, direction, page: 1 },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const confirmRestore = (branch: Branch) => {
        setSelectedBranch(branch);
        setIsRestoreDialogOpen(true);
    };

    const confirmForceDelete = (branch: Branch) => {
        setSelectedBranch(branch);
        setIsForceDeleteDialogOpen(true);
    };

    const handleRestore = () => {
        if (!selectedBranch) return;

        router.put(
            route('branches.restore', selectedBranch.id),
            {},
            {
                onSuccess: (page: Page<PageProps>) => {
                    toast.success(page.props.flash?.success || 'Branch restored successfully');
                    setIsRestoreDialogOpen(false);
                    setSelectedBranch(null);
                },
                onError: () => {
                    toast.error('Failed to restore branch');
                    setIsRestoreDialogOpen(false);
                    setSelectedBranch(null);
                },
            },
        );
    };

    const handleForceDelete = () => {
        if (!selectedBranch) return;

        router.delete(route('branches.force-delete', selectedBranch.id), {
            onSuccess: (page: Page<PageProps>) => {
                toast.success(page.props.flash?.success || 'Branch permanently deleted');
                setIsForceDeleteDialogOpen(false);
                setSelectedBranch(null);
            },
            onError: () => {
                toast.error('Failed to permanently delete branch');
                setIsForceDeleteDialogOpen(false);
                setSelectedBranch(null);
            },
        });
    };

    const getSortIcon = (field: string) => {
        if (field !== filters.sort) return null;
        return filters.direction === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="🗑️ Trashed Salon Locations" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-red-500 to-pink-500 p-3 shadow-lg">
                            <Building className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                                Trashed Salon Locations
                            </h1>
                            <p className="text-red-600 dark:text-red-400">Restore or permanently delete salon locations</p>
                        </div>
                    </div>
                    <Link href={route('branches.index')}>
                        <Button variant="outline" className="border-red-200 text-red-600 hover:bg-red-50 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-950/30">
                            ← Back to Locations
                        </Button>
                    </Link>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950 dark:to-pink-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-red-500/10 p-2">
                                <Building className="h-5 w-5 text-red-600" />
                            </div>
                            <CardTitle className="text-lg text-red-800 dark:text-red-200">
                                🗑️ Trashed Location Management
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                <div className="flex w-full max-w-sm items-center space-x-2">
                                    <Search className="text-muted-foreground h-4 w-4" />
                                    <Input
                                        placeholder="Search trashed salon locations..."
                                        defaultValue={filters.search || ''}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="h-9 bg-white/70 dark:bg-red-950/30 border-red-200 dark:border-red-700 focus:border-red-400 focus:ring-red-400"
                                    />
                                    {filters.search && (
                                        <Button variant="ghost" size="icon" onClick={() => handleSearch('')} className="h-8 w-8">
                                            <X className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                            </div>

                            <div className="rounded-md border">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead className="w-[50px]">#</TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('name')} className="flex items-center">
                                                    Branch Name
                                                    {getSortIcon('name')}
                                                </button>
                                            </TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('phone')} className="flex items-center">
                                                    Phone
                                                    {getSortIcon('phone')}
                                                </button>
                                            </TableHead>
                                            <TableHead>Address</TableHead>
                                            <TableHead>
                                                <button onClick={() => handleSort('deleted_at')} className="flex items-center">
                                                    Deleted At
                                                    {getSortIcon('deleted_at')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-right">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {branches.data.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={6} className="h-24 text-center">
                                                    No trashed branches found.
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            branches.data.map((branch, index) => (
                                                <TableRow key={branch.id}>
                                                    <TableCell className="font-medium">
                                                        {(branches.current_page - 1) * branches.per_page + index + 1}
                                                    </TableCell>
                                                    <TableCell>{branch.name}</TableCell>
                                                    <TableCell>{branch.phone}</TableCell>
                                                    <TableCell className="max-w-[200px] truncate">{branch.address}</TableCell>
                                                    <TableCell>{new Date(branch.deleted_at).toLocaleString()}</TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex justify-end gap-2">
                                                            <Button
                                                                variant="outline"
                                                                size="icon"
                                                                className="h-8 w-8"
                                                                onClick={() => confirmRestore(branch)}
                                                            >
                                                                <RotateCcw className="h-4 w-4" />
                                                            </Button>
                                                            {/* <Button
                                                                variant="outline"
                                                                size="icon"
                                                                className="h-8 w-8 text-red-500 hover:text-red-600"
                                                                onClick={() => confirmForceDelete(branch)}
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button> */}
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {branches.links.length > 3 && (
                                <Pagination>
                                    <PaginationContent>
                                        {branches.links.map((link, i) => {
                                            if (link.url === null) {
                                                return (
                                                    <PaginationItem key={i} disabled>
                                                        {i === 0 ? (
                                                            <PaginationPrevious />
                                                        ) : i === branches.links.length - 1 ? (
                                                            <PaginationNext />
                                                        ) : (
                                                            <PaginationLink disabled>
                                                                {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                            </PaginationLink>
                                                        )}
                                                    </PaginationItem>
                                                );
                                            }

                                            return (
                                                <PaginationItem key={i}>
                                                    {i === 0 ? (
                                                        <PaginationPrevious
                                                            href={link.url}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        />
                                                    ) : i === branches.links.length - 1 ? (
                                                        <PaginationNext
                                                            href={link.url}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        />
                                                    ) : (
                                                        <PaginationLink
                                                            href={link.url}
                                                            isActive={link.active}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        >
                                                            {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                        </PaginationLink>
                                                    )}
                                                </PaginationItem>
                                            );
                                        })}
                                    </PaginationContent>
                                </Pagination>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Restore Dialog */}
                <AlertDialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>🔄 Restore Salon Location</AlertDialogTitle>
                            <AlertDialogDescription>Are you sure you want to restore the salon location "{selectedBranch?.name}"? This will move it back to active locations.</AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleRestore} className="bg-emerald-600 text-white hover:bg-emerald-700">Restore Location</AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>

                {/* Force Delete Dialog */}
                <AlertDialog open={isForceDeleteDialogOpen} onOpenChange={setIsForceDeleteDialogOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>⚠️ Permanently Delete Salon Location</AlertDialogTitle>
                            <AlertDialogDescription>
                                This will permanently delete the salon location "{selectedBranch?.name}". This action cannot be undone and the location will be lost forever.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleForceDelete} className="bg-red-600 text-white hover:bg-red-700">
                                Delete Permanently
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </div>
        </AppLayout>
    );
}
