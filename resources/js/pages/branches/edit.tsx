import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { Building, MapPin, Crown, Sparkles } from 'lucide-react';
import { toast } from 'sonner';

interface Branch {
    id: number;
    name: string;
    address: string;
    phone: string;
    email: string | null;
    is_active: boolean;
}

interface Props {
    branch: Branch;
}

type FormData = {
    name: string;
    address: string;
    phone: string;
    email: string;
    is_active: boolean;
};

const breadcrumbs = [
    { title: '🏢 Salon Locations', href: route('branches.index') },
    { title: '✨ Edit Location', href: '#' },
];

export default function Edit({ branch }: Props) {
    const { data, setData, put, processing, errors } = useForm<FormData>({
        name: branch.name,
        address: branch.address,
        phone: branch.phone,
        email: branch.email || '',
        is_active: branch.is_active,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('branches.update', branch.id), {
            onSuccess: () => {
                toast.success('Branch updated successfully');
            },
            onError: () => {
                toast.error('Failed to update branch');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✨ Edit Salon Location" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center gap-3">
                    <div className="rounded-full bg-gradient-to-br from-emerald-500 to-teal-500 p-3 shadow-lg">
                        <MapPin className="h-6 w-6 text-white" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                            Edit Salon Location
                        </h1>
                        <p className="text-emerald-600 dark:text-emerald-400">Update your salon branch information</p>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-950 dark:to-teal-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-emerald-500/10 p-2">
                                <Building className="h-5 w-5 text-emerald-600" />
                            </div>
                            <CardTitle className="text-lg text-emerald-800 dark:text-emerald-200">
                                🏢 Location Details
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                {/* Basic Information */}
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-emerald-700 dark:text-emerald-300">
                                        🏢 Location Name <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="Enter salon location name"
                                        className="bg-white/70 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-700 focus:border-emerald-400 focus:ring-emerald-400"
                                    />
                                    {errors.name && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.name}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="phone" className="text-emerald-700 dark:text-emerald-300">
                                        📱 Contact Number <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="phone"
                                        value={data.phone}
                                        onChange={(e) => setData('phone', e.target.value)}
                                        placeholder="Enter contact number"
                                        className="bg-white/70 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-700 focus:border-emerald-400 focus:ring-emerald-400"
                                    />
                                    {errors.phone && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.phone}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="email" className="text-emerald-700 dark:text-emerald-300">📧 Email Address</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        value={data.email}
                                        onChange={(e) => setData('email', e.target.value)}
                                        placeholder="Enter email address (optional)"
                                        className="bg-white/70 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-700 focus:border-emerald-400 focus:ring-emerald-400"
                                    />
                                    {errors.email && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.email}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2 md:col-span-2">
                                    <Label htmlFor="address" className="text-emerald-700 dark:text-emerald-300">
                                        📍 Location Address <span className="text-red-500">*</span>
                                    </Label>
                                    <Textarea
                                        id="address"
                                        value={data.address}
                                        onChange={(e) => setData('address', e.target.value)}
                                        rows={3}
                                        placeholder="Enter complete salon address"
                                        className="bg-white/70 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-700 focus:border-emerald-400 focus:ring-emerald-400"
                                    />
                                    {errors.address && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.address}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>
                            </div>

                            {/* Status */}
                            <div className="flex items-center space-x-3">
                                <Switch
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', checked)}
                                    className="data-[state=checked]:bg-emerald-600"
                                />
                                <Label htmlFor="is_active" className="text-emerald-700 dark:text-emerald-300">✨ Active Location</Label>
                            </div>

                            {/* Form Actions */}
                            <div className="flex justify-end gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => window.history.back()}
                                    className="border-emerald-200 text-emerald-600 hover:bg-emerald-50 dark:border-emerald-700 dark:text-emerald-400 dark:hover:bg-emerald-950/30"
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white shadow-lg">
                                    ✨ Update Location
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
