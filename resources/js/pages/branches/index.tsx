import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Page } from '@inertiajs/core';
import { Head, Link, router } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { ArrowDown, ArrowUp, Building, CheckCircle2, Pencil, Plus, Search, Trash, Trash2, X, XCircle, Crown, Sparkles, MapPin } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

interface Branch {
    id: number;
    name: string;
    address: string;
    phone: string;
    email: string | null;
    is_active: boolean;
    created_at: string;
}

interface Props {
    branches: {
        data: Branch[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search?: string | null;
        status?: string | null;
        sort?: string | null;
        direction?: 'asc' | 'desc' | null;
        page?: number | null;
    };
}

// Define the page props interface that includes flash messages
interface PageProps {
    flash?: {
        message?: string;
        success?: string;
        error?: string;
    };
    [key: string]: unknown;
}

const breadcrumbs = [
    {
        title: '🏢 Professional Locations',
        href: route('branches.index'),
    },
];

export default function Index({ branches, filters }: Props) {
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [branchToDelete, setBranchToDelete] = useState<Branch | null>(null);

    const handleSearch = useCallback(
        debounce((value: string) => {
            router.get(
                route('branches.index'),
                { search: value || null, page: 1 },
                {
                    preserveState: true,
                    replace: true,
                },
            );
        }, 300),
        [],
    );

    const handleStatusChange = (value: string) => {
        router.get(
            route('branches.index'),
            {
                status: value === 'all' ? null : value,
                page: 1,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleSort = (field: string) => {
        const direction = field === filters.sort && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            route('branches.index'),
            { sort: field, direction, page: 1 },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const confirmDelete = (branch: Branch) => {
        setBranchToDelete(branch);
        setIsDeleteDialogOpen(true);
    };

    const handleDelete = () => {
        if (!branchToDelete) return;

        router.delete(route('branches.destroy', branchToDelete.id), {
            onSuccess: (page: Page<PageProps>) => {
                toast.success(page.props.flash?.success || 'Branch deleted successfully');
                setIsDeleteDialogOpen(false);
                setBranchToDelete(null);
            },
            onError: () => {
                toast.error('Failed to delete branch');
                setIsDeleteDialogOpen(false);
                setBranchToDelete(null);
            },
        });
    };

    const getSortIcon = (field: string) => {
        if (field !== filters.sort) return null;
        return filters.direction === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="🏢 Salon Locations" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <MapPin className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                Professional Locations
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400">Manage your professional salon branches and locations</p>
                        </div>
                    </div>
                    <div className="flex gap-3">
                        <Link href={route('branches.trashed')}>
                            <Button variant="outline" className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30">
                                <Trash2 className="mr-2 h-4 w-4" />
                                🗑️ Trashed
                            </Button>
                        </Link>
                        <Link href={route('branches.create')}>
                            <Button className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                                <Plus className="mr-2 h-4 w-4" />
                                ✨ Add Location
                            </Button>
                        </Link>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Building className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                🏢 Professional Branch Management
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                <div className="flex w-full max-w-sm items-center space-x-2">
                                    <Search className="text-muted-foreground h-4 w-4" />
                                    <Input
                                        placeholder="Search salon locations..."
                                        defaultValue={filters.search || ''}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="h-9 bg-white/70 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-700 focus:border-emerald-400 focus:ring-emerald-400"
                                    />
                                    {filters.search && (
                                        <Button variant="ghost" size="icon" onClick={() => handleSearch('')} className="h-8 w-8">
                                            <X className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                                <div className="flex items-center gap-2">
                                    <Select value={filters.status || 'all'} onValueChange={handleStatusChange}>
                                        <SelectTrigger className="h-9 w-[180px] bg-white/70 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-700">
                                            <SelectValue placeholder="Filter by status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Statuses</SelectItem>
                                            <SelectItem value="active">✨ Active</SelectItem>
                                            <SelectItem value="inactive">💤 Inactive</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            <div className="rounded-lg border border-emerald-200/50 dark:border-emerald-700/50 bg-white/70 dark:bg-emerald-950/20 shadow-sm">
                                <Table>
                                    <TableHeader>
                                        <TableRow className="border-emerald-200/50 dark:border-emerald-700/50 hover:bg-emerald-50/50 dark:hover:bg-emerald-950/30">
                                            <TableHead className="w-[50px] text-emerald-800 dark:text-emerald-200">#</TableHead>
                                            <TableHead className="text-emerald-800 dark:text-emerald-200">
                                                <button onClick={() => handleSort('name')} className="flex items-center">
                                                    🏢 Location Name
                                                    {getSortIcon('name')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-emerald-800 dark:text-emerald-200">
                                                <button onClick={() => handleSort('phone')} className="flex items-center">
                                                    📱 Phone
                                                    {getSortIcon('phone')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-emerald-800 dark:text-emerald-200">📍 Address</TableHead>
                                            <TableHead className="text-emerald-800 dark:text-emerald-200">
                                                <button onClick={() => handleSort('is_active')} className="flex items-center">
                                                    📊 Status
                                                    {getSortIcon('is_active')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-right text-emerald-800 dark:text-emerald-200">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {branches.data.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={6} className="h-24 text-center text-emerald-600 dark:text-emerald-400">
                                                    🏢 No salon locations found.
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            branches.data.map((branch, index) => (
                                                <TableRow key={branch.id} className="border-emerald-200/50 dark:border-emerald-700/50 hover:bg-emerald-50/50 dark:hover:bg-emerald-950/30">
                                                    <TableCell className="font-medium text-emerald-900 dark:text-emerald-100">
                                                        {(branches.current_page - 1) * branches.per_page + index + 1}
                                                    </TableCell>
                                                    <TableCell className="font-medium text-emerald-800 dark:text-emerald-200">{branch.name}</TableCell>
                                                    <TableCell className="text-emerald-700 dark:text-emerald-300">{branch.phone}</TableCell>
                                                    <TableCell className="max-w-[200px] truncate text-emerald-700 dark:text-emerald-300">{branch.address}</TableCell>
                                                    <TableCell>
                                                        {branch.is_active ? (
                                                            <Badge variant="outline" className="bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700">
                                                                <CheckCircle2 className="mr-1 h-3 w-3" />
                                                                ✨ Active
                                                            </Badge>
                                                        ) : (
                                                            <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700">
                                                                <XCircle className="mr-1 h-3 w-3" />
                                                                💤 Inactive
                                                            </Badge>
                                                        )}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex justify-end gap-2">
                                                            <Link href={route('branches.edit', branch.id)}>
                                                                <Button variant="outline" size="icon" className="h-8 w-8 border-emerald-200 text-emerald-600 hover:bg-emerald-50 dark:border-emerald-700 dark:text-emerald-400 dark:hover:bg-emerald-950/30">
                                                                    <Pencil className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Button
                                                                variant="outline"
                                                                size="icon"
                                                                className="h-8 w-8 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-950/30"
                                                                onClick={() => confirmDelete(branch)}
                                                            >
                                                                <Trash className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {branches.links.length > 3 && (
                                <Pagination>
                                    <PaginationContent>
                                        {branches.links.map((link, i) => {
                                            if (link.url === null) {
                                                return (
                                                    <PaginationItem key={i} disabled>
                                                        {i === 0 ? (
                                                            <PaginationPrevious />
                                                        ) : i === branches.links.length - 1 ? (
                                                            <PaginationNext />
                                                        ) : (
                                                            <PaginationLink disabled>
                                                                {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                            </PaginationLink>
                                                        )}
                                                    </PaginationItem>
                                                );
                                            }

                                            return (
                                                <PaginationItem key={i}>
                                                    {i === 0 ? (
                                                        <PaginationPrevious
                                                            href={link.url}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        />
                                                    ) : i === branches.links.length - 1 ? (
                                                        <PaginationNext
                                                            href={link.url}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        />
                                                    ) : (
                                                        <PaginationLink
                                                            href={link.url}
                                                            isActive={link.active}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        >
                                                            {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                        </PaginationLink>
                                                    )}
                                                </PaginationItem>
                                            );
                                        })}
                                    </PaginationContent>
                                </Pagination>
                            )}
                        </div>
                    </CardContent>
                </Card>

                <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>🏢 Delete Salon Location</AlertDialogTitle>
                            <AlertDialogDescription>
                                Are you sure you want to delete the salon location "{branchToDelete?.name}"? This action cannot be undone and will permanently remove this location from your salon network.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDelete} className="bg-red-600 text-white hover:bg-red-700">
                                Delete Location
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </div>
        </AppLayout>
    );
}
