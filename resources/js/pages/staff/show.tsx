import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Users, Crown, Sparkles, Heart } from 'lucide-react';

interface Staff {
    id: number;
    name: string;
    email: string;
    phone: string;
    gender: string;
    address: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

interface Props {
    staff: Staff;
}

const breadcrumbs = [
    { title: '💎 Salon Team', href: route('staff.index') },
    { title: '👤 Stylist Details', href: '#' },
];

export default function Show({ staff }: Props) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`💎 Stylist: ${staff.name}`} />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-pink-500 to-rose-500 p-3 shadow-lg">
                            <Crown className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent">
                                Stylist Details
                            </h1>
                            <p className="text-pink-600 dark:text-pink-400">View beauty professional information</p>
                        </div>
                    </div>
                    <div className="flex gap-3">
                        <Link href={route('staff.index')}>
                            <Button variant="outline" className="border-pink-200 text-pink-600 hover:bg-pink-50 dark:border-pink-700 dark:text-pink-400 dark:hover:bg-pink-950/30">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Team
                            </Button>
                        </Link>
                        <Link href={route('staff.edit', staff.id)}>
                            <Button className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white shadow-lg">
                                ✨ Edit Stylist
                            </Button>
                        </Link>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-pink-50 to-rose-50 dark:from-pink-950 dark:to-rose-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-pink-500/10 p-2">
                                <Users className="h-5 w-5 text-pink-600" />
                            </div>
                            <CardTitle className="text-lg text-pink-800 dark:text-pink-200">
                                💎 Stylist Information
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-pink-700 dark:text-pink-300">💎 Stylist Name</h3>
                                <p className="text-lg font-semibold text-pink-900 dark:text-pink-100">{staff.name}</p>
                            </div>
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-pink-700 dark:text-pink-300">📧 Email Address</h3>
                                <p className="text-sm text-pink-800 dark:text-pink-200">{staff.email}</p>
                            </div>
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-pink-700 dark:text-pink-300">📱 Phone Number</h3>
                                <p className="text-sm text-pink-800 dark:text-pink-200">{staff.phone}</p>
                            </div>
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-pink-700 dark:text-pink-300">👤 Gender</h3>
                                <p className="text-sm text-pink-800 dark:text-pink-200 capitalize">
                                    {staff.gender === 'male' ? '👨 Male' : staff.gender === 'female' ? '👩 Female' : '🌟 Other'}
                                </p>
                            </div>
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-pink-700 dark:text-pink-300">📊 Status</h3>
                                <Badge variant={staff.is_active ? 'success' : 'secondary'} className={staff.is_active ? 'bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700' : 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700'}>
                                    {staff.is_active ? '✨ Active' : '💤 Inactive'}
                                </Badge>
                            </div>
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-pink-700 dark:text-pink-300">📅 Joined Date</h3>
                                <p className="text-sm text-pink-800 dark:text-pink-200">{new Date(staff.created_at).toLocaleDateString()}</p>
                            </div>
                            <div className="md:col-span-2 space-y-2">
                                <h3 className="text-sm font-medium text-pink-700 dark:text-pink-300">🏠 Address</h3>
                                <p className="text-sm text-pink-800 dark:text-pink-200 bg-white/50 dark:bg-pink-950/30 p-3 rounded-lg border border-pink-200/50 dark:border-pink-700/50">
                                    {staff.address || 'No address provided'}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
