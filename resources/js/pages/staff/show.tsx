import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Users, Crown, Sparkles, Heart } from 'lucide-react';

interface Staff {
    id: number;
    name: string;
    email: string;
    phone: string;
    gender: string;
    address: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

interface Props {
    staff: Staff;
}

const breadcrumbs = [
    { title: '👥 Professional Team', href: route('staff.index') },
    { title: '👤 Professional Details', href: '#' },
];

export default function Show({ staff }: Props) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`👥 Professional: ${staff.name}`} />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <Users className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                Professional Details
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400">View professional staff information</p>
                        </div>
                    </div>
                    <div className="flex gap-3">
                        <Link href={route('staff.index')}>
                            <Button variant="outline" className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Team
                            </Button>
                        </Link>
                        <Link href={route('staff.edit', staff.id)}>
                            <Button className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                                ✨ Edit Professional
                            </Button>
                        </Link>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Users className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                👥 Professional Information
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300">👥 Professional Name</h3>
                                <p className="text-lg font-semibold text-slate-900 dark:text-slate-100">{staff.name}</p>
                            </div>
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300">📧 Email Address</h3>
                                <p className="text-sm text-slate-800 dark:text-slate-200">{staff.email}</p>
                            </div>
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300">📱 Phone Number</h3>
                                <p className="text-sm text-slate-800 dark:text-slate-200">{staff.phone}</p>
                            </div>
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300">👤 Gender</h3>
                                <p className="text-sm text-slate-800 dark:text-slate-200 capitalize">
                                    {staff.gender === 'male' ? '👨 Male' : staff.gender === 'female' ? '👩 Female' : '🌟 Other'}
                                </p>
                            </div>
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300">📊 Status</h3>
                                <Badge variant={staff.is_active ? 'success' : 'secondary'} className={staff.is_active ? 'bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700' : 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700'}>
                                    {staff.is_active ? '✨ Active' : '💤 Inactive'}
                                </Badge>
                            </div>
                            <div className="space-y-2">
                                <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300">📅 Joined Date</h3>
                                <p className="text-sm text-slate-800 dark:text-slate-200">{new Date(staff.created_at).toLocaleDateString()}</p>
                            </div>
                            <div className="md:col-span-2 space-y-2">
                                <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300">🏠 Address</h3>
                                <p className="text-sm text-slate-800 dark:text-slate-200 bg-white/50 dark:bg-slate-950/30 p-3 rounded-lg border border-slate-200/50 dark:border-slate-700/50">
                                    {staff.address || 'No address provided'}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
