import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { Users, Crown, Sparkles, Heart } from 'lucide-react';
import { toast } from 'sonner';

interface Staff {
    id: number;
    name: string;
    email: string;
    phone: string;
    gender: string;
    address: string | null;
    is_active: boolean;
}

interface Props {
    staff: Staff;
}

type FormData = {
    name: string;
    email: string;
    phone: string;
    gender: string;
    address: string;
    password: string;
    password_confirmation: string;
    is_active: boolean;
};

const breadcrumbs = [
    { title: '👥 Professional Team', href: route('staff.index') },
    { title: '✨ Edit Professional', href: '#' },
];

export default function Edit({ staff }: Props) {
    const { data, setData, put, processing, errors } = useForm<FormData>({
        name: staff.name,
        email: staff.email,
        phone: staff.phone,
        gender: staff.gender,
        address: staff.address || '',
        password: '',
        password_confirmation: '',
        is_active: staff.is_active,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('staff.update', staff.id), {
            onSuccess: () => {
                toast.success('Staff member updated successfully');
            },
            onError: () => {
                toast.error('Failed to update staff member');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✨ Edit Professional Staff" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center gap-3">
                    <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                        <Users className="h-6 w-6 text-white" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                            Edit Professional Staff
                        </h1>
                        <p className="text-slate-600 dark:text-slate-400">Update your professional team member's information</p>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Users className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                👥 Professional Details
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                {/* Basic Information */}
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-slate-700 dark:text-slate-300">
                                        👥 Professional Name <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.name && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.name}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="email" className="text-slate-700 dark:text-slate-300">
                                        📧 Email Address <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        value={data.email}
                                        onChange={(e) => setData('email', e.target.value)}
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.email && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.email}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="phone" className="text-slate-700 dark:text-slate-300">
                                        📱 Phone Number <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="phone"
                                        value={data.phone}
                                        onChange={(e) => setData('phone', e.target.value)}
                                        placeholder="Enter phone number"
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.phone && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.phone}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="gender" className="text-slate-700 dark:text-slate-300">
                                        👤 Gender <span className="text-red-500">*</span>
                                    </Label>
                                    <Select value={data.gender} onValueChange={(value) => setData('gender', value)}>
                                        <SelectTrigger id="gender" className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400">
                                            <SelectValue placeholder="Select gender" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="male">👨 Male</SelectItem>
                                            <SelectItem value="female">👩 Female</SelectItem>
                                            <SelectItem value="other">🌟 Other</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.gender && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.gender}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2 md:col-span-2">
                                    <Label htmlFor="address" className="text-slate-700 dark:text-slate-300">🏠 Address</Label>
                                    <Textarea
                                        id="address"
                                        value={data.address}
                                        onChange={(e) => setData('address', e.target.value)}
                                        rows={3}
                                        placeholder="Enter address (optional)"
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.address && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.address}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Password (optional on edit) */}
                                <div className="space-y-2">
                                    <Label htmlFor="password" className="text-slate-700 dark:text-slate-300">
                                        🔒 New Password <span className="text-slate-500 text-xs">(leave blank to keep current)</span>
                                    </Label>
                                    <Input
                                        id="password"
                                        type="password"
                                        value={data.password}
                                        onChange={(e) => setData('password', e.target.value)}
                                        placeholder="Enter new password (optional)"
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.password && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.password}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="password_confirmation" className="text-slate-700 dark:text-slate-300">
                                        🔒 Confirm New Password
                                    </Label>
                                    <Input
                                        id="password_confirmation"
                                        type="password"
                                        value={data.password_confirmation}
                                        onChange={(e) => setData('password_confirmation', e.target.value)}
                                        placeholder="Confirm new password"
                                        className="bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    {errors.password_confirmation && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.password_confirmation}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>
                            </div>

                            {/* Status */}
                            <div className="flex items-center space-x-3">
                                <Switch
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', checked)}
                                    className="data-[state=checked]:bg-blue-600"
                                />
                                <Label htmlFor="is_active" className="text-slate-700 dark:text-slate-300">✨ Active Professional</Label>
                            </div>

                            {/* Form Actions */}
                            <div className="flex justify-end gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => window.history.back()}
                                    className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30"
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                                    ✨ Update Professional
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
