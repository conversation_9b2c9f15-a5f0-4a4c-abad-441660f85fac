import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { Users, Crown, Sparkles, Heart } from 'lucide-react';
import { toast } from 'sonner';

interface Staff {
    id: number;
    name: string;
    email: string;
    phone: string;
    gender: string;
    address: string | null;
    is_active: boolean;
}

interface Props {
    staff: Staff;
}

type FormData = {
    name: string;
    email: string;
    phone: string;
    gender: string;
    address: string;
    password: string;
    password_confirmation: string;
    is_active: boolean;
};

const breadcrumbs = [
    { title: '💎 Salon Team', href: route('staff.index') },
    { title: '✨ Edit Stylist', href: '#' },
];

export default function Edit({ staff }: Props) {
    const { data, setData, put, processing, errors } = useForm<FormData>({
        name: staff.name,
        email: staff.email,
        phone: staff.phone,
        gender: staff.gender,
        address: staff.address || '',
        password: '',
        password_confirmation: '',
        is_active: staff.is_active,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('staff.update', staff.id), {
            onSuccess: () => {
                toast.success('Staff member updated successfully');
            },
            onError: () => {
                toast.error('Failed to update staff member');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✨ Edit Beauty Stylist" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center gap-3">
                    <div className="rounded-full bg-gradient-to-br from-pink-500 to-rose-500 p-3 shadow-lg">
                        <Crown className="h-6 w-6 text-white" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent">
                            Edit Beauty Stylist
                        </h1>
                        <p className="text-pink-600 dark:text-pink-400">Update your salon team member's information</p>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-pink-50 to-rose-50 dark:from-pink-950 dark:to-rose-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-pink-500/10 p-2">
                                <Users className="h-5 w-5 text-pink-600" />
                            </div>
                            <CardTitle className="text-lg text-pink-800 dark:text-pink-200">
                                💎 Stylist Details
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                {/* Basic Information */}
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-pink-700 dark:text-pink-300">
                                        💎 Stylist Name <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        className="bg-white/70 dark:bg-pink-950/30 border-pink-200 dark:border-pink-700 focus:border-pink-400 focus:ring-pink-400"
                                    />
                                    {errors.name && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.name}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="email" className="text-pink-700 dark:text-pink-300">
                                        📧 Email Address <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        value={data.email}
                                        onChange={(e) => setData('email', e.target.value)}
                                        className="bg-white/70 dark:bg-pink-950/30 border-pink-200 dark:border-pink-700 focus:border-pink-400 focus:ring-pink-400"
                                    />
                                    {errors.email && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.email}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="phone" className="text-pink-700 dark:text-pink-300">
                                        📱 Phone Number <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="phone"
                                        value={data.phone}
                                        onChange={(e) => setData('phone', e.target.value)}
                                        placeholder="Enter phone number"
                                        className="bg-white/70 dark:bg-pink-950/30 border-pink-200 dark:border-pink-700 focus:border-pink-400 focus:ring-pink-400"
                                    />
                                    {errors.phone && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.phone}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="gender" className="text-pink-700 dark:text-pink-300">
                                        👤 Gender <span className="text-red-500">*</span>
                                    </Label>
                                    <Select value={data.gender} onValueChange={(value) => setData('gender', value)}>
                                        <SelectTrigger id="gender" className="bg-white/70 dark:bg-pink-950/30 border-pink-200 dark:border-pink-700 focus:border-pink-400 focus:ring-pink-400">
                                            <SelectValue placeholder="Select gender" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="male">👨 Male</SelectItem>
                                            <SelectItem value="female">👩 Female</SelectItem>
                                            <SelectItem value="other">🌟 Other</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.gender && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.gender}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2 md:col-span-2">
                                    <Label htmlFor="address" className="text-pink-700 dark:text-pink-300">🏠 Address</Label>
                                    <Textarea
                                        id="address"
                                        value={data.address}
                                        onChange={(e) => setData('address', e.target.value)}
                                        rows={3}
                                        placeholder="Enter address (optional)"
                                        className="bg-white/70 dark:bg-pink-950/30 border-pink-200 dark:border-pink-700 focus:border-pink-400 focus:ring-pink-400"
                                    />
                                    {errors.address && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.address}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Password (optional on edit) */}
                                <div className="space-y-2">
                                    <Label htmlFor="password" className="text-pink-700 dark:text-pink-300">
                                        🔒 New Password <span className="text-pink-500 text-xs">(leave blank to keep current)</span>
                                    </Label>
                                    <Input
                                        id="password"
                                        type="password"
                                        value={data.password}
                                        onChange={(e) => setData('password', e.target.value)}
                                        placeholder="Enter new password (optional)"
                                        className="bg-white/70 dark:bg-pink-950/30 border-pink-200 dark:border-pink-700 focus:border-pink-400 focus:ring-pink-400"
                                    />
                                    {errors.password && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.password}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="password_confirmation" className="text-pink-700 dark:text-pink-300">
                                        🔒 Confirm New Password
                                    </Label>
                                    <Input
                                        id="password_confirmation"
                                        type="password"
                                        value={data.password_confirmation}
                                        onChange={(e) => setData('password_confirmation', e.target.value)}
                                        placeholder="Confirm new password"
                                        className="bg-white/70 dark:bg-pink-950/30 border-pink-200 dark:border-pink-700 focus:border-pink-400 focus:ring-pink-400"
                                    />
                                    {errors.password_confirmation && (
                                        <Alert variant="destructive">
                                            <AlertDescription>{errors.password_confirmation}</AlertDescription>
                                        </Alert>
                                    )}
                                </div>
                            </div>

                            {/* Status */}
                            <div className="flex items-center space-x-3">
                                <Switch
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', checked)}
                                    className="data-[state=checked]:bg-pink-600"
                                />
                                <Label htmlFor="is_active" className="text-pink-700 dark:text-pink-300">✨ Active Stylist</Label>
                            </div>

                            {/* Form Actions */}
                            <div className="flex justify-end gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => window.history.back()}
                                    className="border-pink-200 text-pink-600 hover:bg-pink-50 dark:border-pink-700 dark:text-pink-400 dark:hover:bg-pink-950/30"
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white shadow-lg">
                                    ✨ Update Stylist
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
