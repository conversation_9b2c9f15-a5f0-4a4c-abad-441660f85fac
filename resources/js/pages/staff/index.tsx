import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { ArrowDown, ArrowUp, Edit, Eye, Plus, Search, Trash, Users, X, Crown, Sparkles, Heart } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

interface Staff {
    id: number;
    name: string;
    email: string;
    phone: string;
    gender: string;
    is_active: boolean;
}

interface Props {
    staff: {
        data: Staff[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search?: string | null;
        status?: string | null;
        sort?: string | null;
        direction?: 'asc' | 'desc' | null;
        page?: number | null;
    };
}

const breadcrumbs = [
    {
        title: '👥 Professional Team',
        href: route('staff.index'),
    },
];

export default function Index({ staff, filters }: Props) {
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [selectedStaff, setSelectedStaff] = useState<Staff | null>(null);

    const handleSearch = useCallback(
        debounce((value: string) => {
            router.get(
                route('staff.index'),
                { search: value || null, page: 1 },
                {
                    preserveState: true,
                    replace: true,
                },
            );
        }, 300),
        [],
    );

    const handleStatusChange = (value: string) => {
        router.get(
            route('staff.index'),
            {
                status: value === 'all' ? null : value,
                page: 1,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleSort = (field: string) => {
        const direction = field === filters.sort && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            route('staff.index'),
            { sort: field, direction, page: 1 },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const confirmDelete = (staff: Staff) => {
        setSelectedStaff(staff);
        setIsDeleteDialogOpen(true);
    };

    const handleDelete = () => {
        if (!selectedStaff) return;

        router.delete(route('staff.destroy', selectedStaff.id), {
            onSuccess: () => {
                toast.success('Staff member deleted successfully');
                setIsDeleteDialogOpen(false);
            },
            onError: () => {
                toast.error('Failed to delete staff member');
            },
        });
    };

    const getSortIcon = (field: string) => {
        if (filters.sort !== field) return null;
        return filters.direction === 'asc' ? <ArrowUp className="ml-2 h-4 w-4" /> : <ArrowDown className="ml-2 h-4 w-4" />;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="💎 Salon Team" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <Users className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                Professional Team
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400">Manage your professional staff and specialists</p>
                        </div>
                    </div>
                    <Link href={route('staff.create')}>
                        <Button className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white shadow-lg">
                            <Plus className="mr-2 h-4 w-4" />
                            ✨ Add Stylist
                        </Button>
                    </Link>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-pink-50 to-rose-50 dark:from-pink-950 dark:to-rose-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-pink-500/10 p-2">
                                <Users className="h-5 w-5 text-pink-600" />
                            </div>
                            <CardTitle className="text-lg text-pink-800 dark:text-pink-200">
                                💎 Beauty Team Management
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                <div className="flex w-full max-w-sm items-center space-x-2">
                                    <Search className="text-muted-foreground h-4 w-4" />
                                    <Input
                                        placeholder="Search beauty team..."
                                        defaultValue={filters.search || ''}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="h-9 bg-white/70 dark:bg-pink-950/30 border-pink-200 dark:border-pink-700 focus:border-pink-400 focus:ring-pink-400"
                                    />
                                    {filters.search && (
                                        <Button variant="ghost" size="icon" onClick={() => handleSearch('')} className="h-8 w-8">
                                            <X className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                                <div className="flex items-center gap-2">
                                    <Select value={filters.status || 'all'} onValueChange={handleStatusChange}>
                                        <SelectTrigger className="h-9 w-[180px] bg-white/70 dark:bg-pink-950/30 border-pink-200 dark:border-pink-700">
                                            <SelectValue placeholder="Filter by status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Status</SelectItem>
                                            <SelectItem value="active">✨ Active</SelectItem>
                                            <SelectItem value="inactive">💤 Inactive</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            <div className="rounded-lg border border-pink-200/50 dark:border-pink-700/50 bg-white/70 dark:bg-pink-950/20 shadow-sm">
                                <Table>
                                    <TableHeader>
                                        <TableRow className="border-pink-200/50 dark:border-pink-700/50 hover:bg-pink-50/50 dark:hover:bg-pink-950/30">
                                            <TableHead className="w-[50px] text-pink-800 dark:text-pink-200">#</TableHead>
                                            <TableHead className="text-pink-800 dark:text-pink-200">
                                                <button onClick={() => handleSort('name')} className="flex items-center">
                                                    💎 Stylist Name
                                                    {getSortIcon('name')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-pink-800 dark:text-pink-200">
                                                <button onClick={() => handleSort('email')} className="flex items-center">
                                                    📧 Email
                                                    {getSortIcon('email')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-pink-800 dark:text-pink-200">📱 Phone</TableHead>
                                            <TableHead className="text-pink-800 dark:text-pink-200">
                                                <button onClick={() => handleSort('is_active')} className="flex items-center">
                                                    📊 Status
                                                    {getSortIcon('is_active')}
                                                </button>
                                            </TableHead>
                                            <TableHead className="text-right text-pink-800 dark:text-pink-200">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {staff.data.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={6} className="h-24 text-center text-pink-600 dark:text-pink-400">
                                                    💎 No beauty team members found.
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            staff.data.map((member, index) => (
                                                <TableRow key={member.id} className="border-pink-200/50 dark:border-pink-700/50 hover:bg-pink-50/50 dark:hover:bg-pink-950/30">
                                                    <TableCell className="font-medium text-pink-900 dark:text-pink-100">
                                                        {(staff.current_page - 1) * staff.per_page + index + 1}
                                                    </TableCell>
                                                    <TableCell className="font-medium text-pink-800 dark:text-pink-200">{member.name}</TableCell>
                                                    <TableCell className="text-pink-700 dark:text-pink-300">{member.email}</TableCell>
                                                    <TableCell className="text-pink-700 dark:text-pink-300">{member.phone}</TableCell>
                                                    <TableCell>
                                                        <Badge variant={member.is_active ? 'success' : 'secondary'} className={member.is_active ? 'bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700' : 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700'}>
                                                            {member.is_active ? '✨ Active' : '💤 Inactive'}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex justify-end gap-2">
                                                            <Link href={route('staff.show', member.id)}>
                                                                <Button variant="outline" size="icon" className="h-8 w-8 border-pink-200 text-pink-600 hover:bg-pink-50 dark:border-pink-700 dark:text-pink-400 dark:hover:bg-pink-950/30">
                                                                    <Eye className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Link href={route('staff.edit', member.id)}>
                                                                <Button variant="outline" size="icon" className="h-8 w-8 border-pink-200 text-pink-600 hover:bg-pink-50 dark:border-pink-700 dark:text-pink-400 dark:hover:bg-pink-950/30">
                                                                    <Edit className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Button
                                                                variant="outline"
                                                                size="icon"
                                                                className="h-8 w-8 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-950/30"
                                                                onClick={() => confirmDelete(member)}
                                                            >
                                                                <Trash className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {staff.links.length > 3 && (
                                <Pagination>
                                    <PaginationContent>
                                        {staff.links.map((link, i) => {
                                            if (link.url === null) {
                                                return (
                                                    <PaginationItem key={i} disabled>
                                                        {i === 0 ? (
                                                            <PaginationPrevious />
                                                        ) : i === staff.links.length - 1 ? (
                                                            <PaginationNext />
                                                        ) : (
                                                            <PaginationLink disabled>
                                                                {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                            </PaginationLink>
                                                        )}
                                                    </PaginationItem>
                                                );
                                            }

                                            return (
                                                <PaginationItem key={i}>
                                                    {i === 0 ? (
                                                        <PaginationPrevious
                                                            href={link.url}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        />
                                                    ) : i === staff.links.length - 1 ? (
                                                        <PaginationNext
                                                            href={link.url}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        />
                                                    ) : (
                                                        <PaginationLink
                                                            href={link.url}
                                                            isActive={link.active}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                router.get(
                                                                    link.url as string,
                                                                    {},
                                                                    {
                                                                        preserveState: true,
                                                                        replace: true,
                                                                    },
                                                                );
                                                            }}
                                                        >
                                                            {link.label.replace('&laquo;', '').replace('&raquo;', '')}
                                                        </PaginationLink>
                                                    )}
                                                </PaginationItem>
                                            );
                                        })}
                                    </PaginationContent>
                                </Pagination>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Delete Confirmation Dialog */}
                <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>💎 Delete Beauty Team Member</AlertDialogTitle>
                            <AlertDialogDescription>
                                Are you sure you want to delete the beauty stylist "{selectedStaff?.name}"? This action cannot be undone and will remove them from your salon team.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDelete} className="bg-red-600 text-white hover:bg-red-700">
                                Delete Stylist
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </div>
        </AppLayout>
    );
}
