import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';
import { Clock, Calendar, Sparkles, Crown } from 'lucide-react';

interface WorkingHour {
    id: number;
    day: string;
    day_order: number;
    open_time: string | null;
    close_time: string | null;
    is_closed: boolean;
}

interface Props {
    workingHours: WorkingHour[];
}

const breadcrumbs = [{ title: '⏰ Professional Hours', href: route('working-hours.index') }];

const dayNames: Record<string, string> = {
    mon: 'Monday',
    tue: 'Tuesday',
    wed: 'Wednesday',
    thu: 'Thursday',
    fri: 'Friday',
    sat: 'Saturday',
    sun: 'Sunday',
};

export default function Index({ workingHours }: Props) {
    const formatTime = (time: string | null) => {
        if (!time) return '-';
        return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="⏰ Salon Hours" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <Calendar className="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                Professional Hours
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400">Manage your professional salon's operating schedule</p>
                        </div>
                    </div>
                    <Link href={route('working-hours.edit')}>
                        <Button className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                            ✨ Edit Hours
                        </Button>
                    </Link>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Clock className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                ⏰ Professional Salon Schedule
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="rounded-lg border border-slate-200/50 dark:border-slate-700/50 bg-white/70 dark:bg-slate-950/20 shadow-sm">
                            <Table>
                                <TableHeader>
                                    <TableRow className="border-slate-200/50 dark:border-slate-700/50 hover:bg-slate-50/50 dark:hover:bg-slate-950/30">
                                        <TableHead className="text-slate-800 dark:text-slate-200">📅 Day</TableHead>
                                        <TableHead className="text-slate-800 dark:text-slate-200">🌅 Opening Time</TableHead>
                                        <TableHead className="text-slate-800 dark:text-slate-200">🌇 Closing Time</TableHead>
                                        <TableHead className="text-slate-800 dark:text-slate-200">📊 Status</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {workingHours.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={4} className="h-24 text-center text-slate-600 dark:text-slate-400">
                                                ⏰ No professional hours found.
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        workingHours.map((workingHour) => (
                                            <TableRow key={workingHour.id} className="border-slate-200/50 dark:border-slate-700/50 hover:bg-slate-50/50 dark:hover:bg-slate-950/30">
                                                <TableCell className="font-medium text-slate-900 dark:text-slate-100">
                                                    {dayNames[workingHour.day] || workingHour.day}
                                                </TableCell>
                                                <TableCell className="text-slate-800 dark:text-slate-200">
                                                    {workingHour.is_closed ? '-' : formatTime(workingHour.open_time)}
                                                </TableCell>
                                                <TableCell className="text-slate-800 dark:text-slate-200">
                                                    {workingHour.is_closed ? '-' : formatTime(workingHour.close_time)}
                                                </TableCell>
                                                <TableCell>
                                                    {workingHour.is_closed ? (
                                                        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700">
                                                            🚫 Closed
                                                        </Badge>
                                                    ) : (
                                                        <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700">
                                                            ✨ Open
                                                        </Badge>
                                                    )}
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
