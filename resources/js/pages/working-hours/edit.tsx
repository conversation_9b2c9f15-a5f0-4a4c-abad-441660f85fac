import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { Clock, Calendar, Sparkles, Crown } from 'lucide-react';
import { toast } from 'sonner';

interface WorkingHour {
    id: number;
    day: string;
    day_order: number;
    open_time: string | null;
    close_time: string | null;
    is_closed: boolean;
}

interface Props {
    workingHours: WorkingHour[];
}

type FormData = {
    hours: {
        id: number;
        open_time: string | null;
        close_time: string | null;
        is_closed: boolean;
    }[];
};

const breadcrumbs = [
    { title: '⏰ Professional Hours', href: route('working-hours.index') },
    { title: '✨ Edit Hours', href: '#' },
];

const dayNames: Record<string, string> = {
    mon: 'Monday',
    tue: 'Tuesday',
    wed: 'Wednesday',
    thu: 'Thursday',
    fri: 'Friday',
    sat: 'Saturday',
    sun: 'Sunday',
};

export default function Edit({ workingHours }: Props) {
    const { data, setData, put, processing, errors } = useForm<FormData>({
        hours: workingHours.map((hour) => ({
            id: hour.id,
            open_time: hour.open_time,
            close_time: hour.close_time,
            is_closed: hour.is_closed,
        })),
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('working-hours.update'), {
            onSuccess: () => {
                toast.success('Working hours updated successfully');
            },
            onError: () => {
                toast.error('Failed to update working hours');
            },
        });
    };

    const updateHourField = (index: number, field: string, value: any) => {
        const updatedHours = [...data.hours];
        updatedHours[index] = {
            ...updatedHours[index],
            [field]: value,
        };
        setData('hours', updatedHours);
    };

    const generateTimeOptions = () => {
        const options = [];
        for (let hour = 0; hour < 24; hour++) {
            for (let minute = 0; minute < 60; minute += 30) {
                const h = hour.toString().padStart(2, '0');
                const m = minute.toString().padStart(2, '0');
                const time = `${h}:${m}:00`;
                const label = new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true,
                });
                options.push({ value: time, label });
            }
        }
        return options;
    };

    const timeOptions = generateTimeOptions();

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="✨ Edit Salon Hours" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div className="flex items-center gap-3">
                    <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                        <Calendar className="h-6 w-6 text-white" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                            Edit Professional Hours
                        </h1>
                        <p className="text-slate-600 dark:text-slate-400">Update your professional salon's operating schedule</p>
                    </div>
                </div>

                <Card className="shadow-lg border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950">
                    <CardHeader className="pb-4">
                        <div className="flex items-center gap-2">
                            <div className="rounded-full bg-slate-500/10 p-2">
                                <Clock className="h-5 w-5 text-slate-600" />
                            </div>
                            <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                ⏰ Professional Salon Schedule
                            </CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="rounded-lg border border-slate-200/50 dark:border-slate-700/50 bg-white/70 dark:bg-slate-950/20 shadow-sm">
                                <Table>
                                    <TableHeader>
                                        <TableRow className="border-slate-200/50 dark:border-slate-700/50 hover:bg-slate-50/50 dark:hover:bg-slate-950/30">
                                            <TableHead className="text-slate-800 dark:text-slate-200">📅 Day</TableHead>
                                            <TableHead className="text-slate-800 dark:text-slate-200">🌅 Opening Time</TableHead>
                                            <TableHead className="text-slate-800 dark:text-slate-200">🌇 Closing Time</TableHead>
                                            <TableHead className="text-slate-800 dark:text-slate-200">🚫 Closed</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {workingHours.map((hour, index) => (
                                            <TableRow key={hour.id} className="border-slate-200/50 dark:border-slate-700/50 hover:bg-slate-50/50 dark:hover:bg-slate-950/30">
                                                <TableCell className="font-medium text-slate-900 dark:text-slate-100">{dayNames[hour.day] || hour.day}</TableCell>
                                                <TableCell>
                                                    <Select
                                                        value={data.hours[index].open_time || ''}
                                                        onValueChange={(value) => updateHourField(index, 'open_time', value)}
                                                        disabled={data.hours[index].is_closed}
                                                    >
                                                        <SelectTrigger className="w-[180px] bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400">
                                                            <SelectValue placeholder="Select opening time" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {timeOptions.map((option) => (
                                                                <SelectItem key={option.value} value={option.value}>
                                                                    {option.label}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    {errors.hours && errors.hours[index]?.open_time && (
                                                        <Alert variant="destructive" className="mt-2">
                                                            <AlertDescription>{errors.hours[index]?.open_time}</AlertDescription>
                                                        </Alert>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <Select
                                                        value={data.hours[index].close_time || ''}
                                                        onValueChange={(value) => updateHourField(index, 'close_time', value)}
                                                        disabled={data.hours[index].is_closed}
                                                    >
                                                        <SelectTrigger className="w-[180px] bg-white/70 dark:bg-slate-950/30 border-slate-200 dark:border-slate-700 focus:border-blue-400 focus:ring-blue-400">
                                                            <SelectValue placeholder="Select closing time" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {timeOptions.map((option) => (
                                                                <SelectItem key={option.value} value={option.value}>
                                                                    {option.label}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    {errors.hours && errors.hours[index]?.close_time && (
                                                        <Alert variant="destructive" className="mt-2">
                                                            <AlertDescription>{errors.hours[index]?.close_time}</AlertDescription>
                                                        </Alert>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center space-x-3">
                                                        <Switch
                                                            id={`closed-${hour.id}`}
                                                            checked={data.hours[index].is_closed}
                                                            onCheckedChange={(checked) => updateHourField(index, 'is_closed', checked)}
                                                            className="data-[state=checked]:bg-red-600"
                                                        />
                                                        <Label htmlFor={`closed-${hour.id}`} className="text-slate-700 dark:text-slate-300">
                                                            {data.hours[index].is_closed ? '🚫 Closed' : '✨ Open'}
                                                        </Label>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>

                            {/* Form Actions */}
                            <div className="flex justify-end gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => window.history.back()}
                                    className="border-slate-200 text-slate-600 hover:bg-slate-50 dark:border-slate-700 dark:text-slate-400 dark:hover:bg-slate-950/30"
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                                    ⏰ Update Hours
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
