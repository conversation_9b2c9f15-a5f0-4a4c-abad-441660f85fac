// Components
import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import AuthLayout from '@/layouts/auth-layout';

export default function VerifyEmail({ status }: { status?: string }) {
    const { post, processing } = useForm({});

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('verification.send'));
    };

    return (
        <AuthLayout title="📧 Verify Your Email" description="Please verify your salon email address by clicking on the link we just emailed to you.">
            <Head title="📧 Email Verification - Salon Management" />

            {status === 'verification-link-sent' && (
                <div className="mb-4 text-center text-sm font-medium text-emerald-600 bg-emerald-50 dark:bg-emerald-950/30 p-4 rounded-lg border border-emerald-200 dark:border-emerald-700">
                    ✨ A new verification link has been sent to your salon email address. Please check your inbox and click the link to verify your account.
                </div>
            )}

            <form onSubmit={submit} className="space-y-6 text-center">
                <Button disabled={processing} className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 text-white shadow-lg">
                    {processing && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                    ✨ Resend Verification Email
                </Button>

                <TextLink href={route('logout')} method="post" className="mx-auto block text-sm text-slate-600 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300 font-medium">
                    ✂️ Log out
                </TextLink>
            </form>
        </AuthLayout>
    );
}
