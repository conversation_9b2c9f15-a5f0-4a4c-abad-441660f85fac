import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler, useEffect } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';
import AppLogoIcon from '@/components/app-logo-icon';

type LoginForm = {
    email: string;
    password: string;
    remember: boolean;
};

interface LoginProps {
    status?: string;
    canResetPassword: boolean;
    isTenant?: boolean;
    tenantId?: string;
}

export default function Login({ status, canResetPassword, isTenant, tenantId }: LoginProps) {
    const { data, setData, post, processing, errors, reset } = useForm<Required<LoginForm>>({
        email: '',
        password: '',
        remember: false,
    });

    // Refresh CSRF token for tenant domains
    useEffect(() => {
        if (isTenant) {
            // Get a fresh CSRF token for the tenant domain
            fetch('/csrf-token')
                .then(response => response.json())
                .then(data => {
                    // Update the CSRF token in the meta tag
                    const csrfMeta = document.querySelector('meta[name="csrf-token"]');
                    if (csrfMeta) {
                        csrfMeta.setAttribute('content', data.csrf_token);
                    }
                })
                .catch(error => {
                    console.warn('Could not refresh CSRF token:', error);
                });
        }
    }, [isTenant]);

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('login'), {
            onFinish: () => reset('password'),
            onError: (errors) => {
                // If CSRF error on tenant domain, try to refresh token and retry
                if (errors.message && errors.message.includes('419') && isTenant) {
                    fetch('/csrf-token')
                        .then(response => response.json())
                        .then(data => {
                            const csrfMeta = document.querySelector('meta[name="csrf-token"]');
                            if (csrfMeta) {
                                csrfMeta.setAttribute('content', data.csrf_token);
                            }
                            // Retry the request
                            setTimeout(() => {
                                post(route('login'), {
                                    onFinish: () => reset('password'),
                                });
                            }, 100);
                        });
                }
            }
        });
    };

    return (
        <div className="flex justify-center items-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-950 dark:via-blue-950 dark:to-indigo-950 p-4 sm:p-6 md:p-8 min-h-screen">
            <Head title="✨ Professional Salon Login" />

            <div className="flex md:flex-row flex-col shadow-2xl rounded-2xl w-full max-w-6xl overflow-hidden border border-slate-200/50 dark:border-slate-800/50">
                {/* Left side - Brand */}
                <div className="relative flex flex-col justify-between bg-gradient-to-br from-slate-700 via-blue-700 to-indigo-800 p-8 md:p-12 w-full md:w-5/12 text-white overflow-hidden">
                    <div className="top-0 left-0 z-0 absolute bg-gradient-to-br from-white/10 to-transparent w-full h-full"></div>

                    <div className="relative z-10">
                        <div className="flex justify-center mb-8">
                            <AppLogoIcon className="mr-3 max-w-60 text-white fill-current drop-shadow-lg" />
                        </div>

                        <p className="mb-6 max-w-md text-lg text-white/90 leading-relaxed">
                            ✨ Transform your salon business with our professional management platform. Where efficiency meets excellence.
                        </p>

                        <div className="md:block hidden">
                            <div className="flex items-center mt-12 mb-4">
                                <div className="bg-white/30 rounded-full w-12 h-1"></div>
                                <div className="ml-4 font-medium text-sm text-white/80">🏆 TRUSTED BY PROFESSIONAL SALONS</div>
                            </div>

                            <div className="gap-4 grid grid-cols-3 opacity-80">
                                <div className="flex justify-center items-center bg-white/20 backdrop-blur-sm rounded-lg h-12 border border-white/10">
                                    <span className="font-semibold text-white text-xs">✂️ Style Studio</span>
                                </div>
                                <div className="flex justify-center items-center bg-white/20 backdrop-blur-sm rounded-lg h-12 border border-white/10">
                                    <span className="font-semibold text-white text-xs">🏢 Pro Salon</span>
                                </div>
                                <div className="flex justify-center items-center bg-white/20 backdrop-blur-sm rounded-lg h-12 border border-white/10">
                                    <span className="font-semibold text-white text-xs">⭐ Elite Cuts</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="md:block relative z-10 hidden mt-8 text-sm text-white/60">
                        © {new Date().getFullYear()} Astrid Web Technology LLP. All rights reserved.
                    </div>
                </div>

                {/* Right side - Login Form */}
                <div className="flex items-center bg-gradient-to-br from-white to-slate-50/30 dark:from-gray-900 dark:to-slate-950/30 p-8 md:p-12 w-full md:w-7/12">
                    <div className="mx-auto w-full max-w-md">
                        <div className="mb-8">
                            <h2 className="mb-2 font-bold text-3xl bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                {isTenant ? '✂️ Salon Login' : '✨ Welcome Back'}
                            </h2>
                            <p className="text-slate-600 dark:text-slate-400">
                                {isTenant
                                    ? `Sign in to your professional salon management system${tenantId ? ` (${tenantId})` : ''}`
                                    : 'Sign in to your account to continue managing your salon'
                                }
                            </p>
                        </div>

                        <form className="space-y-6" onSubmit={submit}>
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="email" className="font-medium text-sm">Email address</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        required
                                        autoFocus
                                        tabIndex={1}
                                        autoComplete="email"
                                        value={data.email}
                                        onChange={(e) => setData('email', e.target.value)}
                                        placeholder="<EMAIL>"
                                        className="bg-slate-50/50 dark:bg-slate-950/30 px-4 border-slate-200 dark:border-slate-700 h-12 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    <InputError message={errors.email} />
                                </div>

                                <div className="space-y-2">
                                    <div className="flex justify-between items-center">
                                        <Label htmlFor="password" className="font-medium text-sm">Password</Label>
                                        {canResetPassword && (
                                            <TextLink href={route('password.request')} className="font-medium text-sm" tabIndex={5}>
                                                Forgot password?
                                            </TextLink>
                                        )}
                                    </div>
                                    <Input
                                        id="password"
                                        type="password"
                                        required
                                        tabIndex={2}
                                        autoComplete="current-password"
                                        value={data.password}
                                        onChange={(e) => setData('password', e.target.value)}
                                        placeholder="Enter your password"
                                        className="bg-slate-50/50 dark:bg-slate-950/30 px-4 border-slate-200 dark:border-slate-700 h-12 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    <InputError message={errors.password} />
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="remember"
                                        name="remember"
                                        checked={data.remember}
                                        onClick={() => setData('remember', !data.remember)}
                                        tabIndex={3}
                                    />
                                    <Label htmlFor="remember" className="text-sm">Remember me</Label>
                                </div>
                            </div>

                            <Button
                                type="submit"
                                className="bg-gradient-to-r from-slate-700 to-blue-700 hover:from-slate-800 hover:to-blue-800 w-full h-12 font-medium text-base cursor-pointer text-white shadow-lg"
                                tabIndex={4}
                                disabled={processing}
                            >
                                {processing && <LoaderCircle className="mr-2 w-4 h-4 animate-spin" />}
                                ✨ Sign in to Salon Dashboard
                            </Button>

                            {!isTenant && (
                                <div className="text-center text-muted-foreground text-sm">
                                    Don't have an account?{' '}
                                    <TextLink href={route('register')} tabIndex={5} className="font-medium">
                                        Create an account
                                    </TextLink>
                                </div>
                            )}
                        </form>

                        {status && <div className="mt-6 font-medium text-center text-green-600 text-sm">{status}</div>}
                    </div>
                </div>
            </div>
        </div>
    );
}



