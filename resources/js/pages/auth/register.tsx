import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle, User, Store, Check, Sparkles, Crown } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLogoIcon from '@/components/app-logo-icon';
import { cn } from '@/lib/utils';

type RegisterForm = {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
    role: string;
    company_name?: string;
    company_domain?: string;
};

// Role selection component
const RoleSelector = ({ selectedRole, onRoleChange }: { selectedRole: string; onRoleChange: (role: string) => void }) => {
    const roles = [
        {
            id: 'customer',
            title: '✨ Beauty Customer',
            description: 'Book appointments and manage your beauty journey',
            icon: Sparkles,
        },
        {
            id: 'vendor',
            title: '👑 Salon Owner',
            description: 'Manage your beauty salon, staff, and appointments',
            icon: Crown,
        },
    ];

    return (
        <div className="space-y-3">
            <Label className="font-medium text-sm">Register as</Label>
            <div className="gap-3 grid grid-cols-1 sm:grid-cols-2">
                {roles.map((role) => {
                    const Icon = role.icon;
                    const isSelected = selectedRole === role.id;

                    return (
                        <div
                            key={role.id}
                            onClick={() => onRoleChange(role.id)}
                            className={cn(
                                "relative flex flex-col items-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:border-blue-400/50 hover:shadow-lg",
                                isSelected
                                    ? "border-blue-500 bg-blue-50 dark:bg-blue-950/30 shadow-lg"
                                    : "border-slate-200 dark:border-slate-700 hover:bg-slate-50/50 dark:hover:bg-slate-950/20"
                            )}
                        >
                            {isSelected && (
                                <div className="top-2 right-2 absolute flex justify-center items-center bg-blue-500 rounded-full w-5 h-5 shadow-md">
                                    <Check className="w-3 h-3 text-white" />
                                </div>
                            )}
                            <Icon className={cn(
                                "w-8 h-8 mb-2",
                                isSelected ? "text-blue-600" : "text-slate-400"
                            )} />
                            <h3 className={cn(
                                "font-medium text-sm text-center",
                                isSelected ? "text-blue-700 dark:text-blue-300" : "text-slate-600 dark:text-slate-400"
                            )}>
                                {role.title}
                            </h3>
                            <p className="mt-1 text-center text-slate-500 dark:text-slate-400 text-xs">
                                {role.description}
                            </p>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default function Register() {
    const { data, setData, post, processing, errors, reset } = useForm<RegisterForm>({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: 'customer',
        company_name: '',
        company_domain: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('register'), {
            onFinish: () => reset('password', 'password_confirmation'),
        });
    };

    return (
        <div className="flex justify-center items-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-950 dark:via-blue-950 dark:to-indigo-950 p-4 sm:p-6 md:p-8 min-h-screen">
            <Head title="✨ Join Professional Platform" />

            <div className="flex md:flex-row flex-col shadow-2xl rounded-2xl w-full max-w-6xl overflow-hidden border border-slate-200/50 dark:border-slate-800/50">
                {/* Left side - Brand */}
                <div className="relative flex flex-col justify-between bg-gradient-to-br from-slate-600 via-blue-600 to-indigo-600 p-8 md:p-12 w-full md:w-5/12 text-white overflow-hidden">
                    <div className="top-0 left-0 z-0 absolute bg-gradient-to-br from-white/10 to-transparent w-full h-full"></div>

                    <div className="relative z-10">
                        <div className="flex justify-center mb-8">
                            <AppLogoIcon className="mr-3 max-w-60 text-white fill-current drop-shadow-lg" />
                        </div>

                        <p className="mb-6 max-w-md text-lg text-white/90 leading-relaxed">
                            💄 Join thousands of professional salons transforming their business with our elegant salon management platform.
                        </p>

                        <div className="md:block hidden">
                            <div className="flex items-center mt-12 mb-4">
                                <div className="bg-white/30 rounded-full w-12 h-1"></div>
                                <div className="ml-4 font-medium text-sm text-white/80">👑 TRUSTED BY BEAUTY SALONS</div>
                            </div>

                            <div className="gap-4 grid grid-cols-3 opacity-80">
                                <div className="flex justify-center items-center bg-white/20 backdrop-blur-sm rounded-lg h-12 border border-white/10">
                                    <span className="font-semibold text-white text-xs">💅 Beauty Hub</span>
                                </div>
                                <div className="flex justify-center items-center bg-white/20 backdrop-blur-sm rounded-lg h-12 border border-white/10">
                                    <span className="font-semibold text-white text-xs">✨ Style Studio</span>
                                </div>
                                <div className="flex justify-center items-center bg-white/20 backdrop-blur-sm rounded-lg h-12 border border-white/10">
                                    <span className="font-semibold text-white text-xs">💄 Glow Salon</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="md:block relative z-10 hidden mt-8 text-sm text-white/60">
                        © {new Date().getFullYear()} Astrid Web Technology LLP. All rights reserved.
                    </div>
                </div>

                {/* Right side - Register Form */}
                <div className="flex items-center bg-gradient-to-br from-white to-slate-50/30 dark:from-gray-900 dark:to-slate-950/30 p-8 md:p-12 w-full md:w-7/12">
                    <div className="mx-auto w-full max-w-md">
                        <div className="mb-8">
                            <h2 className="mb-2 font-bold text-3xl bg-gradient-to-r from-slate-700 to-blue-700 bg-clip-text text-transparent">
                                ✨ Create Your Professional Account
                            </h2>
                            <p className="text-slate-600 dark:text-slate-400">
                                Join our platform and start your professional salon business journey or book stunning appointments
                            </p>
                        </div>

                        <form className="space-y-6" onSubmit={submit}>
                            <div className="space-y-4">
                                {/* Role Selection */}
                                <RoleSelector
                                    selectedRole={data.role}
                                    onRoleChange={(role) => setData('role', role)}
                                />
                                <InputError message={errors.role} />

                                {/* Name Field */}
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="font-medium text-sm">Full Name</Label>
                                    <Input
                                        id="name"
                                        type="text"
                                        required
                                        autoFocus
                                        tabIndex={1}
                                        autoComplete="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        disabled={processing}
                                        placeholder="Enter your full name"
                                        className="bg-slate-50/50 dark:bg-slate-950/30 px-4 border-slate-200 dark:border-slate-700 h-12 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    <InputError message={errors.name} />
                                </div>

                                {/* Vendor-specific fields */}
                                {data.role === 'vendor' && (
                                    <div className="gap-4 grid grid-cols-1 sm:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="company_name" className="font-medium text-sm">💄 Professional Salon Name</Label>
                                            <Input
                                                id="company_name"
                                                type="text"
                                                required={data.role === 'vendor'}
                                                tabIndex={2}
                                                value={data.company_name}
                                                onChange={(e) => setData('company_name', e.target.value)}
                                                disabled={processing}
                                                placeholder="Enter your professional salon name"
                                                className="bg-slate-50/50 dark:bg-slate-950/30 px-4 border-slate-200 dark:border-slate-700 h-12 focus:border-blue-400 focus:ring-blue-400"
                                            />
                                            <InputError message={errors.company_name} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="company_domain" className="font-medium text-sm">🌐 Salon Domain</Label>
                                            <div className="relative">
                                                <Input
                                                    id="company_domain"
                                                    type="text"
                                                    required={data.role === 'vendor'}
                                                    tabIndex={3}
                                                    value={data.company_domain}
                                                    onChange={(e) => setData('company_domain', e.target.value)}
                                                    disabled={processing}
                                                    placeholder="mysalon"
                                                    className="bg-slate-50/50 dark:bg-slate-950/30 px-4 pr-24 border-slate-200 dark:border-slate-700 h-12 focus:border-blue-400 focus:ring-blue-400"
                                                />
                                                <div className="top-0 right-0 absolute flex items-center bg-slate-100 dark:bg-slate-900 px-2 border-l border-slate-200 dark:border-slate-700 rounded-r-md h-12 text-slate-600 dark:text-slate-400 text-xs">
                                                    .salon.test
                                                </div>
                                            </div>
                                            <InputError message={errors.company_domain} />
                                        </div>

                                        <div className="col-span-1 sm:col-span-2">
                                            <p className="text-slate-600 dark:text-slate-400 text-xs">
                                                ✨ Your professional salon will be accessible at: <span className="font-medium">{data.company_domain || 'yourdomain'}.salon.test</span>
                                            </p>
                                        </div>
                                    </div>
                                )}

                                {/* Email Field */}
                                <div className="space-y-2">
                                    <Label htmlFor="email" className="font-medium text-sm">Email Address</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        required
                                        tabIndex={data.role === 'vendor' ? 4 : 2}
                                        autoComplete="email"
                                        value={data.email}
                                        onChange={(e) => setData('email', e.target.value)}
                                        disabled={processing}
                                        placeholder="<EMAIL>"
                                        className="bg-slate-50/50 dark:bg-slate-950/30 px-4 border-slate-200 dark:border-slate-700 h-12 focus:border-blue-400 focus:ring-blue-400"
                                    />
                                    <InputError message={errors.email} />
                                </div>

                                {/* Password Fields */}
                                <div className="gap-4 grid grid-cols-1 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="password" className="font-medium text-sm">Password</Label>
                                        <Input
                                            id="password"
                                            type="password"
                                            required
                                            tabIndex={data.role === 'vendor' ? 5 : 3}
                                            autoComplete="new-password"
                                            value={data.password}
                                            onChange={(e) => setData('password', e.target.value)}
                                            disabled={processing}
                                            placeholder="Create a strong password"
                                            className="bg-slate-50/50 dark:bg-slate-950/30 px-4 border-slate-200 dark:border-slate-700 h-12 focus:border-blue-400 focus:ring-blue-400"
                                        />
                                        <InputError message={errors.password} />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="password_confirmation" className="font-medium text-sm">Confirm Password</Label>
                                        <Input
                                            id="password_confirmation"
                                            type="password"
                                            required
                                            tabIndex={data.role === 'vendor' ? 6 : 4}
                                            autoComplete="new-password"
                                            value={data.password_confirmation}
                                            onChange={(e) => setData('password_confirmation', e.target.value)}
                                            disabled={processing}
                                            placeholder="Confirm your password"
                                            className="bg-slate-50/50 dark:bg-slate-950/30 px-4 border-slate-200 dark:border-slate-700 h-12 focus:border-blue-400 focus:ring-blue-400"
                                        />
                                        <InputError message={errors.password_confirmation} />
                                    </div>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <Button
                                type="submit"
                                className="bg-gradient-to-r from-slate-600 via-blue-600 to-indigo-600 hover:from-slate-700 hover:via-blue-700 hover:to-indigo-700 w-full h-12 font-medium text-base cursor-pointer text-white shadow-lg"
                                tabIndex={data.role === 'vendor' ? 7 : 5}
                                disabled={processing}
                            >
                                {processing && <LoaderCircle className="mr-2 w-4 h-4 animate-spin" />}
                                ✨ Create Professional Account
                            </Button>

                            {/* Login Link */}
                            <div className="text-center text-slate-600 dark:text-slate-400 text-sm">
                                Already have an account?{' '}
                                <TextLink href={route('login')} tabIndex={data.role === 'vendor' ? 8 : 6} className="font-medium text-slate-700 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-200">
                                    Sign in to your professional dashboard
                                </TextLink>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    );
}
