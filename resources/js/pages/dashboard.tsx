import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import {
    Armchair,
    Calendar,
    CheckCircle2,
    Clock,
    FolderTree,
    Hash,
    Package,
    Ruler,
    XCircle,
    Sparkles,
    Heart,
    Star,
    Users,
    Activity
} from 'lucide-react';
import { cn } from "@/lib/utils";

interface DashboardProps {
    stats: {
        [key: string]: StatsData;
    };
    appointmentStats: {
        today_total: number;
        today_pending: number;
        today_in_progress: number;
        today_completed: number;
        today_cancelled: number;
    };
    seatStats: {
        total: number;
        available: number;
        occupied: number;
        maintenance: number;
    };
    upcomingAppointments: {
        id: number;
        customer_name: string;
        date: string;
        time: string;
        ticket_number: number;
        services_count: number;
        status: string;
    }[];
    recentCompletedServices: {
        id: number;
        customer_name: string;
        service_name: string;
        seat: string;
        completed_at: string;
        duration: string;
    }[];
    recentProducts: {
        id: number;
        name: string;
        category?: string;
        unit?: string;
        hsn?: string;
        created_at: string;
        is_active: boolean;
    }[];
    categoryDistribution: {
        name: string;
        count: number;
    }[];
}

interface StatsData {
    total: number;
    active: number;
    inactive: number;
}

const statsConfig = {
    products: {
        title: 'Products',
        icon: Package,
        route: 'products.index',
    },
    categories: {
        title: 'Categories',
        icon: FolderTree,
        route: 'products.categories.index',
    },
    units: {
        title: 'Units',
        icon: Ruler,
        route: 'products.units.index',
        activeLabel: 'In Use',
        inactiveLabel: 'Unused',
    },
    hsns: {
        title: 'HSN Codes',
        icon: Hash,
        route: 'products.hsns.index',
        activeLabel: 'In Use',
        inactiveLabel: 'Unused',
    },
} as const;

// Helper function to get the correct dashboard route
const getDashboardRoute = () => {
    try {
        // Try tenant dashboard first (for tenant domains)
        return route('tenant.dashboard');
    } catch {
        try {
            // Try admin dashboard (for admin users on central domain)
            return route('admin.dashboard');
        } catch {
            // Fallback to root
            return '/';
        }
    }
};

const breadcrumbs = [
    {
        title: 'Dashboard',
        href: getDashboardRoute(),
    },
];

const StatCard = ({
    title,
    stats,
    icon: Icon,
    href,
    activeLabel = 'Active',
    inactiveLabel = 'Inactive',
}: {
    title: string;
    stats: StatsData;
    icon: React.ElementType;
    href: string;
    activeLabel?: string;
    inactiveLabel?: string;
}) => (
    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-slate-50 to-blue-100 dark:from-slate-950 dark:to-blue-900 shadow-lg">
        <CardHeader className="flex flex-row justify-between items-center space-y-0 pb-2">
            <CardTitle className="font-medium text-sm text-slate-700 dark:text-slate-300">{title}</CardTitle>
            <div className="rounded-full bg-slate-500/10 p-2">
                <Icon className="w-4 h-4 text-slate-600" />
            </div>
        </CardHeader>
        <CardContent>
            <div className="font-bold text-3xl text-slate-900 dark:text-slate-100">{stats.total}</div>
            <div className="flex justify-between items-center pt-4">
                <div className="flex items-center text-sm">
                    <CheckCircle2 className="mr-1 w-4 h-4 text-blue-500" />
                    <span className="text-slate-700 dark:text-slate-300">
                        {activeLabel}: {stats.active}
                    </span>
                </div>
                <div className="flex items-center text-sm">
                    <XCircle className="mr-1 w-4 h-4 text-slate-500" />
                    <span className="text-slate-600 dark:text-slate-400">
                        {inactiveLabel}: {stats.inactive}
                    </span>
                </div>
            </div>
            <CardDescription className="pt-2">
                <a href={href} className="text-slate-600 dark:text-slate-400 text-xs hover:text-slate-700 dark:hover:text-slate-300 hover:underline">
                    View all →
                </a>
            </CardDescription>
        </CardContent>
    </Card>
);

const AppointmentStatsCard = ({ stats }: { stats: DashboardProps['appointmentStats'] }) => (
    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-slate-50 to-blue-100 dark:from-slate-950 dark:to-blue-900 shadow-lg">
        <CardHeader className="pb-4">
            <div className="flex items-center gap-2">
                <div className="rounded-full bg-slate-500/10 p-2">
                    <Calendar className="w-5 h-5 text-slate-600" />
                </div>
                <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                    💅 Today's Professional Appointments
                </CardTitle>
            </div>
        </CardHeader>
        <CardContent>
            <div className="font-bold text-4xl text-slate-900 dark:text-slate-100 mb-1">{stats.today_total}</div>
            <p className="text-sm text-slate-600 dark:text-slate-400 mb-6">Total appointments scheduled</p>

            <div className="space-y-4">
                <div className="space-y-2">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                            <div className="bg-slate-400 rounded-full size-3 shadow-sm"></div>
                            <span className="font-medium text-sm text-slate-800 dark:text-slate-200">Pending</span>
                        </div>
                        <span className="font-bold text-sm text-slate-900 dark:text-slate-100">{stats.today_pending}</span>
                    </div>
                    <Progress value={(stats.today_pending / stats.today_total) * 100} indicatorClassName="bg-slate-400" className="bg-slate-100 dark:bg-slate-900/30" />
                </div>

                <div className="space-y-2">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                            <div className="bg-blue-500 rounded-full size-3 shadow-sm"></div>
                            <span className="font-medium text-sm text-slate-800 dark:text-slate-200">In Progress</span>
                        </div>
                        <span className="font-bold text-sm text-slate-900 dark:text-slate-100">{stats.today_in_progress}</span>
                    </div>
                    <Progress value={(stats.today_in_progress / stats.today_total) * 100} indicatorClassName="bg-blue-500" className="bg-blue-100 dark:bg-blue-900/30" />
                </div>

                <div className="space-y-2">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                            <div className="bg-blue-600 rounded-full size-3 shadow-sm"></div>
                            <span className="font-medium text-sm text-slate-800 dark:text-slate-200">Completed</span>
                        </div>
                        <span className="font-bold text-sm text-slate-900 dark:text-slate-100">{stats.today_completed}</span>
                    </div>
                    <Progress value={(stats.today_completed / stats.today_total) * 100} indicatorClassName="bg-blue-600" className="bg-blue-100 dark:bg-blue-900/30" />
                </div>

                <div className="space-y-2">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                            <div className="bg-slate-500 rounded-full size-3 shadow-sm"></div>
                            <span className="font-medium text-sm text-slate-800 dark:text-slate-200">Cancelled</span>
                        </div>
                        <span className="font-bold text-sm text-slate-900 dark:text-slate-100">{stats.today_cancelled}</span>
                    </div>
                    <Progress value={(stats.today_cancelled / stats.today_total) * 100} indicatorClassName="bg-slate-500" className="bg-slate-100 dark:bg-slate-900/30" />
                </div>
            </div>
            <CardDescription className="mt-6">
                <a href="/appointments" className="text-slate-600 dark:text-slate-400 text-xs hover:text-slate-700 dark:hover:text-slate-300 hover:underline">
                    View all appointments →
                </a>
            </CardDescription>
        </CardContent>
    </Card>
);

const SeatStatsCard = ({ stats }: { stats: DashboardProps['seatStats'] }) => (
    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-slate-50 to-blue-100 dark:from-slate-950 dark:to-blue-900 shadow-lg">
        <CardHeader className="pb-4">
            <div className="flex items-center gap-2">
                <div className="rounded-full bg-slate-500/10 p-2">
                    <Armchair className="w-5 h-5 text-slate-600" />
                </div>
                <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                    ✨ Professional Station Availability
                </CardTitle>
            </div>
        </CardHeader>
        <CardContent>
            <div className="flex justify-between items-center mb-6">
                <div>
                    <div className="font-bold text-4xl text-slate-900 dark:text-slate-100">{stats.total}</div>
                    <p className="text-sm text-slate-600 dark:text-slate-400">Total professional stations</p>
                </div>
                <div className="flex flex-col gap-2">
                    <Badge variant="outline" className="bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-300 dark:border-emerald-700 shadow-sm">
                        {stats.available} Available
                    </Badge>
                    <Badge variant="outline" className="bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 border-amber-300 dark:border-amber-700 shadow-sm">
                        {stats.occupied} In Use
                    </Badge>
                    <Badge variant="outline" className="bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-300 dark:border-slate-600 shadow-sm">
                        {stats.maintenance} Maintenance
                    </Badge>
                </div>
            </div>

            {/* Visual Professional Station Representation */}
            <div className="relative bg-gradient-to-b from-white/70 dark:from-slate-900/20 to-slate-50/50 dark:to-slate-800/20 p-4 border border-slate-200/50 dark:border-slate-700/50 rounded-lg shadow-inner">
                <div className="gap-3 grid grid-cols-5">
                    {Array.from({ length: stats.total }).map((_, index) => {
                        // Determine seat status based on counts
                        let status = 'available';
                        let currentIndex = index + 1;

                        if (currentIndex <= stats.occupied) {
                            status = 'occupied';
                        } else if (currentIndex <= stats.occupied + stats.maintenance) {
                            status = 'maintenance';
                        }

                        return (
                            <div key={index} className="flex flex-col items-center">
                                <div className={cn(
                                    "relative p-2 rounded-lg shadow-md transition-all duration-200 hover:scale-105 hover:shadow-lg",
                                    status === 'available' && "bg-emerald-50 dark:bg-emerald-900/30 border border-emerald-200 dark:border-emerald-700",
                                    status === 'occupied' && "bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700",
                                    status === 'maintenance' && "bg-slate-100 dark:bg-slate-800 border border-slate-300 dark:border-slate-600"
                                )}>
                                    <Armchair className={cn(
                                        "w-10 h-10",
                                        status === 'available' && "text-emerald-600 dark:text-emerald-400",
                                        status === 'occupied' && "text-blue-600 dark:text-blue-400",
                                        status === 'maintenance' && "text-slate-500 dark:text-slate-400"
                                    )} />
                                    {status === 'occupied' && (
                                        <div className="top-0 right-0 absolute bg-blue-500 border-2 border-white dark:border-slate-900 rounded-full w-3 h-3">
                                            <span className="absolute inset-0 bg-blue-400 opacity-75 rounded-full animate-ping"></span>
                                        </div>
                                    )}
                                    {status === 'maintenance' && (
                                        <div className="top-0 right-0 absolute bg-slate-500 border-2 border-white dark:border-slate-900 rounded-full w-3 h-3" />
                                    )}
                                    <div className="-bottom-1 left-1/2 absolute bg-slate-300 dark:bg-slate-600 rounded-full w-6 h-1 transform -translate-x-1/2"></div>
                                </div>
                                <span className="mt-1 font-medium text-xs">#{currentIndex}</span>
                            </div>
                        );
                    })}
                </div>

                {/* Floor design elements */}
                <div className="bottom-0 absolute inset-x-0 bg-slate-200 dark:bg-slate-700 h-1"></div>
                <div className="left-0 absolute inset-y-0 bg-slate-200 dark:bg-slate-700 w-1"></div>
                <div className="right-0 absolute inset-y-0 bg-slate-200 dark:bg-slate-700 w-1"></div>
            </div>

            <CardDescription className="flex justify-between items-center mt-4">
                <a href="/seats" className="text-slate-600 dark:text-slate-400 text-xs hover:text-slate-700 dark:hover:text-slate-300 hover:underline">
                    Manage stations →
                </a>
                <span className="text-muted-foreground text-xs">Last updated: {new Date().toLocaleTimeString()}</span>
            </CardDescription>
        </CardContent>
    </Card>
);

export default function Dashboard({
    stats,
    appointmentStats,
    seatStats,
    upcomingAppointments,
    recentCompletedServices,
    recentProducts,
    categoryDistribution,
}: DashboardProps) {
    const hasAnyStats = Object.keys(stats).length > 0 || upcomingAppointments.length > 0;

    if (!hasAnyStats) {
        return (
            <AppLayout>
                <Head title="Dashboard" />
                <div className="flex flex-col flex-1 justify-center items-center p-4 h-full">
                    <Calendar className="w-12 h-12 text-muted-foreground" />
                    <h2 className="mt-4 font-medium text-lg">No Data Available</h2>
                    <p className="text-muted-foreground text-sm">Start by adding appointments to your salon.</p>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex flex-col flex-1 gap-6 p-6 h-full">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="rounded-full bg-gradient-to-br from-slate-600 to-blue-600 p-3 shadow-lg">
                            <Sparkles className="w-6 h-6 text-white" />
                        </div>
                        <div>
                            <h1 className="font-bold text-3xl bg-gradient-to-r from-slate-700 via-blue-700 to-indigo-700 bg-clip-text text-transparent">
                                Professional Salon Dashboard
                            </h1>
                            <p className="text-muted-foreground">✨ Manage your professional salon with excellence</p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Activity className="w-4 h-4 text-blue-500" />
                        <span>Welcome back!</span>
                    </div>
                </div>

                {/* Professional Stats Grid */}
                {Object.keys(stats).length > 0 && (
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        {Object.entries(statsConfig).map(([key, config]) => {
                            const statData = stats[key as keyof typeof stats];
                            if (!statData) return null;

                            return (
                                <StatCard
                                    key={key}
                                    title={config.title}
                                    stats={statData}
                                    icon={config.icon}
                                    href={route(config.route)}
                                    activeLabel={config.activeLabel}
                                    inactiveLabel={config.inactiveLabel}
                                />
                            );
                        })}
                    </div>
                )}

                {/* Appointment and Seat Stats */}
                <div className="gap-4 grid md:grid-cols-2">
                    <AppointmentStatsCard stats={appointmentStats} />
                    <SeatStatsCard stats={seatStats} />
                </div>

                {/* Upcoming Professional Appointments */}
                {upcomingAppointments.length > 0 && (
                    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950 shadow-lg">
                        <CardHeader className="pb-4 border-b border-slate-200 dark:border-slate-800">
                            <div className="flex items-center gap-2">
                                <div className="rounded-full bg-slate-500/10 p-2">
                                    <Clock className="w-5 h-5 text-slate-600" />
                                </div>
                                <CardTitle className="text-lg text-slate-800 dark:text-slate-200">
                                    🌟 Upcoming Professional Appointments
                                </CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="divide-y divide-slate-200 dark:divide-slate-800">
                                {upcomingAppointments.map((appointment) => (
                                    <div key={appointment.id} className="hover:bg-slate-50/50 dark:hover:bg-slate-900/30 p-4 transition-colors">
                                        <div className="flex justify-between items-start">
                                            <div className="flex gap-3">
                                                <div className="flex justify-center items-center bg-gradient-to-br from-slate-600 to-blue-600 rounded-full w-12 h-12 shrink-0 shadow-md">
                                                    <span className="font-semibold text-white text-lg">
                                                        {appointment.customer_name.charAt(0)}
                                                    </span>
                                                </div>
                                                <div>
                                                    <p className="font-semibold text-slate-900 dark:text-slate-100">{appointment.customer_name}</p>
                                                    <div className="flex flex-wrap gap-2 mt-2">
                                                        <span className="inline-flex items-center bg-slate-100 dark:bg-slate-900/50 text-slate-800 dark:text-slate-200 px-2 py-1 rounded-full text-xs font-medium">
                                                            #{appointment.ticket_number}
                                                        </span>
                                                        <span className="inline-flex items-center gap-1 text-slate-700 dark:text-slate-300 text-xs">
                                                            <Calendar className="w-3 h-3" />
                                                            {appointment.date}
                                                        </span>
                                                        <span className="inline-flex items-center gap-1 text-slate-700 dark:text-slate-300 text-xs">
                                                            <Clock className="w-3 h-3" />
                                                            {appointment.time}
                                                        </span>
                                                        <span className="inline-flex items-center gap-1 text-slate-700 dark:text-slate-300 text-xs">
                                                            <Star className="w-3 h-3" />
                                                            {appointment.services_count} professional services
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <Badge
                                                variant={
                                                    appointment.status === 'pending'
                                                        ? 'outline'
                                                        : appointment.status === 'in_progress'
                                                            ? 'secondary'
                                                            : appointment.status === 'completed'
                                                                ? 'default'
                                                                : 'destructive'
                                                }
                                                className={cn(
                                                    "ml-auto shadow-sm",
                                                    appointment.status === 'pending' && "border-slate-300 text-slate-700 bg-slate-50 dark:bg-slate-900/30",
                                                    appointment.status === 'in_progress' && "bg-blue-100 text-blue-800 border-blue-300",
                                                    appointment.status === 'completed' && "bg-emerald-100 text-emerald-800 border-emerald-300"
                                                )}
                                            >
                                                {appointment.status.replace('_', ' ')}
                                            </Badge>
                                        </div>
                                    </div>
                                ))}
                            </div>
                            {upcomingAppointments.length === 0 && (
                                <div className="flex flex-col justify-center items-center p-8 text-center">
                                    <Clock className="mb-2 w-10 h-10 text-cyan-500" />
                                    <p className="text-cyan-600 dark:text-cyan-400">No upcoming professional appointments</p>
                                </div>
                            )}
                            <div className="flex justify-between items-center bg-slate-100/50 dark:bg-slate-900/30 p-4 border-t border-slate-200 dark:border-slate-800">
                                <a href="/appointments" className="inline-flex items-center font-medium text-slate-700 dark:text-slate-300 text-sm hover:text-slate-800 dark:hover:text-slate-200 hover:underline">
                                    View all professional appointments
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1">
                                        <path d="M5 12h14"></path>
                                        <path d="m12 5 7 7-7 7"></path>
                                    </svg>
                                </a>
                                <span className="text-slate-600 dark:text-slate-400 text-xs">
                                    {upcomingAppointments.length} appointments scheduled
                                </span>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}







