export interface ProductFilters {
    search?: string;
    category?: string;
    status?: string;
    sort?: string;
    direction?: 'asc' | 'desc';
    page?: number;
}

export interface ProductCategory {
    id: number;
    name: string;
    parent_id: number | null;
    slug: string;
    description?: string;
    is_active: boolean;
}

export interface ProductHsn {
    id: number;
    code: string;
    description: string;
}

export interface ProductUnit {
    id: number;
    name: string;
}

export interface Product {
    id: number;
    name: string;
    sku: string;
    price: number;
    stock: number;
    hsn_id: number;
    unit_id: number;
    categories: ProductCategory[];
    is_active: boolean;
    is_taxable: boolean;
    description?: string;
    created_at: string;
    updated_at: string;
    deleted_at?: string;
}
