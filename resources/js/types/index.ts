export interface SharedData {
    name: string;
    quote: {
        message: string;
        author: string;
    };
    auth: {
        user: any; // Replace 'any' with your User type
    };
    ziggy: any; // Replace 'any' with Ziggy type if needed
    flash: {
        message?: string;
        success?: string;
        error?: string;
    };
    errors: Record<string, string[]>;
    sidebarOpen: boolean;
}
