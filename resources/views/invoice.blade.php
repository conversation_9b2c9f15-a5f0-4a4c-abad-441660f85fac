<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice #{{ $invoice->invoice_number }}</title>
    <style>
        @page {
            margin: 0;
            padding: 0;
        }
        body {
            font-family: 'De<PERSON><PERSON><PERSON> Sans', 'Segoe UI', Roboto, Helvetica, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            background-color: #fff;
        }
        .container {
            width: 100%;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        .header-image-container {
            width: 100%;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        .header-image {
            width: 100%;
            max-height: 120px;
            margin: 0;
            padding: 0;
            display: block;
        }
        .content-wrapper {
            margin-top: 120px; /* Adjust based on your header image height */
            padding: 0 20px 20px 20px;
        }
        /* Add space at top of subsequent pages */
        .page-break + * {
            margin-top: 80px; /* Increased from 40px to 80px for more space */
        }

        /* Add space to all content after a page break */
        .page-break-spacer {
            height: 80px;
            width: 100%;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table.layout {
            margin-bottom: 20px;
        }
        table.items {
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        table.items th {
            background-color: #081e3b;
            color: white;
            text-align: left;
            padding: 12px 15px;
            border: none;
        }
        table.items td {
            padding: 12px 15px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
            vertical-align: middle;
        }
        table.items tr:last-child td {
            border-bottom: none;
        }
        .striped-row:nth-child(even) {
            background-color: #f9fafb;
        }
        .striped-row:nth-child(odd) {
            background-color: #ffffff;
        }
        .striped-row:hover {
            background-color: #f3f4f6;
        }

        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .text-left {
            text-align: left;
        }
        h1, h2, h3, h4 {
            margin: 0;
            color: #111;
        }
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            color: #081e3b;
            margin: 0;
        }
        .invoice-number {
            font-size: 16px;
            font-weight: bold;
            margin: 5px 0;
        }
        .company-logo {
            max-height: 60px;
            max-width: 150px;
            margin-bottom: 10px;
        }
        .info-section {
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f9fafb;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .vendor-details {
            background: linear-gradient(to right, #f9fafb, #f3f4f6);
            border-left: 4px solid #081e3b;
        }
        .vendor-info-row {
            display: flex;
            margin-bottom: 5px;
        }
        .vendor-info-label {
            font-weight: 600;
            width: 100px;
            color: #4b5563;
        }
        .vendor-info-value {
            flex: 1;
        }
        .summary-table {
            width: 100%;
            max-width: 350px;
            margin-left: auto;
            margin-bottom: 20px;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            page-break-inside: avoid; /* Prevent table from breaking across pages */
        }
        .summary-table tr:not(.grand-total) td {
            padding: 10px 15px;
            border-bottom: 1px solid #edf2f7;
        }
        .summary-table tr:nth-child(odd) {
            background-color: #f9fafb;
        }
        .summary-table tr:nth-child(even) {
            background-color: #ffffff;
        }
        /*.summary-table td:first-child {*/
        /*    font-weight: 500;*/
        /*    color: #4b5563;*/
        /*}*/
        .summary-table td:last-child {
            text-align: right;
        }
        .summary-table .grand-total {
            font-weight: bold;
            background-color: #081e3b;
            color: white;
        }
        .summary-table .grand-total td {
            padding: 12px 15px;
        }
        .summary-table .grand-total td:first-child {
            color: white;
        }
        .summary-table .discount-row td:last-child {
            color: #ef4444;
        }
        .badge {
            display: inline-block;
            padding: 4px 10px;
            border-radius: 9999px;
            font-size: 14px;
            font-weight: 500;
        }
        .badge-success {
            background-color: #10b981;
            color: white;
        }
        .badge-secondary {
            background-color: #6b7280;
            color: white;
        }
        .section-title {
            font-size: 16px;
            margin-bottom: 10px;
            font-weight: bold;
            color: #081e3b;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }
        .mb-2 {
            margin-bottom: 10px;
        }
        .mb-5 {
            margin-bottom: 15px;
        }
        .font-medium {
            font-weight: 500;
        }
        .font-bold {
            font-weight: bold;
        }
        .text-sm {
            font-size: 11px;
        }
        .text-gray-600 {
            color: #4b5563;
        }
        .digital-signature {
            height: 60px;
            margin-bottom: 10px;
        }
        .striped-row:nth-child(even) {
            background-color: #f9fafb;
        }
        .page-break {
            page-break-after: always;
        }
        .col-6 {
            width: 50%;
        }
        .col-4 {
            width: 40%;
        }
        .col-8 {
            width: 60%;
        }
        .pt-2 {
            padding-top: 10px;
        }
        .pb-2 {
            padding-bottom: 10px;
        }
        .pl-2 {
            padding-left: 10px;
        }
        .pr-2 {
            padding-right: 10px;
        }
        .vertical-top {
            vertical-align: top;
        }
        .signature-line {
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 0px;
            width: 80%;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Image - Fixed at top -->
        @if($invoice->company->header_print)
            <div class="header-image-container">
                <img src="{{ url('storage/' . $invoice->company->header_print) }}" alt="Company Header" class="header-image">
            </div>
        @endif

        <div class="content-wrapper">
            <!-- Header -->
            <table class="layout mb-5">
                <tr>
                    <td class="col-8 vertical-top">
                        <h2>{{ $invoice->company->company_name }}</h2>
                        <p class="text-sm text-gray-600">{{ $invoice->company->address }}</p>
                        @if($invoice->company->gstin)
                            <p class="text-sm text-gray-600">GSTIN: {{ $invoice->company->gstin }}</p>
                        @endif
                        @if($invoice->company->phone)
                            <p class="text-sm text-gray-600">Phone: {{ $invoice->company->phone }}</p>
                        @endif
                        @if($invoice->company->email)
                            <p class="text-sm text-gray-600">Email: {{ $invoice->company->email }}</p>
                        @endif
                    </td>
                    <td class="col-4 text-right vertical-top">
                        @if($invoice->company->logo)
                            <img src="{{ url('storage/' . $invoice->company->logo) }}" alt="{{ $invoice->company->company_name }}" class="company-logo">
                        @endif
                        <h1 class="invoice-title">INVOICE</h1>
                        <p class="invoice-number">{{ $invoice->invoice_number }}</p>
                        <p class="text-sm text-gray-600">Date: {{ \Carbon\Carbon::parse($invoice->invoice_date)->format('d/m/Y') }}</p>
                        <span class="badge {{ $invoice->payment_status === 'paid' ? 'badge-success' : 'badge-secondary' }}">
                            {{ $invoice->payment_status === 'paid' ? 'Paid' : 'Pending' }}
                        </span>
                    </td>
                </tr>
            </table>

            <!-- Vendor Details - Improved styling -->
            <div class="info-section vendor-details mb-5">
                <h3 class="section-title mb-2">Vendor Details</h3>
                <table class="layout">
                    <tr>
                        <td class="col-6 vertical-top">
                            <p class="font-bold" style="font-size: 14px; margin-bottom: 8px;">{{ $invoice->vendor_company_name }}</p>

                            <div class="vendor-info-row">
                                <span class="vendor-info-label">Contact:</span>
                                <span class="vendor-info-value">{{ $invoice->vendor_contact_person_name }}</span>
                            </div>

                            <div class="vendor-info-row">
                                <span class="vendor-info-label">Phone:</span>
                                <span class="vendor-info-value">{{ $invoice->vendor_contact_person_number }}</span>
                            </div>

                            @if($invoice->vendor_email)
                                <div class="vendor-info-row">
                                    <span class="vendor-info-label">Email:</span>
                                    <span class="vendor-info-value">{{ $invoice->vendor_email }}</span>
                                </div>
                            @endif

                            @if($invoice->vendor_gstin)
                                <div class="vendor-info-row">
                                    <span class="vendor-info-label">GSTIN:</span>
                                    <span class="vendor-info-value">{{ $invoice->vendor_gstin }}</span>
                                </div>
                            @endif

                            @if($invoice->vendor_pan_number)
                                <div class="vendor-info-row">
                                    <span class="vendor-info-label">PAN:</span>
                                    <span class="vendor-info-value">{{ $invoice->vendor_pan_number }}</span>
                                </div>
                            @endif
                        </td>
                        <td class="col-6 vertical-top">
                            <h4 class="font-medium mb-2">Address:</h4>
                            <p style="margin-bottom: 5px;">{{ $invoice->vendor_address }}</p>
                            <p style="margin-bottom: 5px;">
                                {{ $invoice->vendorCity ? $invoice->vendorCity->name . ', ' : '' }}
                                {{ $invoice->vendorState ? $invoice->vendorState->name . ', ' : '' }}
                                {{ $invoice->vendorCountry ? $invoice->vendorCountry->name : '' }}
                            </p>
                            <p>Pincode: {{ $invoice->vendor_pincode }}</p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Invoice Items -->
            <h3 class="section-title mb-2">Invoice Items:</h3>
            <table class="items mb-5">
                <thead>
                    <tr>
                        <th width="25%">Item</th>
                        <th width="15%">SKU</th>
                        <th width="15%">Unit</th>
                        <th width="15%">Quantity</th>
                        <th width="15%">Price</th>
                        <th width="15%">Total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($invoice->items as $index => $item)
                        <tr class="striped-row">
                            <td><span class="item-name">{{ $item->product->name }}</span></td>
                            <td><span class="item-sku">{{ $item->product->sku }}</span></td>
                            <td>{{ $item->product->unit->name }}</td>
                            <td>{{ $item->quantity }}</td>
                            <td>₹{{ number_format($item->price, 2) }}</td>
                            <td><strong>₹{{ number_format($item->total, 2) }}</strong></td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

            <!-- Add a page break if needed -->
            @if(count($invoice->items) > 10)
                <div class="page-break"></div>
                <div class="page-break-spacer"></div>
            @endif

            <!-- Invoice Summary -->
            <table class="layout" >
                <tr>
                    <td class="col-6"></td>
                    <td class="col-6">
                        <table class="summary-table" style="margin-top: 20px;">
                            <tr>
                                <td>Subtotal:</td>
                                <td>₹{{ number_format($invoice->subtotal, 2) }}</td>
                            </tr>
                            @if($invoice->is_gst_applicable)
                                <tr>
                                    <td>GST Applicable Amount:</td>
                                    <td>₹{{ number_format($invoice->gst_applicable_amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td>GST Amount (18%):</td>
                                    <td>₹{{ number_format($invoice->gst_amount, 2) }}</td>
                                </tr>
                            @endif
                            @if($invoice->discount > 0)
                                <tr class="discount-row">
                                    <td>Discount:</td>
                                    <td>-₹{{ number_format($invoice->discount, 2) }}</td>
                                </tr>
                            @endif
                            <tr>
                                <td>Without GST Amount:</td>
                                <td>₹{{ number_format($invoice->without_gst_amount, 2) }}</td>
                            </tr>
                            <tr>
                                <td>With GST Amount:</td>
                                <td>₹{{ number_format($invoice->with_gst_amount, 2) }}</td>
                            </tr>
                            <tr class="grand-total">
                                <td>Grand Total:</td>
                                <td>₹{{ number_format($invoice->grand_total, 2) }}</td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>

            <!-- Payment Method -->
            <div class="info-section mb-5" style="background: linear-gradient(to right, #f8fafc, #f1f5f9); border-left: 4px solid #0f172a; box-shadow: 0 2px 5px rgba(0,0,0,0.08);">
                <h3 class="section-title mb-2" style="color: #0f172a; border-bottom: 1px solid #cbd5e1;">Payment Information:</h3>
                <table class="layout">
                    <tr>
                        <td class="col-6">
                            <div style="display: flex; align-items: center;">
                                <span class="font-medium" style="min-width: 130px; color: #334155;">Payment Method:</span>
                                <span style="font-weight: 500; color: #1e293b; padding: 4px 8px; background-color: #f8fafc; border-radius: 4px; border: 1px solid #e2e8f0;">
                                    {{ $invoice->payment_method === 'cash' ? 'Cash' :
                                    ($invoice->payment_method === 'cheque' ? 'Cheque' :
                                    ($invoice->payment_method === 'bank_transfer' ? 'Bank Transfer' : $invoice->payment_method)) }}
                                </span>
                            </div>
                        </td>
                        <td class="col-6">
                            <div style="display: flex; align-items: center;">
                                <span class="font-medium" style="min-width: 130px; color: #334155;">Payment Status:</span>
                                <span style="font-weight: 500; padding: 4px 8px; border-radius: 4px;
                                    {{ $invoice->payment_status === 'paid' ? 'background-color: #dcfce7; color: #166534; border: 1px solid #bbf7d0;' :
                                    'background-color: #fef9c3; color: #854d0e; border: 1px solid #fde68a;' }}">
                                    {{ $invoice->payment_status === 'paid' ? 'Paid' : 'Pending' }}
                                </span>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Remarks -->
            @if($invoice->remarks)
                <div class="info-section mb-5">
                    <h3 class="section-title mb-2">Remarks:</h3>
                    <p>{{ $invoice->remarks }}</p>
                </div>
            @endif

            <!-- Add a page break if needed for payment info -->
            @if(count($invoice->items) > 10)
                <!-- Page break already added above -->
            @else
                @if(strlen($invoice->remarks) > 200)
                    <div class="page-break"></div>
                    <div class="page-break-spacer"></div>
                @endif
            @endif

            <!-- Footer - Only Company Signature -->
            <table class="layout" style="margin-top: 40px;">
                <tr>
                    <td class="col-6"></td>
                    <td class="col-6 text-right vertical-top">
                        <p class="font-medium">Authorised Signatory</p>
                        @if($invoice->company->digital_signature)
                            <img src="{{ url('storage/' . $invoice->company->digital_signature) }}" alt="Digital Signature" class="digital-signature" style="margin-left: auto;">
                        @endif
                        <div class="signature-line" style="margin-left: auto;">
                            <p>{{ $invoice->company->company_name }}</p>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</body>
</html>
