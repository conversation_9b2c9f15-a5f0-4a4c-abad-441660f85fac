<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice #{{ $invoice->invoice_number }}</title>
    <style>
        @page {
            margin: 0;
            padding: 0;
        }
        body, p {
            font-family: 'Deja<PERSON>u Sans', 'Segoe UI', Roboto, Helvetica, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 10px;
            line-height: 1;
            color: #333;
        }
        p {
            margin-bottom: 5px;
        }
        .container {
            width: 100%;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .content-wrapper {
            margin: 20px;
        }
        /* Add space at top of subsequent pages */
        .page-break + * {
            margin-top: 80px; /* Increased from 40px to 80px for more space */
        }

        /* Add space to all content after a page break */
        .page-break-spacer {
            height: 80px;
            width: 100%;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table.layout {
            margin-bottom: 20px;
        }
        table.items {
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }
        table.items th {
            text-align: left;
            padding: 10px 15px;
            border: none;
        }
        table.items td {
            padding: 10px 15px;
            border-bottom: 1px solid #e5e7eb;
            vertical-align: middle;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .text-left {
            text-align: left;
        }
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            color: #081e3b;
            margin: 0;
        }
        .invoice-number {
            font-size: 16px;
            font-weight: bold;
            margin: 5px 0;
        }
        .company-logo {
            max-height: 60px;
            margin-bottom: 10px;
        }
        .info-section {
            margin-bottom: 20px;
        }
        .vendor-info-row {
            display: flex;
            margin-bottom: 5px;
        }
        .vendor-info-label {
            font-weight: 600;
            width: 100px;
            color: #4b5563;
        }
        .vendor-info-value {
            flex: 1;
        }
        .summary-table {
            width: 100%;
            max-width: 350px;
            margin-left: auto;
            margin-bottom: 20px;
            border-collapse: separate;
            border-spacing: 0;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            page-break-inside: avoid;
        }
        .summary-table tr:not(.grand-total) td {
            padding: 10px 15px;
            border-bottom: 1px solid #edf2f7;
        }
        .summary-table td:last-child {
            text-align: right;
        }
        .summary-table .grand-total {
            font-weight: bold;
        }
        .summary-table .grand-total td {
            padding: 12px 15px;
        }
        .summary-table .discount-row td:last-child {
            color: #ef4444;
        }
        .section-title {
            font-size: 16px;
            margin-bottom: 10px;
            font-weight: bold;
            color: #081e3b;
        }
        .mb-2 {
            margin-bottom: 10px;
        }
        .mb-5 {
            margin-bottom: 15px;
        }
        .font-medium {
            font-weight: 500;
        }
        .text-sm {
            font-size: 11px;
        }
        .text-gray-600 {
            color: #4b5563;
        }
        .digital-signature {
            height: 60px;
            margin-bottom: 10px;
        }
        .striped-row:nth-child(even) {
            background-color: #f9fafb;
        }
        .page-break {
            page-break-after: always;
        }
        .col-6 {
            width: 50%;
        }
        .col-4 {
            width: 40%;
        }
        .col-3 {
            width: 33.33%;
        }
        .col-8 {
            width: 60%;
        }
        .col-9 {
            width: 66.66%;
        }
        .vertical-top {
            vertical-align: top;
        }
        .signature-line {
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 15px;
            width: 80%;
        }
        hr {
            border: none;
            border-top: 1px solid #e5e7eb;
            margin: 20px 0;
        }
        .small-heading {
            font-size: 12px;
            line-height: 1;
            font-weight: bold;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content-wrapper">
            <!-- Header -->
            <table class="layout mb-5">
                <tr>
                    <td class="col-8 vertical-top">
                        @if($invoice->company->logo)
                            <img src="{{ url('storage/' . $invoice->company->logo) }}" alt="{{ $invoice->company->company_name }}" class="company-logo">
                        @endif
                        <h2>{{ $invoice->company->company_name }}</h2>
                        @if($invoice->company->company_address)
                            <p class="text-sm text-gray-600">{{ $invoice->company->company_address }}</p>
                        @endif
                        @if($invoice->company->contact_no)
                            <p class="text-sm text-gray-600">{{ $invoice->company->contact_no }}</p>
                        @endif
                        @if($invoice->company->gstin)
                            <p class="text-sm text-gray-600">GSTIN: {{ $invoice->company->gstin }}</p>
                        @endif
                        @if($invoice->company->pan_number)
                            <p class="text-sm text-gray-600">PAN NO: {{ $invoice->company->pan_number }}</p>
                        @endif
                    </td>
                    <td class="col-4 text-right vertical-top">

                        <h1 class="invoice-title">INVOICE</h1>
                        <p class="invoice-number">{{ $invoice->invoice_number }}</p>
                        <p><b>Bill Date:</b> {{ \Carbon\Carbon::parse($invoice->invoice_date)->format('d/m/Y') }}</p>

                    </td>
                </tr>
            </table>
            <hr>
            <!-- Vendor Details - Improved styling -->
            <div class="info-section vendor-details mb-5">
                <table class="layout">
                    <tr>
                        <td class="col-6 vertical-top">
                            <p class="small-heading">{{ $invoice->vendor_company_name }}</p>

                            <div class="vendor-info-row">
                                <span class="vendor-info-value">{{ $invoice->vendor_contact_person_name }}</span>
                            </div>
                            <div class="vendor-info-row">
                            <p style="margin-bottom: 5px;">{{ $invoice->vendor_address }}</p>
                            <p style="margin-bottom: 5px;">
                                {{ $invoice->vendorCity ? $invoice->vendorCity->name . ', ' : '' }}
                                {{ $invoice->vendorState ? $invoice->vendorState->name . ', ' : '' }}
                                {{ $invoice->vendorCountry ? $invoice->vendorCountry->name : '' }} - {{ $invoice->vendor_pincode }}
                            </p>
                            </div>

                            <div class="vendor-info-row">
                                <span class="vendor-info-value">{{ $invoice->vendor_contact_person_number }}</span>
                            </div>

                            @if($invoice->vendor_email)
                                <div class="vendor-info-row">
                                    <span class="vendor-info-value">{{ $invoice->vendor_email }}</span>
                                </div>
                            @endif

                            @if($invoice->vendor_gstin)
                                <div class="vendor-info-row">
                                    <span class="vendor-info-label">GSTIN:</span>
                                    <span class="vendor-info-value">{{ $invoice->vendor_gstin }}</span>
                                </div>
                            @endif

                            @if($invoice->vendor_pan_number)
                                <div class="vendor-info-row">
                                    <span class="vendor-info-label">PAN:</span>
                                    <span class="vendor-info-value">{{ $invoice->vendor_pan_number }}</span>
                                </div>
                            @endif
                        </td>
                        <td class="col-6 vertical-top">
                            <p><b>Payment Status</b></p>
                            <p>{{ $invoice->payment_status === 'paid' ? 'Paid' : 'Pending' }}</p>
                            @if($invoice->payment_status === 'paid')
                            <p><b>Payment Method</b></p>
                            <p>{{ $invoice->payment_method === 'cash' ? 'Cash' :
                                ($invoice->payment_method === 'cheque' ? 'Cheque' :
                                ($invoice->payment_method === 'bank_transfer' ? 'Bank Transfer' : $invoice->payment_method)) }}
                            </p>
                            @endif
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Invoice Items -->
            <table class="items mb-5" style="border-collapse: collapse; border: 1px solid #ddd;">
                <thead>
                    <tr>
                        <th width="25%" style="border: 1px solid #ddd;">Item</th>
                        <th width="15%" style="border: 1px solid #ddd;">SKU</th>
                        <th width="15%" style="border: 1px solid #ddd;">Unit</th>
                        <th width="15%" style="border: 1px solid #ddd;">Quantity</th>
                        <th width="15%" style="border: 1px solid #ddd;">Price</th>
                        <th width="15%" style="border: 1px solid #ddd;">Total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($invoice->items as $index => $item)
                        <tr class="striped-row">
                            <td style="border: 1px solid #ddd;"><span class="item-name">{{ $item->product->name }}</span></td>
                            <td style="border: 1px solid #ddd;"><span class="item-sku">{{ $item->product->sku }}</span></td>
                            <td style="border: 1px solid #ddd;">{{ $item->product->unit->name }}</td>
                            <td style="border: 1px solid #ddd;">{{ $item->quantity }}</td>
                            <td style="border: 1px solid #ddd;">₹{{ number_format($item->price, 2) }}</td>
                            <td style="border: 1px solid #ddd;"><strong>₹{{ number_format($item->total, 2) }}</strong></td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

            <!-- Add a page break if needed -->
            @if(count($invoice->items) > 10)
                <div class="page-break"></div>
                <div class="page-break-spacer"></div>
            @endif

            <!-- Invoice Summary -->
            <table class="layout" >
                <tr>
                    <td class="col-9"></td>
                    <td class="col-3">
                        <table class="summary-table">
                            <tr>
                                <td>Subtotal:</td>
                                <td>₹{{ number_format($invoice->subtotal, 2) }}</td>
                            </tr>
                            @if($invoice->is_gst_applicable)
                                <tr>
                                    <td>GST Applicable Amount:</td>
                                    <td>₹{{ number_format($invoice->gst_applicable_amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td>GST Amount (18%):</td>
                                    <td>₹{{ number_format($invoice->gst_amount, 2) }}</td>
                                </tr>
                            @endif
                            @if($invoice->discount > 0)
                                <tr class="discount-row">
                                    <td>Discount:</td>
                                    <td>-₹{{ number_format($invoice->discount, 2) }}</td>
                                </tr>
                            @endif
                            <tr>
                                <td>Without GST Amount:</td>
                                <td>₹{{ number_format($invoice->without_gst_amount, 2) }}</td>
                            </tr>
                            <tr>
                                <td>With GST Amount:</td>
                                <td>₹{{ number_format($invoice->with_gst_amount, 2) }}</td>
                            </tr>
                            <tr >
                                <td><b>Grand Total:</b></td>
                                <td><b>₹{{ number_format($invoice->grand_total, 2) }}</b></td>

                            </tr>
                        </table>
                    </td>
                </tr>
            </table>

            <!-- Remarks -->
            @if($invoice->remarks)
                <div class="info-section mb-5">
                    <h3 class="section-title mb-2" style="color: #334155;">Remarks:</h3>
                    <p style="margin: 0; line-height: 1; color: #475569; font-style: italic;">{{ $invoice->remarks }}</p>
                </div>
            @endif

            <!-- Add a page break if needed for payment info -->
            @if(count($invoice->items) > 10)
                <!-- Page break already added above -->
            @else
                @if(strlen($invoice->remarks) > 200)
                    <div class="page-break"></div>
                    <div class="page-break-spacer"></div>
                @endif
            @endif

            <!-- Terms and Conditions -->
            @if($invoice->termConditions->count() > 0)
                <div class="info-section mb-5">
                    <h3 class="section-title mb-2" style="color: #334155;">Terms & Conditions:</h3>
                    <ol style="margin: 0; padding-left: 20px;">
                        @foreach($invoice->termConditions as $termCondition)
                            <li style="margin-bottom: 8px; color: #475569;">
                                <span style="font-weight: 600;">{{ $termCondition->title }}:</span>
                                {{ $termCondition->condition }}
                            </li>
                        @endforeach
                    </ol>
                </div>
            @endif

            <!-- Footer - Only Company Signature -->
            <table class="layout" style="margin-top: 40px;">
                <tr>
                    <td class="col-6"></td>
                    <td class="col-6 text-right vertical-top">
                        <p class="font-medium" style="color: #334155; font-size: 15px; margin-bottom: 8px;">Authorised Signatory</p>
                        @if($invoice->company->digital_signature)
                            <div style="background-color: #f8fafc;  padding: 10px; display: inline-block; margin-left: auto;">
                                <img src="{{ url('storage/' . $invoice->company->digital_signature) }}" alt="Digital Signature" class="digital-signature" style="margin-left: auto; max-height: 60px;">
                            </div>
                        @endif
                        <div class="signature-line" style="margin-left: auto; ">
                            <p style="font-weight: 500; color: #1e293b;">{{ $invoice->company->company_name }}</p>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</body>
</html>
