import { defineConfig } from 'electron-vite';
import { resolve } from 'path';

export default defineConfig({
  main: {
    entry: 'src/main/index.js', // Your main process entry point
    vite: {
      build: {
        outDir: 'dist/electron/main',
      },
      resolve: {
        alias: {
          '@': resolve(__dirname, 'src/main'),
        },
      },
    },
  },
  preload: {
    entry: 'src/preload/index.js', // Your preload script entry point
    vite: {
      build: {
        outDir: 'dist/electron/preload',
      },
      resolve: {
        alias: {
          '@': resolve(__dirname, 'src/preload'),
        },
      },
    },
  },
  renderer: {
    root: 'src/renderer', // Root directory of your renderer source
    entry: 'src/renderer/index.html', // Renderer entry point (HTML file)
    vite: {
      build: {
        outDir: 'dist/electron/renderer',
      },
      resolve: {
        alias: {
          '@': resolve(__dirname, 'src/renderer/src'), // Alias for your renderer source files
        },
      },
    },
  },
});