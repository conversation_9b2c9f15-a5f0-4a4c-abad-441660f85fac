parameters:
	ignoreErrors:
		-
			message: '#^Cannot access property \$email on App\\Models\\User\|null\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Http/Controllers/Auth/ConfirmablePasswordController.php

		-
			message: '#^Cannot call method hasVerifiedEmail\(\) on App\\Models\\User\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Http/Controllers/Auth/EmailVerificationNotificationController.php

		-
			message: '#^Cannot call method sendEmailVerificationNotification\(\) on App\\Models\\User\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Http/Controllers/Auth/EmailVerificationNotificationController.php

		-
			message: '#^Cannot call method hasVerifiedEmail\(\) on App\\Models\\User\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Http/Controllers/Auth/EmailVerificationPromptController.php

		-
			message: '#^Parameter \#1 \$key of function __ expects string\|null, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/Http/Controllers/Auth/NewPasswordController.php

		-
			message: '#^Parameter \#1 \$value of static method Illuminate\\Support\\Facades\\Hash\:\:make\(\) expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/Http/Controllers/Auth/NewPasswordController.php

		-
			message: '#^Parameter \#1 \$value of static method Illuminate\\Support\\Facades\\Hash\:\:make\(\) expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/Http/Controllers/Auth/RegisteredUserController.php

		-
			message: '#^Cannot call method hasVerifiedEmail\(\) on App\\Models\\User\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Http/Controllers/Auth/VerifyEmailController.php

		-
			message: '#^Cannot call method markEmailAsVerified\(\) on App\\Models\\User\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Http/Controllers/Auth/VerifyEmailController.php

		-
			message: '#^Cannot access offset ''password'' on mixed\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 1
			path: app/Http/Controllers/Settings/PasswordController.php

		-
			message: '#^Cannot call method update\(\) on App\\Models\\User\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Http/Controllers/Settings/PasswordController.php

		-
			message: '#^Instanceof between App\\Models\\User\|null and Illuminate\\Contracts\\Auth\\MustVerifyEmail will always evaluate to false\.$#'
			identifier: instanceof.alwaysFalse
			count: 1
			path: app/Http/Controllers/Settings/PasswordController.php

		-
			message: '#^Parameter \#1 \$value of static method Illuminate\\Support\\Facades\\Hash\:\:make\(\) expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/Http/Controllers/Settings/PasswordController.php

		-
			message: '#^Cannot access property \$email_verified_at on App\\Models\\User\|null\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Http/Controllers/Settings/ProfileController.php

		-
			message: '#^Cannot call method delete\(\) on App\\Models\\User\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Http/Controllers/Settings/ProfileController.php

		-
			message: '#^Cannot call method fill\(\) on App\\Models\\User\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Http/Controllers/Settings/ProfileController.php

		-
			message: '#^Cannot call method isDirty\(\) on App\\Models\\User\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Http/Controllers/Settings/ProfileController.php

		-
			message: '#^Cannot call method save\(\) on App\\Models\\User\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Http/Controllers/Settings/ProfileController.php

		-
			message: '#^Instanceof between App\\Models\\User\|null and Illuminate\\Contracts\\Auth\\MustVerifyEmail will always evaluate to false\.$#'
			identifier: instanceof.alwaysFalse
			count: 1
			path: app/Http/Controllers/Settings/ProfileController.php

		-
			message: '#^Method App\\Http\\Middleware\\HandleInertiaRequests\:\:share\(\) should return array\<string, mixed\> but returns array\.$#'
			identifier: return.type
			count: 1
			path: app/Http/Middleware/HandleInertiaRequests.php

		-
			message: '#^Parameter \#1 \$string of function mb_trim expects string, mixed given\.$#'
			identifier: argument.type
			count: 2
			path: app/Http/Middleware/HandleInertiaRequests.php

		-
			message: '#^Parameter \#1 \$string of function str expects string\|null, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/Http/Middleware/HandleInertiaRequests.php

		-
			message: '#^Parameter \#1 \$value of static method Illuminate\\Support\\Str\:\:lower\(\) expects string, Illuminate\\Support\\Stringable given\.$#'
			identifier: argument.type
			count: 1
			path: app/Http/Requests/Auth/LoginRequest.php

		-
			message: '#^Cannot access property \$id on App\\Models\\User\|null\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Http/Requests/Settings/ProfileUpdateRequest.php