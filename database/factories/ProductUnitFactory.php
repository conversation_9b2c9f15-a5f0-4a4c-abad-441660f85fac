<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\ProductUnit;
use Illuminate\Database\Eloquent\Factories\Factory;

final class ProductUnitFactory extends Factory
{
    protected $model = ProductUnit::class;

    public function definition(): array
    {
        return [
            'name'        => $this->faker->unique()->word(),
            'short_name'  => $this->faker->unique()->lexify('???'),
            'description' => $this->faker->sentence(),
        ];
    }
}
