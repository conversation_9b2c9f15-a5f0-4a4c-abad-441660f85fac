<?php

declare(strict_types=1);

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

final class ProductHsnFactory extends Factory
{
    public function definition(): array
    {
        return [
            'code'        => fake()->unique()->numerify('####'),
            'description' => fake()->sentence(),
            'gst_rate'    => fake()->randomFloat(2, 5, 28),
        ];
    }
}
