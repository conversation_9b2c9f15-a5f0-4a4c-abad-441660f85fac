<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Product;
use App\Models\ProductHsn;
use App\Models\ProductUnit;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

final class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition(): array
    {
        $name = fake()->words(3, true);

        return [
            'name'        => ucwords($name),
            'sku'         => mb_strtoupper(Str::random(8)),
            'price'       => fake()->randomFloat(2, 10, 1000),
            'stock'       => fake()->numberBetween(0, 100),
            'hsn_id'      => ProductHsn::inRandomOrder()->first()->id,
            'unit_id'     => ProductUnit::inRandomOrder()->first()->id,
            'is_active'   => fake()->boolean(80), // 80% chance of being active
            'is_taxable'  => fake()->boolean(90), // 90% chance of being taxable
            'description' => fake()->paragraph(),
        ];
    }
}
