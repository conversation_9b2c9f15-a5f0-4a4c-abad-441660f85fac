<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Branch;
use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class TenantDatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds for tenant databases.
     */
    public function run(): void
    {
        // This seeder is now handled by the SetupTenantDatabase job
        // to avoid cache-related issues during tenant creation
    }
}
