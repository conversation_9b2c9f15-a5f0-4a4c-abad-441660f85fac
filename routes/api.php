<?php

declare(strict_types=1);

use App\Http\Controllers\Api\AppointmentController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\SeatController;
use App\Http\Controllers\Api\ServiceController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::post('/login', [AuthController::class, 'login']);

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Public seat map - accessible without authentication
Route::get('/seats/map', [SeatController::class, 'map']);

// Appointment APIs
Route::middleware(['auth:sanctum'])->group(function () {

    // Service APIs
    Route::get('/services', [ServiceController::class, 'index']);

    // Appointments - accessible by all authenticated users
    Route::get('/appointments/my', [AppointmentController::class, 'myAppointments']);
    Route::post('/appointments', [AppointmentController::class, 'store']);

    // Admin and Staff only routes
    Route::middleware(['role:admin|staff'])->group(function () {

        Route::get('/seats', [SeatController::class, 'index']);
        Route::put('/seats/{seat}/status', [SeatController::class, 'updateStatus']);

        Route::get('/appointments/today', [AppointmentController::class, 'todayAppointments']);
        Route::get('/appointments/upcoming', [AppointmentController::class, 'upcomingAppointments']);
        Route::put('/appointments/{appointment}/status', [AppointmentController::class, 'updateStatus']);
    });
});
