<?php

declare(strict_types=1);

use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

// Include auth routes first to ensure they're registered for central domain


require __DIR__.'/auth.php';


// Apply central domain middleware to all routes in this file
Route::middleware(['central_domain'])->group(function () {

    Route::middleware(['auth'])->group(function () {

        // Central domain root - redirect based on user role
        Route::get('/', function () {
            $user = auth()->user();

            if ($user->hasRole('admin')) {
                return redirect()->route('admin.dashboard');
            } elseif ($user->hasRole('vendor')) {
                // Redirect vendor to their tenant domain
                if ($user->company_domain) {
                    return redirect()->to("http://{$user->company_domain}.salon.test/dashboard");
                }
                return redirect()->route('vendors.index')->with('error', 'No tenant domain found.');
            } else {
                // For other roles, show a basic dashboard or redirect
                return redirect()->route('vendors.index');
            }
        })->name('central.home');

        Route::post('/user/update-branch', [UserController::class, 'updateCurrentBranch'])->name('user.update-branch');

        // Admin Dashboard and Management Routes (Admin only)
        Route::middleware(['role:admin'])->prefix('admin')->name('admin.')->group(function () {

            // Admin Dashboard
            Route::get('/dashboard', [\App\Http\Controllers\AdminDashboardController::class, 'index'])->name('dashboard');

            // Subscription Plans Management
            Route::prefix('subscription-plans')->name('subscription-plans.')->group(function () {
                Route::get('/', [\App\Http\Controllers\SubscriptionPlanController::class, 'index'])->name('index');
                Route::get('/create', [\App\Http\Controllers\SubscriptionPlanController::class, 'create'])->name('create');
                Route::post('/', [\App\Http\Controllers\SubscriptionPlanController::class, 'store'])->name('store');
                Route::get('/{subscriptionPlan}', [\App\Http\Controllers\SubscriptionPlanController::class, 'show'])->name('show');
                Route::get('/{subscriptionPlan}/edit', [\App\Http\Controllers\SubscriptionPlanController::class, 'edit'])->name('edit');
                Route::put('/{subscriptionPlan}', [\App\Http\Controllers\SubscriptionPlanController::class, 'update'])->name('update');
                Route::delete('/{subscriptionPlan}', [\App\Http\Controllers\SubscriptionPlanController::class, 'destroy'])->name('destroy');
            });

            // Vendor Subscriptions Management
            Route::prefix('vendor-subscriptions')->name('vendor-subscriptions.')->group(function () {
                Route::get('/', [\App\Http\Controllers\VendorSubscriptionController::class, 'index'])->name('index');
                Route::get('/create', [\App\Http\Controllers\VendorSubscriptionController::class, 'create'])->name('create');
                Route::post('/', [\App\Http\Controllers\VendorSubscriptionController::class, 'store'])->name('store');
                Route::get('/{vendorSubscription}', [\App\Http\Controllers\VendorSubscriptionController::class, 'show'])->name('show');
                Route::get('/{vendorSubscription}/edit', [\App\Http\Controllers\VendorSubscriptionController::class, 'edit'])->name('edit');
                Route::put('/{vendorSubscription}', [\App\Http\Controllers\VendorSubscriptionController::class, 'update'])->name('update');
                Route::delete('/{vendorSubscription}', [\App\Http\Controllers\VendorSubscriptionController::class, 'destroy'])->name('destroy');
                Route::post('/{vendorSubscription}/extend', [\App\Http\Controllers\VendorSubscriptionController::class, 'extend'])->name('extend');
            });
        });

        // Vendor Management Routes (Admin only)
        Route::prefix('vendors')->name('vendors.')->group(function () {
            Route::get('/', [\App\Http\Controllers\VendorManagementController::class, 'index'])->name('index');
            Route::get('/{vendor}', [\App\Http\Controllers\VendorManagementController::class, 'show'])->name('show');
            Route::delete('/{vendor}', [\App\Http\Controllers\VendorManagementController::class, 'destroy'])->name('destroy');
        });

    });



});
