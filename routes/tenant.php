<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\DashboardController;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
    \App\Http\Middleware\SetupTenantContext::class,
])->group(function () {


    // CSRF token route for tenant domains
    Route::get('/csrf-token', function () {
        return response()->json([
            'csrf_token' => csrf_token(),
        ]);
    });


    // Redirect root to dashboard
    Route::get('/', function () {
        return redirect()->route('tenant.dashboard');
    })->name('home');



    Route::middleware('guest')->group(function () {



//        Route::get('/login', [\App\Http\Controllers\Tenant\AuthController::class, 'create'])
//            ->name('login');
//        Route::post('/login', [\App\Http\Controllers\Tenant\AuthController::class, 'store']);

    });

    Route::middleware(['auth','tenant.user'])->group(function () {


        Route::get('/dashboard', [DashboardController::class, 'index'])->name('tenant.dashboard');

        // Branch Management Routes
        Route::prefix('branches')->name('branches.')->group(function () {
            Route::get('/', [BranchController::class, 'index'])->name('index');
            Route::get('/create', [BranchController::class, 'create'])->name('create');
            Route::post('/', [BranchController::class, 'store'])->name('store')->middleware('subscription.limit:add_branch');
            Route::get('/{branch}/edit', [BranchController::class, 'edit'])->name('edit');
            Route::put('/{branch}', [BranchController::class, 'update'])->name('update');
            Route::delete('/{branch}', [BranchController::class, 'destroy'])->name('destroy');
            Route::post('/user/update-branch', [UserController::class, 'updateCurrentBranch'])->name('user.update-branch');

            // Trashed branches routes
            Route::get('/trashed', [BranchController::class, 'trashed'])->name('trashed');
            Route::put('/{id}/restore', [BranchController::class, 'restore'])->name('restore');
            // Route::delete('/{id}/force-delete', [BranchController::class, 'forceDelete'])->name('force-delete');
        });

        // Appointment Management Routes
        Route::prefix('appointments')->name('appointments.')->group(function () {
            Route::get('/', [\App\Http\Controllers\AppointmentController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\AppointmentController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\AppointmentController::class, 'store'])->name('store')->middleware('subscription.limit:add_appointment');
            Route::get('/{appointment}/edit', [\App\Http\Controllers\AppointmentController::class, 'edit'])->name('edit');
            Route::put('/{appointment}', [\App\Http\Controllers\AppointmentController::class, 'update'])->name('update');
            Route::delete('/{appointment}', [\App\Http\Controllers\AppointmentController::class, 'destroy'])->name('destroy');
            Route::put('/{appointment}/update-status', [\App\Http\Controllers\AppointmentController::class, 'updateStatus'])->name('update-status');
        });

        // Seat Management Routes
        Route::prefix('seats')->name('seats.')->group(function () {
            Route::get('/', [\App\Http\Controllers\SeatController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\SeatController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\SeatController::class, 'store'])->name('store')->middleware('subscription.limit:add_seat');
            Route::get('/{seat}/edit', [\App\Http\Controllers\SeatController::class, 'edit'])->name('edit');
            Route::put('/{seat}', [\App\Http\Controllers\SeatController::class, 'update'])->name('update');
            Route::delete('/{seat}', [\App\Http\Controllers\SeatController::class, 'destroy'])->name('destroy');
            Route::put('/{seat}/update-status', [\App\Http\Controllers\SeatController::class, 'updateStatus'])->name('update-status');
        });

        // Service Management Routes
        Route::prefix('services')->name('services.')->group(function () {
            Route::get('/', [\App\Http\Controllers\ServiceController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\ServiceController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\ServiceController::class, 'store'])->name('store')->middleware('subscription.limit:add_service');
            Route::get('/{service}/edit', [\App\Http\Controllers\ServiceController::class, 'edit'])->name('edit');
            Route::put('/{service}', [\App\Http\Controllers\ServiceController::class, 'update'])->name('update');
            Route::delete('/{service}', [\App\Http\Controllers\ServiceController::class, 'destroy'])->name('destroy');
        });

        // Working Hours Routes
        Route::prefix('working-hours')->name('working-hours.')->group(function () {
            Route::get('/', [\App\Http\Controllers\WorkingHourController::class, 'index'])->name('index');
            Route::get('/edit', [\App\Http\Controllers\WorkingHourController::class, 'edit'])->name('edit');
            Route::put('/', [\App\Http\Controllers\WorkingHourController::class, 'update'])->name('update');
        });

        // Term & Condition routes
        Route::prefix('terms-conditions')->name('terms-conditions.')->group(function () {
            Route::get('/', [\App\Http\Controllers\TermConditionController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\TermConditionController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\TermConditionController::class, 'store'])->name('store');
            Route::get('/{termCondition}/edit', [\App\Http\Controllers\TermConditionController::class, 'edit'])->name('edit');
            Route::put('/{termCondition}', [\App\Http\Controllers\TermConditionController::class, 'update'])->name('update');
            Route::delete('/{termCondition}', [\App\Http\Controllers\TermConditionController::class, 'destroy'])->name('destroy');
            Route::get('/trashed', [\App\Http\Controllers\TermConditionController::class, 'trashed'])->name('trashed');
            Route::post('/{id}/restore', [\App\Http\Controllers\TermConditionController::class, 'restore'])->name('restore');
        });

        // Staff Management Routes
        Route::prefix('staff')->name('staff.')->group(function () {
            Route::get('/', [\App\Http\Controllers\StaffController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\StaffController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\StaffController::class, 'store'])->name('store')->middleware('subscription.limit:add_staff');
            Route::get('/{user}', [\App\Http\Controllers\StaffController::class, 'show'])->name('show');
            Route::get('/{user}/edit', [\App\Http\Controllers\StaffController::class, 'edit'])->name('edit');
            Route::put('/{user}', [\App\Http\Controllers\StaffController::class, 'update'])->name('update');
            Route::delete('/{user}', [\App\Http\Controllers\StaffController::class, 'destroy'])->name('destroy');
        });

        Route::post('/user/update-branch', [\App\Http\Controllers\UserController::class, 'updateCurrentBranch'])->name('user.update-branch');

        require __DIR__.'/settings.php';

    });
});
